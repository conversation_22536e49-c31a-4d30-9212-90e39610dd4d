#!/usr/bin/env python3
"""
Bank Adviser Tracker Manager
Handles all tracker table operations including duplicate checker
"""

import os
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional

class BankAdviserTrackerManager:
    """Manages Bank Adviser tracker tables with sorting, filtering, and management capabilities"""

    def __init__(self, debug: bool = False):
        self.debug = debug

        # Define tracker tables and their configurations
        self.tracker_tables = {
            'in_house_loans': {
                'table_name': 'in_house_loans',
                'type_column': 'loan_type',
                'amount_column': 'loan_amount'
            },
            'external_loans': {
                'table_name': 'external_loans',
                'type_column': 'loan_type',
                'amount_column': 'loan_amount'
            },
            'leave_claims': {
                'table_name': 'leave_claims',
                'type_column': 'allowance_type',
                'amount_column': 'payable_amount'
            },
            'educational_subsidy': {
                'table_name': 'educational_subsidy',
                'type_column': 'allowance_type',
                'amount_column': 'payable_amount'
            },
            'long_service_awards': {
                'table_name': 'long_service_awards',
                'type_column': 'award_type',
                'amount_column': 'payable_amount'
            },
            'motor_vehicle_maintenance': {
                'table_name': 'motor_vehicle_maintenance',
                'type_column': 'allowance_type',
                'amount_column': 'payable_amount'
            },
            'duplicate_checker': {
                'table_name': 'duplicate_checker',
                'type_column': 'claim_type',
                'amount_column': 'payable_amount'
            }
        }

        if self.debug:
            print(f"[TRACKER-MANAGER] Initialized with {len(self.tracker_tables)} tracker tables")

    def get_tracker_data(self, category: str, filters: Dict = None) -> Dict:
        """Get tracker data with optional filtering and sorting"""
        try:
            from core.python_database_manager import get_database_instance
            db = get_database_instance()

            if category not in self.tracker_tables:
                return {'success': False, 'error': f'Unknown category: {category}'}

            table_config = self.tracker_tables[category]
            table_name = table_config['table_name']

            # Build query with filters
            query = f"SELECT * FROM {table_name}"
            params = []
            conditions = []

            if filters:
                if filters.get('year'):
                    conditions.append("period_year = ?")
                    params.append(filters['year'])

                if filters.get('month'):
                    conditions.append("period_month = ?")
                    params.append(filters['month'])

                if filters.get('employee_search'):
                    conditions.append("(employee_no LIKE ? OR employee_name LIKE ?)")
                    search_term = f"%{filters['employee_search']}%"
                    params.extend([search_term, search_term])

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            # Add ordering
            if category == 'duplicate_checker':
                query += " ORDER BY detection_date DESC"
            else:
                query += " ORDER BY period_year DESC, period_month DESC, employee_no ASC"

            data = db.execute_query(query, tuple(params) if params else ())

            # Convert to list of dictionaries (data is already in dict format from python_database_manager)
            if data:
                result_data = data

                return {
                    'success': True,
                    'data': result_data,
                    'total_records': len(result_data)
                }
            else:
                return {
                    'success': True,
                    'data': [],
                    'total_records': 0
                }

        except Exception as e:
            if self.debug:
                print(f"[TRACKER-MANAGER] Error getting tracker data: {e}")
            return {'success': False, 'error': str(e)}

    def delete_records(self, category: str, record_ids: List[int]) -> Dict:
        """Delete specific records from tracker table"""
        try:
            from core.python_database_manager import get_database_instance
            db = get_database_instance()

            if category not in self.tracker_tables:
                return {'success': False, 'error': f'Unknown category: {category}'}

            table_name = self.tracker_tables[category]['table_name']

            # Delete records
            placeholders = ','.join(['?' for _ in record_ids])
            query = f"DELETE FROM {table_name} WHERE id IN ({placeholders})"

            db.execute_update(query, tuple(record_ids))

            if self.debug:
                print(f"[TRACKER-MANAGER] Deleted {len(record_ids)} records from {table_name}")

            return {
                'success': True,
                'deleted_count': len(record_ids)
            }

        except Exception as e:
            if self.debug:
                print(f"[TRACKER-MANAGER] Error deleting records: {e}")
            return {'success': False, 'error': str(e)}

    def clear_table(self, category: str) -> Dict:
        """Clear entire tracker table"""
        try:
            from core.python_database_manager import get_database_instance
            db = get_database_instance()

            if category not in self.tracker_tables:
                return {'success': False, 'error': f'Unknown category: {category}'}

            table_name = self.tracker_tables[category]['table_name']

            # Get count before deletion
            count_result = db.execute_query(f"SELECT COUNT(*) as count FROM {table_name}")
            deleted_count = count_result[0]['count'] if count_result else 0

            # Clear table
            db.execute_update(f"DELETE FROM {table_name}")

            if self.debug:
                print(f"[TRACKER-MANAGER] Cleared {deleted_count} records from {table_name}")

            return {
                'success': True,
                'deleted_count': deleted_count
            }

        except Exception as e:
            if self.debug:
                print(f"[TRACKER-MANAGER] Error clearing table: {e}")
            return {'success': False, 'error': str(e)}

    def get_available_years(self, category: str) -> List[str]:
        """Get available years for filtering"""
        try:
            from core.python_database_manager import get_database_instance
            db = get_database_instance()

            if category not in self.tracker_tables:
                return []

            table_name = self.tracker_tables[category]['table_name']

            years = db.execute_query(
                f"SELECT DISTINCT period_year FROM {table_name} WHERE period_year IS NOT NULL ORDER BY period_year DESC"
            )

            return [year['period_year'] for year in years if year['period_year']]

        except Exception as e:
            if self.debug:
                print(f"[TRACKER-MANAGER] Error getting available years: {e}")
            return []

    def export_tracker_data(self, category: str, filters: Dict = None) -> Dict:
        """Export tracker data to Excel"""
        try:
            import pandas as pd
            from datetime import datetime

            # Get data
            data_result = self.get_tracker_data(category, filters)
            if not data_result['success']:
                return data_result

            data = data_result['data']
            if not data:
                return {'success': False, 'error': 'No data to export'}

            # Create DataFrame
            df = pd.DataFrame(data)

            # Format columns for display
            if 'period_year' in df.columns and 'period_month' in df.columns:
                df['Period'] = df['period_year'].astype(str) + '-' + df['period_month'].astype(str).str.zfill(2)

            # Select and order columns for export
            export_columns = self._get_export_columns(category)
            df_export = df[[col for col in export_columns if col in df.columns]]

            # Create export filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            category_name = category.replace('_', ' ').title()
            filename = f"Bank_Adviser_{category_name}_{timestamp}.xlsx"

            # Ensure reports directory exists
            reports_dir = os.path.join('reports', 'Bank_Adviser')
            os.makedirs(reports_dir, exist_ok=True)

            filepath = os.path.join(reports_dir, filename)

            # Export to Excel
            df_export.to_excel(filepath, index=False, sheet_name=category_name)

            if self.debug:
                print(f"[TRACKER-MANAGER] Exported {len(data)} records to {filepath}")

            return {
                'success': True,
                'filepath': filepath,
                'records_exported': len(data)
            }

        except Exception as e:
            if self.debug:
                print(f"[TRACKER-MANAGER] Error exporting data: {e}")
            return {'success': False, 'error': str(e)}

    def _get_export_columns(self, category: str) -> List[str]:
        """Get columns for export based on category"""
        column_map = {
            'in_house_loans': ['employee_no', 'employee_name', 'department', 'loan_type', 'loan_amount', 'Period', 'remarks'],
            'external_loans': ['employee_no', 'employee_name', 'department', 'loan_type', 'loan_amount', 'Period'],
            'leave_claims': ['employee_no', 'employee_name', 'department', 'allowance_type', 'payable_amount', 'Period', 'remarks'],
            'educational_subsidy': ['employee_no', 'employee_name', 'department', 'allowance_type', 'payable_amount', 'Period', 'remarks'],
            'long_service_awards': ['employee_no', 'employee_name', 'department', 'award_type', 'payable_amount', 'Period', 'remarks'],
            'motor_vehicle_maintenance': ['employee_no', 'employee_name', 'department', 'allowance_type', 'payable_amount', 'Period', 'remarks'],
            'duplicate_checker': ['employee_no', 'employee_name', 'department', 'claim_type', 'payable_amount', 'second_period', 'remarks']
        }

        return column_map.get(category, ['employee_no', 'employee_name', 'department', 'Period'])

    def get_summary_stats(self, category: str, filters: Dict = None) -> Dict:
        """Get summary statistics for category"""
        try:
            data_result = self.get_tracker_data(category, filters)
            if not data_result['success']:
                return data_result

            data = data_result['data']

            if not data:
                return {
                    'success': True,
                    'total_records': 0,
                    'total_amount': 0,
                    'unique_employees': 0,
                    'latest_period': None
                }

            # Calculate statistics
            amount_column = self.tracker_tables[category]['amount_column']
            total_amount = sum(float(row.get(amount_column, 0) or 0) for row in data)
            unique_employees = len(set(row.get('employee_no') for row in data if row.get('employee_no')))

            # Get latest period
            latest_period = None
            if data:
                periods = [f"{row.get('period_year', '')}-{row.get('period_month', '')}" for row in data
                          if row.get('period_year') and row.get('period_month')]
                if periods:
                    latest_period = max(periods)

            return {
                'success': True,
                'total_records': len(data),
                'total_amount': total_amount,
                'unique_employees': unique_employees,
                'latest_period': latest_period
            }

        except Exception as e:
            if self.debug:
                print(f"[TRACKER-MANAGER] Error getting summary stats: {e}")
            return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    """Command line interface for Bank Adviser Tracker Manager"""
    import sys
    import json

    if len(sys.argv) < 2:
        print(json.dumps({'success': False, 'error': 'No command provided'}))
        sys.exit(1)

    command = sys.argv[1]
    manager = BankAdviserTrackerManager(debug=False)

    try:
        if command == 'clear_table':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for clear_table'}
            else:
                category = sys.argv[2]
                result = manager.clear_table(category)

        elif command == 'delete_records':
            if len(sys.argv) < 4:
                result = {'success': False, 'error': 'Category and record IDs required for delete_records'}
            else:
                category = sys.argv[2]
                record_ids = [int(id_str) for id_str in sys.argv[3:]]
                result = manager.delete_records(category, record_ids)

        elif command == 'get_tracker_data':
            if len(sys.argv) < 3:
                result = {'success': False, 'error': 'Category required for get_tracker_data'}
            else:
                category = sys.argv[2]
                result = manager.get_tracker_data(category)

        else:
            result = {'success': False, 'error': f'Unknown command: {command}'}

        print(json.dumps(result))

    except Exception as e:
        error_result = {'success': False, 'error': str(e)}
        print(json.dumps(error_result))
