/**
 * STORAGE MIGRATION MANAGER
 * Systematically migrates from old file-based storage to unified SQLite database
 * Removes old storage systems after successful migration
 */

const fs = require('fs');
const path = require('path');
const UnifiedDatabase = require('./unified_database');

class StorageMigrationManager {
    constructor() {
        this.database = null;
        this.migrationLog = [];
        this.backupDir = path.join(__dirname, '..', 'data', 'migration_backup');
        
        console.log('🔄 STORAGE MIGRATION MANAGER INITIALIZED');
    }

    /**
     * Initialize database connection
     */
    async initialize() {
        this.database = new UnifiedDatabase();
        await this.database.initializeDatabase();
        
        // Ensure backup directory exists
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
        }
        
        console.log('✅ Migration manager ready');
    }

    /**
     * Perform complete migration from old storage to new database
     */
    async performCompleteMigration() {
        console.log('🚀 STARTING COMPLETE STORAGE MIGRATION...');
        
        try {
            // Step 1: Backup all existing data
            await this.backupExistingData();
            
            // Step 2: Migrate each module
            await this.migrateDictionaryManager();
            await this.migrateReportManager();
            await this.migrateAutoLearning();
            await this.migrateSettings();
            
            // Step 3: Verify migration success
            const verification = await this.verifyMigration();
            
            if (verification.success) {
                // Step 4: Remove old storage files
                await this.removeOldStorageFiles();
                
                console.log('✅ COMPLETE STORAGE MIGRATION SUCCESSFUL');
                return {
                    success: true,
                    migrationLog: this.migrationLog,
                    verification: verification
                };
            } else {
                console.error('❌ Migration verification failed');
                return {
                    success: false,
                    error: 'Migration verification failed',
                    verification: verification
                };
            }
            
        } catch (error) {
            console.error('❌ Migration failed:', error);
            return {
                success: false,
                error: error.message,
                migrationLog: this.migrationLog
            };
        }
    }

    /**
     * Backup all existing data before migration
     */
    async backupExistingData() {
        console.log('💾 BACKING UP EXISTING DATA...');
        
        const filesToBackup = [
            'data/payroll_dictionary.json',
            'data/app_settings.json',
            'data/reports',
            'data/auto_learning',
            'data/pending_items.json'
        ];
        
        for (const file of filesToBackup) {
            const fullPath = path.join(__dirname, '..', file);
            if (fs.existsSync(fullPath)) {
                const backupPath = path.join(this.backupDir, file);
                
                // Ensure backup directory structure exists
                const backupDirPath = path.dirname(backupPath);
                if (!fs.existsSync(backupDirPath)) {
                    fs.mkdirSync(backupDirPath, { recursive: true });
                }
                
                if (fs.statSync(fullPath).isDirectory()) {
                    this.copyDirectoryRecursive(fullPath, backupPath);
                } else {
                    fs.copyFileSync(fullPath, backupPath);
                }
                
                this.migrationLog.push(`✅ Backed up: ${file}`);
                console.log(`💾 Backed up: ${file}`);
            }
        }
        
        console.log('✅ Data backup completed');
    }

    /**
     * Migrate Dictionary Manager from JSON to database
     */
    async migrateDictionaryManager() {
        console.log('📚 MIGRATING DICTIONARY MANAGER...');
        
        const dictionaryPath = path.join(__dirname, '..', 'data', 'payroll_dictionary.json');
        
        if (fs.existsSync(dictionaryPath)) {
            try {
                const dictionaryData = JSON.parse(fs.readFileSync(dictionaryPath, 'utf8'));
                await this.database.saveDictionary(dictionaryData);
                
                this.migrationLog.push('✅ Dictionary Manager migrated to database');
                console.log('✅ Dictionary Manager migration completed');
                
                return { success: true, itemsCount: Object.keys(dictionaryData).length };
            } catch (error) {
                this.migrationLog.push(`❌ Dictionary Manager migration failed: ${error.message}`);
                throw error;
            }
        } else {
            this.migrationLog.push('ℹ️ No dictionary file found to migrate');
            console.log('ℹ️ No dictionary file found to migrate');
            return { success: true, itemsCount: 0 };
        }
    }

    /**
     * Migrate Report Manager from JSON files to database
     */
    async migrateReportManager() {
        console.log('📊 MIGRATING REPORT MANAGER...');
        
        const reportsDir = path.join(__dirname, '..', 'data', 'reports');
        let migratedCount = 0;
        
        if (fs.existsSync(reportsDir)) {
            try {
                const reportFiles = this.getAllReportFiles(reportsDir);
                
                for (const reportFile of reportFiles) {
                    try {
                        const reportData = JSON.parse(fs.readFileSync(reportFile, 'utf8'));
                        
                        // Convert to database format
                        const dbReport = {
                            report_id: reportData.id || path.basename(reportFile, '.json'),
                            report_type: reportData.type || 'migrated',
                            report_category: this.determineReportCategory(reportFile),
                            title: reportData.title || 'Migrated Report',
                            description: reportData.description || '',
                            file_paths: reportData.report_paths || {},
                            metadata: reportData.metadata || {},
                            file_size: reportData.file_size || fs.statSync(reportFile).size
                        };
                        
                        await this.database.saveReport(dbReport);
                        migratedCount++;
                        
                    } catch (error) {
                        console.warn(`⚠️ Failed to migrate report: ${reportFile}`, error);
                    }
                }
                
                this.migrationLog.push(`✅ Report Manager migrated: ${migratedCount} reports`);
                console.log(`✅ Report Manager migration completed: ${migratedCount} reports`);
                
                return { success: true, reportsCount: migratedCount };
            } catch (error) {
                this.migrationLog.push(`❌ Report Manager migration failed: ${error.message}`);
                throw error;
            }
        } else {
            this.migrationLog.push('ℹ️ No reports directory found to migrate');
            console.log('ℹ️ No reports directory found to migrate');
            return { success: true, reportsCount: 0 };
        }
    }

    /**
     * Migrate Auto Learning from JSON files to database
     */
    async migrateAutoLearning() {
        console.log('🧠 MIGRATING AUTO LEARNING...');
        
        const autoLearningFiles = [
            path.join(__dirname, '..', 'data', 'pending_items.json'),
            path.join(__dirname, '..', 'data', 'auto_learning', 'pending.json')
        ];
        
        let migratedCount = 0;
        
        for (const filePath of autoLearningFiles) {
            if (fs.existsSync(filePath)) {
                try {
                    const pendingData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                    
                    // Create auto learning session
                    const sessionId = await this.database.startAutoLearningSession('Migrated Session');
                    
                    // Migrate pending items
                    if (Array.isArray(pendingData)) {
                        for (const item of pendingData) {
                            await this.database.addPendingItem(sessionId, {
                                label: item.label || item.item_name,
                                suggested_section: item.section || 'UNKNOWN',
                                suggested_standard_name: item.standard_name || item.label,
                                confidence: item.confidence || 0.8,
                                occurrence_count: item.count || 1,
                                first_seen_in: item.first_seen || 'migration'
                            });
                            migratedCount++;
                        }
                    }
                    
                } catch (error) {
                    console.warn(`⚠️ Failed to migrate auto learning file: ${filePath}`, error);
                }
            }
        }
        
        this.migrationLog.push(`✅ Auto Learning migrated: ${migratedCount} items`);
        console.log(`✅ Auto Learning migration completed: ${migratedCount} items`);
        
        return { success: true, itemsCount: migratedCount };
    }

    /**
     * Migrate Settings to database
     */
    async migrateSettings() {
        console.log('⚙️ MIGRATING SETTINGS...');
        
        const settingsPath = path.join(__dirname, '..', 'data', 'app_settings.json');
        
        if (fs.existsSync(settingsPath)) {
            try {
                const settingsData = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
                
                // Store each setting in database
                for (const [key, value] of Object.entries(settingsData)) {
                    await this.database.runQuery(
                        'INSERT OR REPLACE INTO system_settings (setting_key, setting_value, module_name) VALUES (?, ?, ?)',
                        [key, JSON.stringify(value), 'application']
                    );
                }
                
                this.migrationLog.push(`✅ Settings migrated: ${Object.keys(settingsData).length} settings`);
                console.log('✅ Settings migration completed');
                
                return { success: true, settingsCount: Object.keys(settingsData).length };
            } catch (error) {
                this.migrationLog.push(`❌ Settings migration failed: ${error.message}`);
                throw error;
            }
        } else {
            this.migrationLog.push('ℹ️ No settings file found to migrate');
            console.log('ℹ️ No settings file found to migrate');
            return { success: true, settingsCount: 0 };
        }
    }

    /**
     * Verify migration success
     */
    async verifyMigration() {
        console.log('🔍 VERIFYING MIGRATION...');
        
        try {
            const stats = await this.database.getSystemStatistics();
            
            const verification = {
                success: true,
                issues: [],
                stats: stats
            };
            
            // Check if data was migrated
            if (stats.dictionary.totalSections.count === 0) {
                verification.issues.push('No dictionary sections found in database');
            }
            
            if (stats.reports.totalReports.count === 0) {
                verification.issues.push('No reports found in database');
            }
            
            // Check database integrity
            const integrityCheck = await this.database.getQuery('PRAGMA integrity_check');
            if (integrityCheck.integrity_check !== 'ok') {
                verification.issues.push('Database integrity check failed');
            }
            
            verification.success = verification.issues.length === 0;
            
            console.log(`🔍 Migration verification: ${verification.success ? 'PASSED' : 'FAILED'}`);
            if (verification.issues.length > 0) {
                console.log('Issues found:', verification.issues);
            }
            
            return verification;
            
        } catch (error) {
            console.error('❌ Migration verification error:', error);
            return {
                success: false,
                error: error.message,
                issues: ['Verification process failed']
            };
        }
    }

    /**
     * Remove old storage files after successful migration
     */
    async removeOldStorageFiles() {
        console.log('🗑️ REMOVING OLD STORAGE FILES...');
        
        const filesToRemove = [
            'data/payroll_dictionary.json',
            'data/app_settings.json'
            // Note: Keep reports directory for now as it may contain actual report files
        ];
        
        for (const file of filesToRemove) {
            const fullPath = path.join(__dirname, '..', file);
            if (fs.existsSync(fullPath)) {
                try {
                    fs.unlinkSync(fullPath);
                    this.migrationLog.push(`🗑️ Removed old file: ${file}`);
                    console.log(`🗑️ Removed: ${file}`);
                } catch (error) {
                    console.warn(`⚠️ Failed to remove: ${file}`, error);
                }
            }
        }
        
        console.log('✅ Old storage files cleanup completed');
    }

    // Helper methods
    getAllReportFiles(dir) {
        const files = [];
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            if (fs.statSync(fullPath).isDirectory()) {
                files.push(...this.getAllReportFiles(fullPath));
            } else if (item.endsWith('.json')) {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    determineReportCategory(filePath) {
        if (filePath.includes('Payroll_Audit')) return 'Payroll_Audit_Reports';
        if (filePath.includes('PDF_Sorter')) return 'PDF_Sorter_Reports';
        if (filePath.includes('Data_Builder')) return 'Data_Builder_Reports';
        return 'General_Reports';
    }

    copyDirectoryRecursive(src, dest) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        
        const items = fs.readdirSync(src);
        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            
            if (fs.statSync(srcPath).isDirectory()) {
                this.copyDirectoryRecursive(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    /**
     * Close database connection
     */
    async close() {
        if (this.database) {
            await this.database.close();
        }
    }
}

module.exports = StorageMigrationManager;
