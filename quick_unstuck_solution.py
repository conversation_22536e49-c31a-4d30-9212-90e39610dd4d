#!/usr/bin/env python3
"""Quick solution to unstuck the audit process"""

import sqlite3
import sys
from datetime import datetime

def quick_unstuck():
    print("🚀 QUICK UNSTUCK SOLUTION")
    print("=" * 30)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # 1. Get current session
        print("1. 📊 Getting current session...")
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        if result:
            session = result[0]
            print(f"   Session: {session}")
        else:
            print("   ❌ No current session found")
            return False
        
        # 2. Check current state
        print("\n2. 🔍 Checking current state...")
        
        # Check phases
        cursor.execute('SELECT phase_name, status FROM session_phases WHERE session_id = ?', (session,))
        phases = cursor.fetchall()
        
        extraction_done = False
        comparison_done = False
        
        for phase_name, status in phases:
            print(f"   {phase_name}: {status}")
            if phase_name == 'EXTRACTION' and status == 'COMPLETED':
                extraction_done = True
            elif phase_name == 'COMPARISON' and status == 'COMPLETED':
                comparison_done = True
        
        # Check data
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))
        extracted_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session,))
        comparison_count = cursor.fetchone()[0]
        
        print(f"   Extracted data: {extracted_count}")
        print(f"   Comparison data: {comparison_count}")
        
        # 3. Apply fix based on state
        print("\n3. 🔧 Applying fix...")
        
        if extraction_done and not comparison_done and extracted_count > 0:
            print("   🎯 ISSUE: Extraction done but comparison not started")
            print("   🔄 SOLUTION: Force comparison phase to start")
            
            # Force comparison phase
            cursor.execute('''
                UPDATE session_phases 
                SET status = 'READY', started_at = NULL, completed_at = NULL
                WHERE session_id = ? AND phase_name = 'COMPARISON'
            ''', (session,))
            
            # Clear any locks
            try:
                cursor.execute('DELETE FROM session_locks WHERE lock_type = ?', ('processing',))
            except:
                pass
            
            conn.commit()
            print("   ✅ Comparison phase reset to READY")
            
        elif comparison_done and comparison_count > 0:
            print("   🎯 ISSUE: Comparison done but pre-reporting not started")
            print("   🔄 SOLUTION: Force pre-reporting phase to start")
            
            # Force pre-reporting phase
            cursor.execute('''
                UPDATE session_phases 
                SET status = 'READY', started_at = NULL, completed_at = NULL
                WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
            ''', (session,))
            
            conn.commit()
            print("   ✅ Pre-reporting phase reset to READY")
            
        elif extracted_count == 0:
            print("   🚨 ISSUE: No extracted data found")
            print("   💡 SOLUTION: Extraction may have failed - restart the audit")
            return False
            
        else:
            print("   ⚠️ ISSUE: Unknown state")
            print("   🔄 SOLUTION: Reset all phases after extraction")
            
            # Reset all phases after extraction
            phases_to_reset = ['COMPARISON', 'PRE_REPORTING', 'REPORT_GENERATION']
            for phase in phases_to_reset:
                cursor.execute('''
                    UPDATE session_phases 
                    SET status = 'NOT_STARTED', started_at = NULL, completed_at = NULL
                    WHERE session_id = ? AND phase_name = ?
                ''', (session, phase))
            
            conn.commit()
            print("   ✅ Reset phases after extraction")
        
        # 4. Verify fix
        print("\n4. ✅ Verification:")
        cursor.execute('SELECT phase_name, status FROM session_phases WHERE session_id = ?', (session,))
        updated_phases = cursor.fetchall()
        
        for phase_name, status in updated_phases:
            print(f"   {phase_name}: {status}")
        
        print("\n🎉 UNSTUCK SOLUTION APPLIED!")
        print("📋 NEXT STEPS:")
        print("   1. The process should continue automatically")
        print("   2. If still stuck, restart the audit from terminal")
        print("   3. Monitor the progress in the UI")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        conn.close()

def create_restart_command():
    print("\n🔄 RESTART COMMAND (if needed):")
    print("   If the process is still stuck, use this command:")
    print("   python main.py --current-pdf [current.pdf] --previous-pdf [previous.pdf]")
    print("   (Replace [current.pdf] and [previous.pdf] with your actual PDF paths)")

if __name__ == "__main__":
    print("🚨 AUDIT PROCESS STUCK AFTER EXTRACTION")
    print("This script will attempt to unstuck the process\n")
    
    success = quick_unstuck()
    
    if success:
        print("\n✅ UNSTUCK SOLUTION COMPLETED")
        print("The audit process should continue now")
    else:
        print("\n❌ UNSTUCK FAILED")
        print("You may need to restart the audit process")
        create_restart_command()
    
    sys.exit(0 if success else 1)
