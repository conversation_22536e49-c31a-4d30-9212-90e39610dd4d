#!/usr/bin/env python3
"""Fix the running job by updating phase statuses"""

import sqlite3
from datetime import datetime

def fix_running_job():
    """Fix the running job by updating extraction status"""
    print("🔧 FIXING RUNNING JOB - UPDATING PHASE STATUSES")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session_result = cursor.fetchone()
        if not session_result:
            print("❌ No current session found")
            return False
            
        session = session_result[0]
        print(f"📋 Current session: {session}")
        
        # Get extraction data count
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))
        count_result = cursor.fetchone()
        count = count_result[0] if count_result else 0
        print(f"📊 Extracted data count: {count}")
        
        if count == 0:
            print("❌ No extracted data found")
            return False
        
        # Update EXTRACTION phase status to COMPLETED
        cursor.execute('''
            UPDATE session_phases 
            SET status = ?, completed_at = ?, data_count = ? 
            WHERE session_id = ? AND phase_name = ?
        ''', ('COMPLETED', datetime.now().isoformat(), count, session, 'EXTRACTION'))
        
        print(f"✅ EXTRACTION phase marked as COMPLETED with {count} records")
        
        # Set COMPARISON phase status to READY
        cursor.execute('''
            UPDATE session_phases 
            SET status = ? 
            WHERE session_id = ? AND phase_name = ?
        ''', ('READY', session, 'COMPARISON'))
        
        print("✅ COMPARISON phase set to READY")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n🎉 SUCCESS: Running job fixed!")
        print("📋 The workflow should now be able to progress to comparison phase")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing running job: {e}")
        return False

if __name__ == "__main__":
    fix_running_job()
