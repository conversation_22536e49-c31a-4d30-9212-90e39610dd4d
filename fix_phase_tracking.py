#!/usr/bin/env python3
"""Fix phase tracking to match actual data state"""

import sqlite3
import sys
from datetime import datetime

def fix_phase_tracking():
    print("🔧 FIXING PHASE TRACKING TO MATCH DATA STATE")
    print("=" * 45)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session = cursor.fetchone()[0]
        print(f"Session: {session}")
        
        # Check actual data state
        print("\n📊 ACTUAL DATA STATE:")
        
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))
        extracted_count = cursor.fetchone()[0]
        print(f"   Extracted data: {extracted_count} records")
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison data: {comparison_count} records")
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   Pre-reporting data: {pre_reporting_count} records")
        
        # Fix phase tracking to match data
        print("\n🔄 UPDATING PHASE TRACKING:")
        
        current_time = datetime.now().isoformat()
        
        # 1. Mark EXTRACTION as completed (since we have 156K records)
        if extracted_count > 0:
            cursor.execute('''
                UPDATE session_phases 
                SET status = 'COMPLETED', 
                    started_at = ?, 
                    completed_at = ?,
                    data_count = ?
                WHERE session_id = ? AND phase_name = 'EXTRACTION'
            ''', (current_time, current_time, extracted_count, session))
            print(f"   ✅ EXTRACTION: COMPLETED ({extracted_count} records)")
        
        # 2. Set COMPARISON as ready to start
        cursor.execute('''
            UPDATE session_phases 
            SET status = 'READY', 
                started_at = NULL, 
                completed_at = NULL,
                data_count = 0
            WHERE session_id = ? AND phase_name = 'COMPARISON'
        ''', (session,))
        print("   🔄 COMPARISON: READY")
        
        # 3. Reset other phases
        other_phases = ['PRE_REPORTING', 'REPORT_GENERATION', 'TRACKER_FEEDING', 'AUTO_LEARNING']
        for phase in other_phases:
            cursor.execute('''
                UPDATE session_phases 
                SET status = 'NOT_STARTED', 
                    started_at = NULL, 
                    completed_at = NULL,
                    data_count = 0
                WHERE session_id = ? AND phase_name = ?
            ''', (session, phase))
            print(f"   ⏸️ {phase}: NOT_STARTED")
        
        # 4. Clear any processing locks
        try:
            cursor.execute('DELETE FROM session_locks')
            print("   🔓 Cleared processing locks")
        except:
            pass
        
        conn.commit()
        
        # 5. Verify the fix
        print("\n✅ VERIFICATION:")
        cursor.execute('SELECT phase_name, status, data_count FROM session_phases WHERE session_id = ?', (session,))
        phases = cursor.fetchall()
        
        for phase_name, status, data_count in phases:
            print(f"   {phase_name}: {status} ({data_count} records)")
        
        # 6. Trigger the next phase
        print("\n🚀 TRIGGERING NEXT PHASE:")
        print("   The process should now continue with COMPARISON phase")
        print("   Monitor your terminal for progress updates")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        conn.close()

def provide_monitoring_tips():
    print("\n📋 MONITORING TIPS:")
    print("   1. Watch your terminal for phase progress messages")
    print("   2. Check the UI for real-time updates")
    print("   3. If still stuck after 5 minutes, the comparison phase may need manual trigger")
    print("   4. Expected next: 'Starting COMPARISON phase...'")
    
    print("\n🔄 IF STILL STUCK:")
    print("   Run this command to manually trigger comparison:")
    print("   python -c \"")
    print("   import sys; sys.path.append('core')")
    print("   from phased_process_manager import PhasedProcessManager")
    print("   manager = PhasedProcessManager()")
    print("   manager.session_id = 'audit_session_1751207456_e8df521a'")
    print("   result = manager._phase_comparison({})")
    print("   print('Comparison result:', result)")
    print("   \"")

if __name__ == "__main__":
    print("🚨 FIXING STUCK PROCESS - PHASE TRACKING MISMATCH")
    print("The extraction has 156K records but phase tracking shows NOT_STARTED\n")
    
    success = fix_phase_tracking()
    
    if success:
        provide_monitoring_tips()
        print("\n🎉 PHASE TRACKING FIXED!")
        print("✅ The audit process should continue automatically")
        print("📊 Next phase: COMPARISON (156K records → comparison_results)")
    else:
        print("\n❌ PHASE TRACKING FIX FAILED")
        print("Manual intervention may be required")
    
    sys.exit(0 if success else 1)
