#!/usr/bin/env python3
"""
Diagnose Data Builder API Issues
Investigates the backend API response parsing error in data_builder.js
"""

import sys
import os
import json
import subprocess
from pathlib import Path

def diagnose_data_builder_api():
    """Diagnose Data Builder API initialization issues"""
    
    print("🔍 DIAGNOSING DATA BUILDER API ISSUES")
    print("=" * 50)
    
    # 1. Check if Data Builder module exists
    print("\n1. 📁 DATA BUILDER MODULE CHECK:")
    
    data_builder_paths = [
        "core/data_builder.py",
        "data_builder.py", 
        "core/data_builder_module.py",
        "modules/data_builder.py"
    ]
    
    data_builder_found = None
    for path in data_builder_paths:
        if Path(path).exists():
            data_builder_found = path
            print(f"   ✅ Found Data Builder module: {path}")
            break
    
    if not data_builder_found:
        print("   ❌ Data Builder module not found")
        print("   📋 Searched paths:")
        for path in data_builder_paths:
            print(f"     - {path}")
        return
    
    # 2. Test Data Builder import
    print("\n2. 🐍 PYTHON MODULE IMPORT TEST:")
    
    try:
        sys.path.append('core')
        sys.path.append('.')
        
        # Try to import the Data Builder
        if 'data_builder' in data_builder_found:
            from core.data_builder import DataBuilder
            print("   ✅ Successfully imported DataBuilder")
            
            # Test initialization
            try:
                data_builder = DataBuilder()
                print("   ✅ Successfully created DataBuilder instance")
                
                # Test basic methods
                if hasattr(data_builder, 'initialize'):
                    print("   ✅ DataBuilder has initialize method")
                else:
                    print("   ⚠️ DataBuilder missing initialize method")
                    
            except Exception as e:
                print(f"   ❌ DataBuilder initialization failed: {e}")
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    # 3. Check Electron API integration
    print("\n3. 🖥️ ELECTRON API INTEGRATION CHECK:")
    
    # Check if main.js or electron main process exists
    electron_files = [
        "main.js",
        "electron/main.js",
        "src/main.js",
        "app/main.js"
    ]
    
    electron_main_found = None
    for path in electron_files:
        if Path(path).exists():
            electron_main_found = path
            print(f"   ✅ Found Electron main file: {path}")
            break
    
    if electron_main_found:
        try:
            with open(electron_main_found, 'r') as f:
                content = f.read()
                
            if 'callDataBuilderAPI' in content:
                print("   ✅ Found callDataBuilderAPI in Electron main")
            else:
                print("   ❌ callDataBuilderAPI not found in Electron main")
                print("   💡 This might be causing the API parsing error")
                
        except Exception as e:
            print(f"   ❌ Error reading Electron main file: {e}")
    else:
        print("   ⚠️ Electron main file not found")
        print("   📋 Searched paths:")
        for path in electron_files:
            print(f"     - {path}")
    
    # 4. Test API response format
    print("\n4. 📡 API RESPONSE FORMAT TEST:")
    
    try:
        # Simulate the initialize command
        if data_builder_found:
            sys.path.append(os.path.dirname(data_builder_found))
            
            # Test what the API would return
            test_response = {
                "success": True,
                "message": "Data Builder system initialized successfully",
                "capabilities": {
                    "maxEmployees": "2900+",
                    "extractionAccuracy": "100%",
                    "uiOptimization": "Enabled",
                    "databaseIntegration": "SQLite",
                    "zeroDataLoss": "Guaranteed"
                }
            }
            
            # Test JSON serialization
            json_response = json.dumps(test_response)
            print("   ✅ Test response JSON serialization successful")
            print(f"   📄 Response length: {len(json_response)} characters")
            
            # Test JSON parsing
            parsed_response = json.loads(json_response)
            print("   ✅ Test response JSON parsing successful")
            
            # Validate response structure
            if 'success' in parsed_response and 'capabilities' in parsed_response:
                print("   ✅ Response structure is valid")
            else:
                print("   ❌ Response structure is invalid")
                
    except Exception as e:
        print(f"   ❌ API response test failed: {e}")
    
    # 5. Check for common issues
    print("\n5. 🔍 COMMON ISSUES CHECK:")
    
    issues_found = []
    
    # Check if Python is accessible from Electron
    try:
        result = subprocess.run(['python', '--version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"   ✅ Python accessible: {result.stdout.strip()}")
        else:
            print("   ❌ Python not accessible from command line")
            issues_found.append("Python not in PATH")
    except Exception as e:
        print(f"   ❌ Python check failed: {e}")
        issues_found.append("Python execution error")
    
    # Check if required modules are available
    required_modules = ['sqlite3', 'json', 'pathlib']
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ Module {module} available")
        except ImportError:
            print(f"   ❌ Module {module} missing")
            issues_found.append(f"Missing module: {module}")
    
    # 6. Potential solutions
    print("\n6. 💡 POTENTIAL SOLUTIONS:")
    
    if issues_found:
        print("   🔧 ISSUES FOUND:")
        for issue in issues_found:
            print(f"     - {issue}")
        print()
    
    print("   📋 RECOMMENDED FIXES:")
    
    if not electron_main_found:
        print("     1. 🖥️ Add Electron main process with Data Builder API integration")
        print("        - Create main.js with IPC handlers")
        print("        - Implement callDataBuilderAPI function")
    
    if 'callDataBuilderAPI' not in (content if electron_main_found else ''):
        print("     2. 📡 Add Data Builder API handler to Electron main process")
        print("        - Register IPC handler for 'call-data-builder-api'")
        print("        - Implement Python subprocess communication")
    
    print("     3. 🔄 Add error handling for API response parsing")
    print("        - Add try-catch around JSON.parse operations")
    print("        - Validate response format before processing")
    
    print("     4. 🐛 Add debugging to data_builder.js")
    print("        - Log raw API responses before parsing")
    print("        - Add response validation")
    
    # 7. Quick fix suggestion
    print("\n7. 🚀 QUICK FIX SUGGESTION:")
    
    print("   📝 IMMEDIATE ACTION:")
    print("     1. Add response validation to data_builder.js callAPI method")
    print("     2. Log raw responses to identify parsing issues")
    print("     3. Add fallback for when Electron API is not available")
    
    # 8. Create a test fix
    print("\n8. 🧪 CREATING TEST FIX:")
    
    fix_content = '''
// Add this to data_builder.js callAPI method for debugging:

async callAPI(command, args = []) {
    try {
        console.log(`🔍 Calling API: ${command}`, args);
        
        // Use Electron's IPC to communicate with main process
        if (window.electronAPI && window.electronAPI.callDataBuilderAPI) {
            const response = await window.electronAPI.callDataBuilderAPI(command, args);
            console.log('📡 Raw API response:', response);
            
            // Validate response format
            if (typeof response === 'string') {
                try {
                    return JSON.parse(response);
                } catch (parseError) {
                    console.error('❌ JSON parse error:', parseError);
                    console.error('📄 Raw response:', response);
                    throw new Error('Failed to parse API response: ' + parseError.message);
                }
            }
            
            return response;
        }

        // Fallback: simulate API responses for testing
        console.log('🔄 Using simulated API response');
        return this.simulateAPIResponse(command, args);

    } catch (error) {
        console.error('❌ API call error:', error);
        return { success: false, error: error.message };
    }
}
'''
    
    print("   📄 Suggested fix added to debug the parsing issue")
    print("   💡 This will help identify if the issue is:")
    print("     - Malformed JSON from backend")
    print("     - Missing Electron API")
    print("     - Response format mismatch")

if __name__ == "__main__":
    diagnose_data_builder_api()
