#!/usr/bin/env python3
"""Check if report tables exist in the database"""

import sqlite3
from pathlib import Path

def check_report_tables():
    """Check if report-related tables exist"""
    
    db_path = Path("payroll_audit.db")
    if not db_path.exists():
        print("❌ Database not found")
        return
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print("📊 DATABASE TABLES:")
        for table in sorted(tables):
            print(f"   - {table}")
        
        # Check for report-related tables
        report_tables = ['reports', 'generated_reports']
        
        print("\n🔍 REPORT TABLE CHECK:")
        for table in report_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} records")
                
                # Show schema
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                print(f"      Columns: {[col[1] for col in columns]}")
            else:
                print(f"   ❌ {table}: Not found")
        
        # Check if we need to create the tables
        if 'generated_reports' not in tables:
            print("\n🔧 CREATING generated_reports TABLE:")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS generated_reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    report_type TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER DEFAULT 0,
                    report_metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
            print("   ✅ generated_reports table created")
        
        if 'reports' not in tables:
            print("\n🔧 CREATING reports TABLE:")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    report_id TEXT UNIQUE NOT NULL,
                    report_type TEXT NOT NULL,
                    report_category TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    file_paths TEXT,
                    metadata TEXT,
                    file_size INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
            print("   ✅ reports table created")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_report_tables()
