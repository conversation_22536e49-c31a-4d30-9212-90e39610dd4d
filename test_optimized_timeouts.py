#!/usr/bin/env python3
"""Test optimized timeouts that don't interfere with normal operations"""

import sys
import time
import platform

def test_optimized_timeouts():
    print("🧪 TESTING OPTIMIZED TIMEOUT MECHANISMS")
    print("=" * 45)
    
    print(f"Platform: {platform.system()}")
    print(f"Timeout protection: {'Enabled' if platform.system() == 'Windows' else 'Disabled (Unix optimization)'}")
    
    try:
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        # Create manager instance
        manager = PhasedProcessManager(debug_mode=False)  # Disable debug for speed
        
        # Get current session
        import sqlite3
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session = cursor.fetchone()[0]
        manager.session_id = session
        conn.close()
        
        print(f"Testing with session: {session}")
        
        # 1. Test normal operations are FAST
        print("\n1. ⚡ Testing normal operations speed:")
        
        operations = [
            ('Phase verification', lambda: manager._verify_phase_completion('EXTRACTION', 0)),
            ('Data count', lambda: manager._get_phase_data_count('EXTRACTION')),
            ('Interruption check (normal)', lambda: manager._check_interruption())
        ]
        
        all_fast = True
        for op_name, operation in operations:
            # Reset any pause state
            manager.is_paused = False
            manager.is_stopped = False
            
            start_time = time.time()
            try:
                result = operation()
                elapsed = time.time() - start_time
                
                if elapsed < 2:  # Should be very fast (under 2 seconds)
                    print(f"   ✅ {op_name}: {elapsed:.2f}s (FAST)")
                else:
                    print(f"   ⚠️ {op_name}: {elapsed:.2f}s (SLOW - may affect performance)")
                    all_fast = False
            except Exception as e:
                elapsed = time.time() - start_time
                print(f"   ❌ {op_name}: {elapsed:.2f}s - Error: {e}")
                all_fast = False
        
        # 2. Test timeout protection only activates when needed
        print("\n2. 🛡️ Testing timeout protection activation:")
        
        # Test pause timeout (should be quick now - 30 seconds max)
        manager.is_paused = True
        manager.is_stopped = False
        
        start_time = time.time()
        try:
            manager._check_interruption()
            elapsed = time.time() - start_time
            
            if elapsed < 35:  # Should auto-resume within 30 seconds + buffer
                print(f"   ✅ Pause timeout: {elapsed:.1f}s (REASONABLE)")
            else:
                print(f"   ❌ Pause timeout: {elapsed:.1f}s (TOO LONG)")
                all_fast = False
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"   ⚠️ Pause timeout: {elapsed:.1f}s - {e}")
        
        # 3. Test database operations aren't affected
        print("\n3. 💾 Testing database operations performance:")
        
        db_operations = [
            ('Count extracted_data', lambda: manager.db_manager.execute_query('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))),
            ('Count comparison_results', lambda: manager.db_manager.execute_query('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session,))),
            ('Count pre_reporting_results', lambda: manager.db_manager.execute_query('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session,)))
        ]
        
        for op_name, operation in db_operations:
            start_time = time.time()
            try:
                result = operation()
                elapsed = time.time() - start_time
                
                if elapsed < 1:  # Database ops should be very fast
                    print(f"   ✅ {op_name}: {elapsed:.3f}s")
                else:
                    print(f"   ⚠️ {op_name}: {elapsed:.3f}s (database may be slow)")
            except Exception as e:
                elapsed = time.time() - start_time
                print(f"   ❌ {op_name}: {elapsed:.3f}s - Error: {e}")
                all_fast = False
        
        return all_fast
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def summarize_optimizations():
    print("\n" + "=" * 50)
    print("📋 OPTIMIZED TIMEOUT SUMMARY")
    print("=" * 50)
    
    print("✅ OPTIMIZATIONS APPLIED:")
    print("   1. Auto-resume timeout: 5 minutes → 30 seconds")
    print("   2. Platform-specific timeouts: Only on Windows")
    print("   3. Database verification: 30s → 10s (Windows only)")
    print("   4. Phase status update: 15s → 10s (Windows only)")
    print("   5. Unix systems: No timeout overhead (better process management)")
    
    print("\n🎯 PERFORMANCE IMPACT:")
    print("   • Normal operations: NO IMPACT (timeouts only when needed)")
    print("   • Database operations: NO IMPACT (existing 30s timeout)")
    print("   • Phase transitions: MINIMAL IMPACT (10s max on Windows)")
    print("   • Unix systems: ZERO IMPACT (no timeouts)")
    
    print("\n🚀 EXPECTED BEHAVIOR:")
    print("   • Normal audit: Runs at full speed")
    print("   • Stuck process: Auto-recovers in 30 seconds max")
    print("   • Database operations: Unaffected")
    print("   • Full cycle completion: Same speed as before")

if __name__ == "__main__":
    print("🔧 TESTING OPTIMIZED TIMEOUT MECHANISMS")
    print("Verifying timeouts don't affect normal system performance\n")
    
    success = test_optimized_timeouts()
    summarize_optimizations()
    
    if success:
        print("\n🎉 OPTIMIZED TIMEOUTS WORKING!")
        print("✅ Normal operations run at full speed")
        print("✅ Timeout protection only when needed")
        print("✅ 30-second auto-resume (reasonable)")
        print("✅ Database operations unaffected")
        print("✅ Full audit cycle will complete normally")
    else:
        print("\n⚠️ PERFORMANCE ISSUES DETECTED")
        print("Some operations may be slower than expected")
    
    sys.exit(0 if success else 1)
