#!/usr/bin/env python3
"""Simple test of hybrid fix"""

import sys
import sqlite3

def test_simple():
    print("🔧 SIMPLE HYBRID FIX TEST")
    print("=" * 30)
    
    try:
        # Test database connection
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get session
        cursor.execute('SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY created_at DESC LIMIT 1')
        session = cursor.fetchone()[0]
        print(f"Session: {session}")
        
        # Test primary data query
        cursor.execute('''
            SELECT COUNT(*) FROM pre_reporting_results 
            WHERE session_id = ? AND selected_for_report = 1
        ''', (session,))
        primary_count = cursor.fetchone()[0]
        print(f"Primary data: {primary_count} records")
        
        # Test secondary data query
        cursor.execute('''
            SELECT COUNT(*) FROM comparison_results 
            WHERE session_id = ?
        ''', (session,))
        secondary_count = cursor.fetchone()[0]
        print(f"Secondary data: {secondary_count} records")
        
        conn.close()
        
        # Test Python import
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=False)
        print("Manager created successfully")
        
        # Test the method
        result = manager.get_pre_reporting_data(session)
        print(f"Method result: {result.get('success', False)}")
        print(f"Hybrid mode: {result.get('hybrid_mode', False)}")
        
        if result.get('success'):
            print("✅ HYBRID FIX WORKING!")
            return True
        else:
            print(f"❌ Failed: {result.get('error')}")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}")
    sys.exit(0 if success else 1)
