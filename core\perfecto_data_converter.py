#!/usr/bin/env python3
"""
PERFECTO Data Converter
Converts PERFECTO extractor output to system-compatible format
"""

from typing import Dict, Any

class PerfectoDataConverter:
    """Convert PERFECTO extractor output to system-expected format"""
    
    def __init__(self, debug=False):
        self.debug = debug
        
        # Field mapping from PERFECTO output to system format
        self.field_mapping = {
            # Personal Details
            'Employee No.': 'EMPLOYEE NO.',
            'Employee Name': 'EMPLOYEE NAME', 
            'SSF No.': 'SSF NO.',
            'Ghana Card ID': 'GHANA CARD ID',
            'Section': 'SECTION',
            'Department': 'DEPARTMENT',
            'Job Title': 'JOB TITLE',
            'Period': 'PERIOD',
            
            # Earnings
            'BASIC SALARY': 'BASIC SALARY',
            'GROSS SALARY': 'GROSS SALARY',
            'TAXABLE SALARY': 'TAXABLE SALARY',
            'NET PAY': 'NET PAY',
            'LEAVE ALLOWANCE': 'LEAVE ALLOWANCE',
            'RENT ELEMENT': 'RENT ELEMENT',
            '1ST FUEL ELEMENT': '1ST FUEL ELEMENT',
            '2ND FUEL ELEMENT': '2ND FUEL ELEMENT',
            'RESPONSIBILITY  HEADS': 'RESPONSIBILITY HEADS',
            
            # Deductions
            'SSF EEMPLOYEE': 'SSF EMPLOYEE',
            'INCOME TAX': 'INCOME TAX',
            'TITHES': 'TITHES',
            'TOTAL DEDUCTIONS': 'TOTAL DEDUCTIONS',
            'PENT. MIN WELFARE FUND': 'WELFARE FUND',
            'SCHOLARSHIP FUND (MINISTE': 'SCHOLARSHIP FUND',
            'Loan Deductions': 'LOAN DEDUCTIONS',
            'MINISTERS\' PENSION - CAT 2': 'MINISTERS PENSION',
            
            # Employers Contribution
            'SSF EMPLOYER': 'SSF EMPLOYER',
            'SAVING SCHEME (EMPLOYER)': 'SAVING SCHEME (EMPLOYER)',
            
            # Bank Details
            'Bank': 'BANK',
            'Account No.': 'ACCOUNT NO.',
            'Branch': 'BRANCH'
        }
        
        # Section classification mapping
        self.section_classification = {
            # Personal Details
            'EMPLOYEE NO.': 'PERSONAL DETAILS',
            'EMPLOYEE NAME': 'PERSONAL DETAILS',
            'SSF NO.': 'PERSONAL DETAILS',
            'GHANA CARD ID': 'PERSONAL DETAILS',
            'SECTION': 'PERSONAL DETAILS',
            'DEPARTMENT': 'PERSONAL DETAILS',
            'JOB TITLE': 'PERSONAL DETAILS',
            'PERIOD': 'PERSONAL DETAILS',
            
            # Earnings
            'BASIC SALARY': 'EARNINGS',
            'GROSS SALARY': 'EARNINGS',
            'TAXABLE SALARY': 'EARNINGS',
            'NET PAY': 'EARNINGS',
            'LEAVE ALLOWANCE': 'EARNINGS',
            'RENT ELEMENT': 'EARNINGS',
            '1ST FUEL ELEMENT': 'EARNINGS',
            '2ND FUEL ELEMENT': 'EARNINGS',
            'RESPONSIBILITY HEADS': 'EARNINGS',
            
            # Deductions
            'SSF EMPLOYEE': 'DEDUCTIONS',
            'INCOME TAX': 'DEDUCTIONS',
            'TITHES': 'DEDUCTIONS',
            'TOTAL DEDUCTIONS': 'DEDUCTIONS',
            'WELFARE FUND': 'DEDUCTIONS',
            'SCHOLARSHIP FUND': 'DEDUCTIONS',
            'LOAN DEDUCTIONS': 'DEDUCTIONS',
            'MINISTERS PENSION': 'DEDUCTIONS',
            
            # Employers Contribution
            'SSF EMPLOYER': 'EMPLOYERS CONTRIBUTION',
            'SAVING SCHEME (EMPLOYER)': 'EMPLOYERS CONTRIBUTION',
            
            # Bank Details
            'BANK': 'EMPLOYEE BANK DETAILS',
            'ACCOUNT NO.': 'EMPLOYEE BANK DETAILS',
            'BRANCH': 'EMPLOYEE BANK DETAILS'
        }
    
    def convert_to_system_format(self, perfecto_data: Dict[str, str]) -> Dict[str, Any]:
        """
        Convert PERFECTO extractor output to system-expected format
        
        Args:
            perfecto_data: Dict from PERFECTO extractor {field_name: value}
            
        Returns:
            System format: {
                'success': True,
                'employee_data': {
                    'PERSONAL DETAILS': {field: value},
                    'EARNINGS': {field: value},
                    'DEDUCTIONS': {field: value},
                    'LOANS': {field: value},
                    'EMPLOYERS CONTRIBUTION': {field: value},
                    'EMPLOYEE BANK DETAILS': {field: value}
                }
            }
        """
        
        if self.debug:
            print(f"🔄 Converting PERFECTO data: {len(perfecto_data)} fields")
        
        # Check for errors
        if 'error' in perfecto_data:
            return {
                'success': False,
                'error': perfecto_data['error'],
                'employee_data': {}
            }
        
        # Initialize system format
        system_data = {
            'PERSONAL DETAILS': {},
            'EARNINGS': {},
            'DEDUCTIONS': {},
            'LOANS': {},
            'EMPLOYERS CONTRIBUTION': {},
            'EMPLOYEE BANK DETAILS': {}
        }
        
        # Convert each field
        for perfecto_field, value in perfecto_data.items():
            # Skip empty values
            if not value or str(value).strip() == '':
                continue
                
            # Handle loan fields specially (they have format "LOAN_NAME - FIELD_TYPE")
            if ' - ' in perfecto_field and any(loan_field in perfecto_field for loan_field in ['BALANCE B/F', 'CURRENT DEDUCTION', 'OUST. BALANCE']):
                self._process_loan_field(perfecto_field, value, system_data)
                continue
            
            # Map field name
            system_field = self.field_mapping.get(perfecto_field, perfecto_field.upper())
            
            # Get section classification
            section = self.section_classification.get(system_field, 'PERSONAL DETAILS')
            
            # Store in appropriate section
            system_data[section][system_field] = value
            
            if self.debug:
                print(f"  {perfecto_field} -> {section}[{system_field}] = {value}")
        
        return {
            'success': True,
            'employee_data': system_data
        }
    
    def _process_loan_field(self, perfecto_field: str, value: str, system_data: Dict) -> None:
        """Process loan fields with special format"""
        
        # Extract loan name and field type
        # Format: "SALARY ADVANCE-MINS - BALANCE B/F"
        parts = perfecto_field.split(' - ', 1)
        if len(parts) != 2:
            return
            
        loan_name = parts[0].strip()
        field_type = parts[1].strip()
        
        # Create system field name
        system_field = f"{loan_name} - {field_type}"
        
        # Store in LOANS section
        system_data['LOANS'][system_field] = value
        
        if self.debug:
            print(f"  LOAN: {perfecto_field} -> LOANS[{system_field}] = {value}")

def main():
    """Test the converter"""
    
    # Test data from PERFECTO extractor
    test_data = {
        'Employee No.': 'COP0209',
        'Employee Name': 'APPIAH-AIDOO    A.',
        'BASIC SALARY': '9,309.97',
        'SSF EEMPLOYEE': '0.00',
        'SALARY ADVANCE-MINS - BALANCE B/F': '11,111.12',
        'SSF EMPLOYER': '0.00',
        'Bank': 'N/A'
    }
    
    converter = PerfectoDataConverter(debug=True)
    result = converter.convert_to_system_format(test_data)
    
    print("\n=== CONVERSION RESULT ===")
    print(f"Success: {result['success']}")
    for section, data in result['employee_data'].items():
        if data:
            print(f"\n{section}:")
            for field, value in data.items():
                print(f"  {field}: {value}")

if __name__ == "__main__":
    main()
