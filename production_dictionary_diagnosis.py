#!/usr/bin/env python3
"""
Production Dictionary Diagnosis
Comprehensive diagnosis of database connection and synchronization issues
"""

import sqlite3
import json
import os
import sys
from pathlib import Path
import hashlib
import time

def production_dictionary_diagnosis():
    """Comprehensive diagnosis of dictionary synchronization issues"""
    
    print("🔍 PRODUCTION DICTIONARY DIAGNOSIS")
    print("=" * 60)
    
    # 1. Database File Analysis
    print("\n1. 📁 DATABASE FILE ANALYSIS:")
    
    db_files = []
    search_paths = [
        ".",
        "core",
        "data", 
        "database",
        os.path.expanduser("~"),
        os.path.join(os.path.expanduser("~"), "AppData", "Local"),
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming")
    ]
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            for root, dirs, files in os.walk(search_path):
                for file in files:
                    if file.endswith('.db') or file.endswith('.sqlite') or file.endswith('.sqlite3'):
                        full_path = os.path.join(root, file)
                        db_files.append(full_path)
    
    print(f"Found {len(db_files)} database files:")
    for db_file in db_files:
        size = os.path.getsize(db_file) if os.path.exists(db_file) else 0
        modified = time.ctime(os.path.getmtime(db_file)) if os.path.exists(db_file) else "Unknown"
        print(f"  {db_file} ({size} bytes, modified: {modified})")
    
    # 2. Analyze each database file
    print("\n2. 🔍 DATABASE CONTENT ANALYSIS:")
    
    dictionary_data_found = {}
    
    for db_file in db_files:
        try:
            print(f"\n  📊 Analyzing: {db_file}")
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # Check for dictionary tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'dictionary%'")
            dict_tables = cursor.fetchall()
            
            if dict_tables:
                print(f"    Dictionary tables: {[t[0] for t in dict_tables]}")
                
                # Check sections
                try:
                    cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
                    sections_count = cursor.fetchone()[0]
                    print(f"    Sections: {sections_count}")
                    
                    if sections_count > 0:
                        cursor.execute("SELECT id, section_name FROM dictionary_sections")
                        sections = cursor.fetchall()
                        print(f"    Section names: {[s[1] for s in sections]}")
                except:
                    print("    Error reading sections")
                
                # Check items
                try:
                    cursor.execute("SELECT COUNT(*) FROM dictionary_items")
                    items_count = cursor.fetchone()[0]
                    print(f"    Items: {items_count}")
                    
                    if items_count > 0:
                        dictionary_data_found[db_file] = {
                            'sections': sections_count,
                            'items': items_count
                        }
                        
                        # Sample items
                        cursor.execute("""
                            SELECT ds.section_name, di.item_name, di.include_in_report
                            FROM dictionary_items di
                            JOIN dictionary_sections ds ON di.section_id = ds.id
                            LIMIT 5
                        """)
                        sample_items = cursor.fetchall()
                        print("    Sample items:")
                        for section, item, include in sample_items:
                            print(f"      {section}.{item} (include: {bool(include)})")
                except:
                    print("    Error reading items")
            else:
                print("    No dictionary tables found")
            
            conn.close()
            
        except Exception as e:
            print(f"    Error analyzing {db_file}: {e}")
    
    # 3. Python Dictionary Manager Analysis
    print("\n3. 🐍 PYTHON DICTIONARY MANAGER ANALYSIS:")
    
    try:
        sys.path.append('core')
        from dictionary_manager import PayrollDictionaryManager
        
        dict_manager = PayrollDictionaryManager(debug=True)
        
        # Check what database it's trying to use
        print(f"Dictionary manager database path: {getattr(dict_manager, 'database_path', 'Not set')}")
        print(f"Dictionary manager use_database: {getattr(dict_manager, 'use_database', 'Not set')}")
        
        # Try to load
        success = dict_manager.load_dictionary()
        print(f"Load success: {success}")
        
        if success and dict_manager.dictionary:
            total_items = sum(len(section.get('items', {})) for section in dict_manager.dictionary.values())
            print(f"Python manager loaded: {len(dict_manager.dictionary)} sections, {total_items} items")
            
            # Check if it has database connection
            if hasattr(dict_manager, 'database') and dict_manager.database:
                print("Dictionary manager has database connection")
                
                # Try to get the actual database file path
                if hasattr(dict_manager.database, 'db_path'):
                    actual_db_path = dict_manager.database.db_path
                    print(f"Actual database path: {actual_db_path}")
                    
                    # Check if this file exists and has data
                    if actual_db_path in dictionary_data_found:
                        print("✅ Database file matches and has dictionary data")
                    else:
                        print("❌ Database file mismatch or no data")
            else:
                print("❌ Dictionary manager has no database connection")
        
    except Exception as e:
        print(f"❌ Error analyzing Python dictionary manager: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. Database Connection Path Analysis
    print("\n4. 🔗 DATABASE CONNECTION PATH ANALYSIS:")
    
    # Check environment variables
    env_vars = ['DATABASE_PATH', 'DB_PATH', 'SQLITE_PATH', 'PAYROLL_DB']
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            print(f"Environment variable {var}: {value}")
    
    # Check common configuration files
    config_files = ['config.json', 'database.json', 'settings.json', '.env']
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    if config_file.endswith('.json'):
                        config = json.load(f)
                        print(f"Config file {config_file}: {config}")
                    else:
                        content = f.read()
                        print(f"Config file {config_file}: {content[:200]}...")
            except Exception as e:
                print(f"Error reading {config_file}: {e}")
    
    # 5. File Hash Analysis
    print("\n5. 🔐 FILE HASH ANALYSIS:")
    
    for db_file in db_files:
        try:
            with open(db_file, 'rb') as f:
                content = f.read()
                hash_md5 = hashlib.md5(content).hexdigest()
                print(f"{db_file}: {hash_md5}")
        except Exception as e:
            print(f"Error hashing {db_file}: {e}")
    
    # 6. Recommendations
    print("\n6. 💡 DIAGNOSIS SUMMARY:")
    
    if len(dictionary_data_found) == 0:
        print("❌ CRITICAL: No database files contain dictionary data")
        print("🔧 SOLUTION: Dictionary data exists only in Python memory, not persisted")
    elif len(dictionary_data_found) == 1:
        db_with_data = list(dictionary_data_found.keys())[0]
        print(f"✅ Dictionary data found in: {db_with_data}")
        print("🔧 SOLUTION: Ensure all components use this database file")
    else:
        print(f"⚠️ Multiple databases with dictionary data: {list(dictionary_data_found.keys())}")
        print("🔧 SOLUTION: Consolidate to single source of truth")
    
    return dictionary_data_found

if __name__ == "__main__":
    production_dictionary_diagnosis()
