#!/usr/bin/env python3
"""
ADVANCED BANK PROCESSOR
Enhanced multi-bank processing with performance optimization
Handles large datasets with intelligent bank grouping and validation
"""

import os
import sys
import json
import sqlite3
import threading
from datetime import datetime
from typing import List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

class AdvancedBankProcessor:
    """
    Advanced bank processing with multi-threading and optimization
    """
    
    def __init__(self, max_workers: int = 4, debug: bool = False):
        self.max_workers = max_workers
        self.debug = debug
        self.bank_mapping = self._load_bank_mapping()
        self.processing_stats = {
            'total_employees': 0,
            'total_banks': 0,
            'processing_time': 0,
            'errors': []
        }
        
        print(f"[ADVANCED BANK PROCESSOR] Initialized with {max_workers} workers")

    def _load_bank_mapping(self) -> Dict[str, str]:
        """Load bank name standardization mapping"""
        # Standard bank name mappings for Ghana
        return {
            'GCB': 'Ghana Commercial Bank Ltd',
            'GCB BANK': 'Ghana Commercial Bank Ltd',
            'GHANA COMMERCIAL BANK': 'Ghana Commercial Bank Ltd',
            'GHANA COMMERCIAL BANK LTD': 'Ghana Commercial Bank Ltd',
            'ECOBANK': 'Ecobank Ghana Ltd',
            'ECOBANK GHANA': 'Ecobank Ghana Ltd',
            'ECOBANK GHANA LTD': 'Ecobank Ghana Ltd',
            'ABSA': 'Absa Bank Ghana Ltd',
            'ABSA BANK': 'Absa Bank Ghana Ltd',
            'ABSA BANK GHANA': 'Absa Bank Ghana Ltd',
            'STANDARD CHARTERED': 'Standard Chartered Bank Ghana Ltd',
            'STANCHART': 'Standard Chartered Bank Ghana Ltd',
            'FIDELITY': 'Fidelity Bank Ghana Ltd',
            'FIDELITY BANK': 'Fidelity Bank Ghana Ltd',
            'CAL BANK': 'CAL Bank Ltd',
            'ZENITH BANK': 'Zenith Bank Ghana Ltd',
            'ACCESS BANK': 'Access Bank Ghana Ltd',
            'UBA': 'United Bank for Africa Ghana Ltd',
            'UNITED BANK FOR AFRICA': 'United Bank for Africa Ghana Ltd',
            'CITY SAVINGS & LOAN BANK': 'City Savings & Loan Bank',
            'NOT APPLICABLE': 'NO BANK SPECIFIED',
            'N/A': 'NO BANK SPECIFIED',
            '': 'NO BANK SPECIFIED'
        }

    def standardize_bank_name(self, bank_name: str) -> str:
        """Standardize bank names for consistent grouping"""
        if not bank_name or bank_name.strip() == '':
            return 'NO BANK SPECIFIED'
        
        bank_upper = bank_name.strip().upper()
        return self.bank_mapping.get(bank_upper, bank_name.strip())

    def process_multi_bank_data(self, session_data: Dict, data_type: str) -> Dict[str, Any]:
        """
        Process data with advanced multi-bank grouping and validation
        """
        start_time = time.time()
        
        print(f"[ADVANCED PROCESSOR] Processing {data_type} data with multi-bank optimization")
        
        try:
            # Get data from database
            raw_data = self._get_data_from_database(session_data, data_type)
            
            if not raw_data:
                return {
                    'success': False,
                    'error': f'No {data_type} data found',
                    'banks': {},
                    'stats': self.processing_stats
                }
            
            # Process with multi-threading for large datasets
            if len(raw_data) > 100:
                processed_data = self._process_large_dataset(raw_data, data_type)
            else:
                processed_data = self._process_standard_dataset(raw_data, data_type)
            
            # Advanced bank grouping with validation
            bank_groups = self._advanced_bank_grouping(processed_data, data_type)
            
            # Generate processing statistics
            self.processing_stats['total_employees'] = len(processed_data)
            self.processing_stats['total_banks'] = len(bank_groups)
            self.processing_stats['processing_time'] = time.time() - start_time
            
            print(f"[ADVANCED PROCESSOR] Completed: {len(processed_data)} records, {len(bank_groups)} banks")
            print(f"[ADVANCED PROCESSOR] Processing time: {self.processing_stats['processing_time']:.2f}s")
            
            return {
                'success': True,
                'data_type': data_type,
                'banks': bank_groups,
                'stats': self.processing_stats,
                'validation_results': self._validate_bank_groups(bank_groups)
            }
            
        except Exception as e:
            error_msg = f"Advanced processing failed: {str(e)}"
            print(f"[ERROR] {error_msg}")
            self.processing_stats['errors'].append(error_msg)
            
            return {
                'success': False,
                'error': error_msg,
                'banks': {},
                'stats': self.processing_stats
            }

    def _get_data_from_database(self, session_data: Dict, data_type: str) -> List[Dict]:
        """Get data from database with optimized queries"""
        try:
            import sqlite3
            
            db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'templar_payroll_auditor.db')
            if not os.path.exists(db_path):
                return []
            
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            session_id = session_data.get('session_id')
            if not session_id:
                return []
            
            # Optimized queries for each data type
            if data_type == 'final_adjusted':
                query = """
                    SELECT employee_no, employee_name, department, section, job_title,
                           net_pay, gross_salary, taxable_salary, total_deductions,
                           bank, account_no, branch, extraction_confidence
                    FROM final_adjusted_data 
                    WHERE session_id = ?
                    ORDER BY bank, employee_no
                """
            elif data_type == 'allowances':
                query = """
                    SELECT a.employee_no, a.employee_name, a.department,
                           a.allowance_type, a.gross_amount, a.tax_amount, a.payable_amount,
                           COALESCE(f.bank, 'NO BANK SPECIFIED') as bank, 
                           COALESCE(f.account_no, 'N/A') as account_no,
                           COALESCE(f.branch, '') as branch
                    FROM allowances_data a
                    LEFT JOIN final_adjusted_data f ON a.employee_no = f.employee_no AND a.session_id = f.session_id
                    WHERE a.session_id = ?
                    ORDER BY bank, a.employee_no, a.allowance_type
                """
            elif data_type == 'awards':
                query = """
                    SELECT a.employee_no, a.employee_name, a.department,
                           a.award_type, a.gross_amount, a.tax_amount, a.payable_amount, a.net_pay,
                           COALESCE(f.bank, 'NO BANK SPECIFIED') as bank,
                           COALESCE(f.account_no, 'N/A') as account_no,
                           COALESCE(f.branch, '') as branch
                    FROM awards_data a
                    LEFT JOIN final_adjusted_data f ON a.employee_no = f.employee_no AND a.session_id = f.session_id
                    WHERE a.session_id = ?
                    ORDER BY bank, a.employee_no, a.award_type
                """
            else:
                return []
            
            cursor.execute(query, [session_id])
            rows = cursor.fetchall()
            
            # Convert to list of dictionaries
            data = []
            for row in rows:
                record = dict(row)
                # Standardize bank name
                record['bank'] = self.standardize_bank_name(record.get('bank', ''))
                data.append(record)
            
            conn.close()
            
            if self.debug:
                print(f"[DATABASE] Retrieved {len(data)} {data_type} records")
            
            return data
            
        except Exception as e:
            print(f"[ERROR] Database query failed: {e}")
            return []

    def _process_large_dataset(self, data: List[Dict], data_type: str) -> List[Dict]:
        """Process large datasets with multi-threading"""
        print(f"[ADVANCED PROCESSOR] Using multi-threading for {len(data)} records")
        
        # Split data into chunks for parallel processing
        chunk_size = max(10, len(data) // self.max_workers)
        chunks = [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]
        
        processed_data = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit chunks for processing
            future_to_chunk = {
                executor.submit(self._process_data_chunk, chunk, data_type): chunk 
                for chunk in chunks
            }
            
            # Collect results
            for future in as_completed(future_to_chunk):
                try:
                    chunk_result = future.result()
                    processed_data.extend(chunk_result)
                except Exception as e:
                    print(f"[ERROR] Chunk processing failed: {e}")
                    self.processing_stats['errors'].append(f"Chunk processing error: {e}")
        
        return processed_data

    def _process_standard_dataset(self, data: List[Dict], data_type: str) -> List[Dict]:
        """Process standard datasets without multi-threading"""
        return self._process_data_chunk(data, data_type)

    def _process_data_chunk(self, chunk: List[Dict], data_type: str) -> List[Dict]:
        """Process a chunk of data with validation and enhancement"""
        processed_chunk = []
        
        for record in chunk:
            try:
                # Validate and enhance record
                enhanced_record = self._enhance_record(record, data_type)
                if enhanced_record:
                    processed_chunk.append(enhanced_record)
            except Exception as e:
                if self.debug:
                    print(f"[WARNING] Record processing failed: {e}")
                self.processing_stats['errors'].append(f"Record error: {e}")
        
        return processed_chunk

    def _enhance_record(self, record: Dict, data_type: str) -> Dict:
        """Enhance record with additional validation and formatting"""
        enhanced = record.copy()
        
        # Standardize bank name
        enhanced['bank'] = self.standardize_bank_name(record.get('bank', ''))
        
        # Validate required fields
        if data_type == 'final_adjusted':
            if not enhanced.get('employee_no') or not enhanced.get('net_pay'):
                return None
            enhanced['net_pay'] = float(enhanced.get('net_pay', 0))
            
        elif data_type in ['allowances', 'awards']:
            type_field = 'allowance_type' if data_type == 'allowances' else 'award_type'
            if not enhanced.get('employee_no') or not enhanced.get(type_field):
                return None
            
            enhanced['gross_amount'] = float(enhanced.get('gross_amount', 0))
            enhanced['tax_amount'] = float(enhanced.get('tax_amount', 0))
            enhanced['payable_amount'] = float(enhanced.get('payable_amount', 0))
        
        # Add processing metadata
        enhanced['processed_at'] = datetime.now().isoformat()
        enhanced['processor_version'] = '5.0'
        
        return enhanced

    def _advanced_bank_grouping(self, data: List[Dict], data_type: str) -> Dict[str, Any]:
        """Advanced bank grouping with statistics and validation"""
        bank_groups = {}
        
        for record in data:
            bank = record.get('bank', 'NO BANK SPECIFIED')
            
            if bank not in bank_groups:
                bank_groups[bank] = {
                    'bank_name': bank,
                    'records': [],
                    'total_amount': 0.0,
                    'employee_count': 0,
                    'departments': set(),
                    'sections': set()
                }
            
            bank_groups[bank]['records'].append(record)
            
            # Calculate totals
            if data_type == 'final_adjusted':
                bank_groups[bank]['total_amount'] += record.get('net_pay', 0)
            else:
                bank_groups[bank]['total_amount'] += record.get('payable_amount', 0)
            
            # Track unique employees
            bank_groups[bank]['departments'].add(record.get('department', ''))
            bank_groups[bank]['sections'].add(record.get('section', ''))
        
        # Convert sets to lists and calculate final statistics
        for bank, group in bank_groups.items():
            group['departments'] = list(group['departments'])
            group['sections'] = list(group['sections'])
            group['employee_count'] = len(set(r.get('employee_no') for r in group['records']))
            group['record_count'] = len(group['records'])
        
        return bank_groups

    def _validate_bank_groups(self, bank_groups: Dict) -> Dict[str, Any]:
        """Validate bank groups for data integrity"""
        validation_results = {
            'total_banks': len(bank_groups),
            'total_records': sum(group['record_count'] for group in bank_groups.values()),
            'total_employees': sum(group['employee_count'] for group in bank_groups.values()),
            'total_amount': sum(group['total_amount'] for group in bank_groups.values()),
            'banks_with_issues': [],
            'validation_passed': True
        }
        
        for bank_name, group in bank_groups.items():
            issues = []
            
            # Check for missing account numbers
            missing_accounts = sum(1 for r in group['records'] if r.get('account_no') in ['N/A', '', None])
            if missing_accounts > 0:
                issues.append(f"{missing_accounts} records missing account numbers")
            
            # Check for zero amounts
            zero_amounts = sum(1 for r in group['records'] if r.get('payable_amount', r.get('net_pay', 0)) == 0)
            if zero_amounts > 0:
                issues.append(f"{zero_amounts} records with zero amounts")
            
            if issues:
                validation_results['banks_with_issues'].append({
                    'bank': bank_name,
                    'issues': issues
                })
                validation_results['validation_passed'] = False
        
        return validation_results

def main():
    """Command line interface for Advanced Bank Processor"""
    if len(sys.argv) < 3:
        print("Usage: python advanced_bank_processor.py <session_data_file> <data_type>")
        print("Data types: final_adjusted, allowances, awards")
        sys.exit(1)
    
    session_data_file = sys.argv[1]
    data_type = sys.argv[2]
    
    # Load session data
    try:
        with open(session_data_file, 'r') as f:
            session_data = json.load(f)
    except Exception as e:
        print(f"[ERROR] Failed to load session data: {e}")
        sys.exit(1)
    
    # Initialize processor
    processor = AdvancedBankProcessor(max_workers=4, debug=True)
    
    # Process data
    result = processor.process_multi_bank_data(session_data, data_type)
    
    # Output results
    print(json.dumps(result, indent=2, default=str))

if __name__ == "__main__":
    main()
