#!/usr/bin/env python3
"""
Debug Dictionary Discrepancy
Investigates why Dictionary Manager UI shows items but database queries show empty dictionary.
"""

import sqlite3
import json
from pathlib import Path

def debug_dictionary_discrepancy():
    """Debug the discrepancy between UI display and database content"""
    
    print("🔍 DEBUGGING DICTIONARY DISCREPANCY")
    print("=" * 60)
    
    # Connect to database
    db_path = Path("payroll_audit.db")
    if not db_path.exists():
        print("❌ Database not found!")
        return
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 1. Check database tables structure
        print("\n1. 📊 DATABASE STRUCTURE ANALYSIS:")
        
        # Check if dictionary tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'dictionary%'")
        dict_tables = cursor.fetchall()
        
        print(f"Dictionary tables found: {[t[0] for t in dict_tables]}")
        
        if dict_tables:
            # Check dictionary_sections
            cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
            sections_count = cursor.fetchone()[0]
            print(f"Dictionary sections count: {sections_count}")
            
            if sections_count > 0:
                cursor.execute("SELECT id, section_name FROM dictionary_sections")
                sections = cursor.fetchall()
                print("Sections:")
                for section_id, section_name in sections:
                    print(f"  {section_id}: {section_name}")
            
            # Check dictionary_items
            cursor.execute("SELECT COUNT(*) FROM dictionary_items")
            items_count = cursor.fetchone()[0]
            print(f"Dictionary items count: {items_count}")
            
            if items_count > 0:
                cursor.execute("""
                    SELECT di.id, ds.section_name, di.item_name, di.include_in_report,
                           di.include_new, di.include_increase, di.include_decrease,
                           di.include_removed, di.include_no_change
                    FROM dictionary_items di
                    JOIN dictionary_sections ds ON di.section_id = ds.id
                    ORDER BY ds.section_name, di.item_name
                """)
                items = cursor.fetchall()
                print("Items:")
                for item in items:
                    item_id, section, name, include_report, new, inc, dec, rem, no_change = item
                    print(f"  {section}.{name}:")
                    print(f"    Include in report: {bool(include_report)}")
                    print(f"    Change detection: NEW={bool(new)}, INC={bool(inc)}, DEC={bool(dec)}, REM={bool(rem)}, NO_CHANGE={bool(no_change)}")
        
        # 2. Check if there are any JSON files that might be used
        print("\n2. 📁 FILE SYSTEM CHECK:")
        
        json_files = list(Path(".").glob("*dictionary*.json"))
        print(f"Dictionary JSON files found: {[str(f) for f in json_files]}")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                print(f"  {json_file}: {len(data)} sections" if isinstance(data, dict) else f"  {json_file}: {type(data)}")
            except Exception as e:
                print(f"  {json_file}: Error reading - {e}")
        
        # 3. Test the actual dictionary loading mechanism
        print("\n3. 🔧 TESTING DICTIONARY LOADING MECHANISM:")
        
        try:
            # Import the dictionary manager
            import sys
            sys.path.append('core')
            from dictionary_manager import PayrollDictionaryManager
            
            # Test database loading
            dict_manager = PayrollDictionaryManager(debug=True)
            success = dict_manager.load_dictionary()
            
            print(f"Dictionary loading success: {success}")
            
            if success and dict_manager.dictionary:
                print(f"Loaded dictionary sections: {list(dict_manager.dictionary.keys())}")
                
                total_items = 0
                for section_name, section_data in dict_manager.dictionary.items():
                    items = section_data.get('items', {})
                    total_items += len(items)
                    print(f"  {section_name}: {len(items)} items")
                    
                    # Show first few items
                    for i, (item_name, item_data) in enumerate(items.items()):
                        if i < 3:  # Show first 3 items
                            include_report = item_data.get('include_in_report', True)
                            print(f"    - {item_name} (include: {include_report})")
                        elif i == 3:
                            print(f"    ... and {len(items) - 3} more items")
                            break
                
                print(f"Total items loaded: {total_items}")
            else:
                print("❌ Dictionary loading failed or empty")
        
        except Exception as e:
            print(f"❌ Error testing dictionary manager: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Check if there's a default dictionary creation
        print("\n4. 🏗️ DEFAULT DICTIONARY ANALYSIS:")
        
        try:
            # Test default dictionary creation
            dict_manager = PayrollDictionaryManager(debug=True)
            dict_manager._create_default_dictionary()
            
            if dict_manager.dictionary:
                print("Default dictionary created successfully:")
                for section_name, section_data in dict_manager.dictionary.items():
                    items = section_data.get('items', {})
                    print(f"  {section_name}: {len(items)} items")
                
                # Try to save it to database
                print("\nAttempting to save default dictionary to database...")
                save_success = dict_manager.save_dictionary()
                print(f"Save success: {save_success}")
                
                if save_success:
                    # Re-check database
                    cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
                    new_sections_count = cursor.fetchone()[0]
                    cursor.execute("SELECT COUNT(*) FROM dictionary_items")
                    new_items_count = cursor.fetchone()[0]
                    
                    print(f"After save - Sections: {new_sections_count}, Items: {new_items_count}")
        
        except Exception as e:
            print(f"❌ Error with default dictionary: {e}")
        
        # 5. Check database manager connection
        print("\n5. 🔗 DATABASE MANAGER CONNECTION CHECK:")
        
        try:
            sys.path.append('core')
            from database_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            is_connected = db_manager.is_connected()
            print(f"Database manager connected: {is_connected}")
            
            if is_connected:
                # Test dictionary loading through database manager
                dictionary_data = db_manager.get_dictionary_items()
                if dictionary_data:
                    print(f"Database manager loaded dictionary: {len(dictionary_data)} sections")
                else:
                    print("Database manager returned empty dictionary")
        
        except Exception as e:
            print(f"❌ Error testing database manager: {e}")
        
        # 6. Summary and recommendations
        print("\n6. 📋 SUMMARY AND RECOMMENDATIONS:")
        
        if sections_count == 0 and items_count == 0:
            print("❌ CONFIRMED: Database dictionary tables are empty")
            print("💡 LIKELY CAUSES:")
            print("   1. Dictionary Manager UI is using in-memory/cached data")
            print("   2. Dictionary Manager UI is loading from JSON files")
            print("   3. Dictionary Manager UI is creating default data that's not saved")
            print("   4. Database connection issues preventing saves")
            
            print("\n🔧 RECOMMENDED ACTIONS:")
            print("   1. Check if Dictionary Manager UI has unsaved changes")
            print("   2. Try manually saving the dictionary from the UI")
            print("   3. Check browser console for save errors")
            print("   4. Verify database write permissions")
        else:
            print("✅ Database contains dictionary data")
            print("🔍 Need to investigate why pre-reporting queries don't see it")
    
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

if __name__ == "__main__":
    debug_dictionary_discrepancy()
