#!/usr/bin/env python3
"""
Intelligent Cache Manager
Implements smart caching for repeated operations and dictionary lookups
"""

import time
import threading
from typing import Any, Dict, Optional, Callable, Tuple
from functools import wraps, lru_cache
from dataclasses import dataclass
import hashlib
import json

@dataclass
class CacheStats:
    """Cache performance statistics"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    total_requests: int = 0
    hit_rate: float = 0.0
    average_lookup_time: float = 0.0

class IntelligentCacheManager:
    """
    Intelligent cache manager with TTL, LRU eviction, and performance monitoring
    """
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_times = {}
        self.expiry_times = {}
        self.stats = CacheStats()
        self.lock = threading.RLock()
        
        # Performance tracking
        self.lookup_times = []
        self.max_lookup_history = 1000
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache with performance tracking
        """
        start_time = time.time()
        
        with self.lock:
            self.stats.total_requests += 1
            
            # Check if key exists and is not expired
            if key in self.cache:
                if time.time() < self.expiry_times.get(key, 0):
                    # Cache hit
                    self.access_times[key] = time.time()
                    self.stats.hits += 1
                    value = self.cache[key]
                else:
                    # Expired entry
                    self._remove_key(key)
                    self.stats.misses += 1
                    value = default
            else:
                # Cache miss
                self.stats.misses += 1
                value = default
            
            # Update performance stats
            lookup_time = time.time() - start_time
            self.lookup_times.append(lookup_time)
            if len(self.lookup_times) > self.max_lookup_history:
                self.lookup_times.pop(0)
            
            self._update_hit_rate()
            
            return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set value in cache with TTL
        """
        with self.lock:
            # Use default TTL if not specified
            ttl = ttl or self.default_ttl
            
            # Check if we need to evict entries
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            # Set the value
            self.cache[key] = value
            self.access_times[key] = time.time()
            self.expiry_times[key] = time.time() + ttl
    
    def invalidate(self, key: str) -> bool:
        """
        Invalidate a specific cache entry
        """
        with self.lock:
            if key in self.cache:
                self._remove_key(key)
                return True
            return False
    
    def clear(self) -> None:
        """
        Clear all cache entries
        """
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.expiry_times.clear()
            self.stats.evictions += len(self.cache)
    
    def _remove_key(self, key: str) -> None:
        """
        Remove key from all cache structures
        """
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
        self.expiry_times.pop(key, None)
    
    def _evict_lru(self) -> None:
        """
        Evict least recently used entry
        """
        if not self.access_times:
            return
        
        # Find LRU key
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self._remove_key(lru_key)
        self.stats.evictions += 1
    
    def _update_hit_rate(self) -> None:
        """
        Update hit rate statistics
        """
        if self.stats.total_requests > 0:
            self.stats.hit_rate = self.stats.hits / self.stats.total_requests
        
        if self.lookup_times:
            self.stats.average_lookup_time = sum(self.lookup_times) / len(self.lookup_times)
    
    def get_stats(self) -> CacheStats:
        """
        Get cache performance statistics
        """
        with self.lock:
            return CacheStats(
                hits=self.stats.hits,
                misses=self.stats.misses,
                evictions=self.stats.evictions,
                total_requests=self.stats.total_requests,
                hit_rate=self.stats.hit_rate,
                average_lookup_time=self.stats.average_lookup_time
            )
    
    def cleanup_expired(self) -> int:
        """
        Clean up expired entries
        """
        with self.lock:
            current_time = time.time()
            expired_keys = [
                key for key, expiry in self.expiry_times.items()
                if current_time >= expiry
            ]
            
            for key in expired_keys:
                self._remove_key(key)
            
            return len(expired_keys)

# Global cache instance
_global_cache = IntelligentCacheManager()

def cached_function(ttl: int = 3600, cache_instance: Optional[IntelligentCacheManager] = None):
    """
    Decorator for caching function results
    
    Args:
        ttl: Time to live in seconds
        cache_instance: Optional cache instance (uses global if None)
    """
    def decorator(func: Callable) -> Callable:
        cache = cache_instance or _global_cache
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            key_data = {
                'func': func.__name__,
                'args': args,
                'kwargs': kwargs
            }
            cache_key = hashlib.md5(json.dumps(key_data, sort_keys=True, default=str).encode()).hexdigest()
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            
            return result
        
        # Add cache management methods to function
        wrapper.cache_clear = lambda: cache.clear()
        wrapper.cache_stats = lambda: cache.get_stats()
        wrapper.cache_invalidate = lambda *args, **kwargs: cache.invalidate(
            hashlib.md5(json.dumps({
                'func': func.__name__,
                'args': args,
                'kwargs': kwargs
            }, sort_keys=True, default=str).encode()).hexdigest()
        )
        
        return wrapper
    
    return decorator

# Cached dictionary operations
@cached_function(ttl=7200)  # Cache for 2 hours
def classify_loan_type_cached(loan_name: str, dictionary_manager=None) -> str:
    """
    Cached loan type classification - DICTIONARY ONLY (no keyword fallback)
    """
    if dictionary_manager:
        return dictionary_manager.classify_loan_type(loan_name)

    # CRITICAL FIX: No keyword fallback - only dictionary-based classification
    # If no dictionary manager provided, default to EXTERNAL (safer assumption)
    return 'EXTERNAL LOAN'

@cached_function(ttl=3600)  # Cache for 1 hour
def extract_amount_cached(value: Any) -> float:
    """
    Cached amount extraction
    """
    import re
    
    if isinstance(value, (int, float)):
        return float(value)
    
    value_str = str(value).replace(',', '')
    
    # Extract first number found
    match = re.search(r'([0-9]+\.?[0-9]*)', value_str)
    if match:
        try:
            return float(match.group(1))
        except ValueError:
            pass
    
    return 0.0

@cached_function(ttl=1800)  # Cache for 30 minutes
def get_section_priority_cached(section: str) -> str:
    """
    Cached section priority lookup
    """
    section_priorities = {
        'PERSONAL': 'HIGH',
        'PERSONAL DETAILS': 'HIGH',
        'EARNINGS': 'HIGH',
        'DEDUCTIONS': 'HIGH',
        'BANK DETAILS': 'HIGH',
        'BANK': 'HIGH',
        'LOANS': 'MODERATE',
        'EMPLOYER CONTRIBUTIONS': 'LOW',
        'EMPLOYERS CONTRIBUTION': 'LOW'
    }
    return section_priorities.get(section.upper(), 'MODERATE')

class PerformanceMonitor:
    """
    Monitor and report cache performance
    """
    
    def __init__(self):
        self.start_time = time.time()
        self.operation_counts = {}
        self.operation_times = {}
    
    def record_operation(self, operation: str, execution_time: float):
        """
        Record operation performance
        """
        if operation not in self.operation_counts:
            self.operation_counts[operation] = 0
            self.operation_times[operation] = []
        
        self.operation_counts[operation] += 1
        self.operation_times[operation].append(execution_time)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        Generate performance report
        """
        total_time = time.time() - self.start_time
        cache_stats = _global_cache.get_stats()
        
        operation_stats = {}
        for op, times in self.operation_times.items():
            operation_stats[op] = {
                'count': self.operation_counts[op],
                'total_time': sum(times),
                'average_time': sum(times) / len(times) if times else 0,
                'min_time': min(times) if times else 0,
                'max_time': max(times) if times else 0
            }
        
        return {
            'total_runtime': total_time,
            'cache_performance': {
                'hit_rate': cache_stats.hit_rate,
                'total_requests': cache_stats.total_requests,
                'hits': cache_stats.hits,
                'misses': cache_stats.misses,
                'evictions': cache_stats.evictions,
                'average_lookup_time': cache_stats.average_lookup_time
            },
            'operation_performance': operation_stats,
            'cache_efficiency': 'EXCELLENT' if cache_stats.hit_rate > 0.8 else 
                              'GOOD' if cache_stats.hit_rate > 0.6 else 
                              'NEEDS_IMPROVEMENT'
        }

# Global performance monitor
performance_monitor = PerformanceMonitor()

def get_global_cache() -> IntelligentCacheManager:
    """
    Get global cache instance
    """
    return _global_cache

def cleanup_all_caches():
    """
    Cleanup all caches and expired entries
    """
    expired_count = _global_cache.cleanup_expired()
    print(f"✅ Cleaned up {expired_count} expired cache entries")
    
    # Clear function-specific caches
    classify_loan_type_cached.cache_clear()
    extract_amount_cached.cache_clear()
    get_section_priority_cached.cache_clear()
    
    print("✅ All caches cleaned up")
