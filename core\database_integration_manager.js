/**
 * <PERSON>AT<PERSON>ASE INTEGRATION MANAGER
 * Coordinates SQLite database integration across ALL TEMPLAR PAYROLL AUDITOR modules
 * Solves UI freezing, slowing, and flickering by implementing async database operations
 */

const UnifiedDatabase = require('./unified_database');
const EventEmitter = require('events');

class DatabaseIntegrationManager extends EventEmitter {
    constructor() {
        super();
        this.database = null;
        this.isInitialized = false;
        this.activeOperations = new Map();
        this.performanceMonitor = {
            operationCount: 0,
            totalExecutionTime: 0,
            averageExecutionTime: 0
        };

        console.log('🔗 DATABASE INTEGRATION MANAGER INITIALIZING...');
    }

    /**
     * Initialize the unified database system
     */
    async initialize() {
        try {
            console.log('🚀 INITIALIZING UNIFIED DATABASE SYSTEM...');

            // Initialize unified database
            this.database = new UnifiedDatabase();
            await this.database.initializeDatabase();

            // Setup performance monitoring
            this.setupPerformanceMonitoring();

            // Migrate existing data if needed
            await this.migrateExistingData();

            this.isInitialized = true;
            console.log('✅ DATABASE INTEGRATION MANAGER READY');

            this.emit('initialized', {
                success: true,
                message: 'Unified database system ready',
                capabilities: {
                    modules: ['Payroll Audit', 'Dictionary Manager', 'Auto Learning', 'Report Manager', 'PDF Sorter', 'Data Builder'],
                    performance: 'Optimized for 2900+ employees',
                    uiBlocking: 'Eliminated',
                    transactionSupport: 'Full ACID compliance'
                }
            });

            return { success: true };

        } catch (error) {
            console.error('❌ Database integration initialization failed:', error);
            this.emit('error', { error: error.message });
            return { success: false, error: error.message };
        }
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        // Monitor database operations
        setInterval(async () => {
            if (this.isInitialized) {
                try {
                    const stats = await this.database.getPerformanceStats();
                    this.emit('performance-update', {
                        timestamp: new Date().toISOString(),
                        stats: stats,
                        activeOperations: this.activeOperations.size
                    });
                } catch (error) {
                    console.warn('Performance monitoring error:', error);
                }
            }
        }, 30000); // Every 30 seconds
    }

    /**
     * Migrate existing file-based data to database
     */
    async migrateExistingData() {
        console.log('🔄 CHECKING FOR DATA MIGRATION...');

        try {
            // Check if migration is needed
            const stats = await this.database.getSystemStatistics();
            const migrationNeeded = await this.checkIfMigrationNeeded();

            if (migrationNeeded.hasOldFiles) {
                console.log('🚀 PERFORMING AUTOMATIC MIGRATION...');

                // Use the comprehensive migration manager
                const StorageMigrationManager = require('./storage_migration_manager');
                const migrationManager = new StorageMigrationManager();
                await migrationManager.initialize();

                const migrationResult = await migrationManager.performCompleteMigration();
                await migrationManager.close();

                if (migrationResult.success) {
                    console.log('✅ AUTOMATIC MIGRATION COMPLETED SUCCESSFULLY');
                    this.emit('migration-completed', {
                        success: true,
                        message: 'All data migrated to unified database',
                        migrationLog: migrationResult.migrationLog
                    });
                } else {
                    console.error('❌ AUTOMATIC MIGRATION FAILED:', migrationResult.error);
                    this.emit('migration-failed', {
                        success: false,
                        error: migrationResult.error
                    });
                }
            } else {
                console.log('✅ No migration needed - using unified database');
            }

        } catch (error) {
            console.warn('⚠️ Data migration warning:', error);
            this.emit('migration-error', { error: error.message });
        }
    }

    /**
     * Check if migration is needed
     */
    async checkIfMigrationNeeded() {
        const fs = require('fs');
        const path = require('path');

        const oldFiles = [
            path.join(__dirname, '..', 'data', 'payroll_dictionary.json'),
            path.join(__dirname, '..', 'data', 'app_settings.json'),
            path.join(__dirname, '..', 'data', 'reports')
        ];

        const existingFiles = oldFiles.filter(file => fs.existsSync(file));

        return {
            hasOldFiles: existingFiles.length > 0,
            existingFiles: existingFiles,
            migrationRecommended: existingFiles.length > 0
        };
    }

    /**
     * Migrate dictionary data from JSON files
     */
    async migrateDictionaryData() {
        const fs = require('fs');
        const path = require('path');

        const dictionaryPath = path.join(__dirname, '..', 'data', 'payroll_dictionary.json');

        if (fs.existsSync(dictionaryPath)) {
            try {
                const dictionaryData = JSON.parse(fs.readFileSync(dictionaryPath, 'utf8'));
                await this.database.saveDictionary(dictionaryData);
                console.log('✅ Dictionary data migrated successfully');
            } catch (error) {
                console.error('❌ Dictionary migration failed:', error);
            }
        }
    }

    /**
     * Migrate report data from JSON files
     */
    async migrateReportData() {
        const fs = require('fs');
        const path = require('path');

        const reportsDir = path.join(__dirname, '..', 'data', 'reports');

        if (fs.existsSync(reportsDir)) {
            try {
                const reportFiles = fs.readdirSync(reportsDir).filter(file => file.endsWith('.json'));

                for (const file of reportFiles) {
                    const reportPath = path.join(reportsDir, file);
                    const reportData = JSON.parse(fs.readFileSync(reportPath, 'utf8'));

                    // Convert to database format
                    const dbReport = {
                        report_id: reportData.id || file.replace('.json', ''),
                        report_type: reportData.type || 'unknown',
                        report_category: this.determineReportCategory(reportData),
                        title: reportData.title || 'Migrated Report',
                        description: reportData.description || '',
                        file_paths: reportData.report_paths || {},
                        metadata: reportData.metadata || {},
                        file_size: reportData.file_size || 0
                    };

                    await this.database.saveReport(dbReport);
                }

                console.log(`✅ Migrated ${reportFiles.length} reports`);
            } catch (error) {
                console.error('❌ Report migration failed:', error);
            }
        }
    }

    /**
     * Determine report category from report data
     */
    determineReportCategory(reportData) {
        if (reportData.type && reportData.type.includes('audit')) return 'Payroll_Audit_Reports';
        if (reportData.type && reportData.type.includes('sort')) return 'PDF_Sorter_Reports';
        if (reportData.type && reportData.type.includes('data')) return 'Data_Builder_Reports';
        return 'General_Reports';
    }

    // ========== MODULE INTEGRATION METHODS ==========

    /**
     * Payroll Audit Integration
     */
    async integratePayrollAudit(sessionId, pdfPath, employees, extractedItems) {
        const operationId = `payroll_audit_${Date.now()}`;
        const startTime = Date.now();

        try {
            this.activeOperations.set(operationId, 'Payroll Audit Processing');

            // Create session
            await this.database.createPayrollSession(sessionId, pdfPath, null);

            // Store employees and items in batches to prevent UI blocking
            const batchSize = 100;

            for (let i = 0; i < employees.length; i += batchSize) {
                const batch = employees.slice(i, i + batchSize);
                await this.database.storeEmployees(sessionId, batch);

                // Emit progress update
                this.emit('progress-update', {
                    module: 'Payroll Audit',
                    operation: 'Storing Employees',
                    progress: Math.min(100, ((i + batch.length) / employees.length) * 50),
                    processed: i + batch.length,
                    total: employees.length
                });

                // Yield control to prevent UI blocking
                await new Promise(resolve => setImmediate(resolve));
            }

            // Store extracted items in batches
            for (let i = 0; i < extractedItems.length; i += batchSize) {
                const batch = extractedItems.slice(i, i + batchSize);
                await this.database.storeExtractedItems(sessionId, batch);

                // Emit progress update
                this.emit('progress-update', {
                    module: 'Payroll Audit',
                    operation: 'Storing Extracted Items',
                    progress: 50 + Math.min(50, ((i + batch.length) / extractedItems.length) * 50),
                    processed: i + batch.length,
                    total: extractedItems.length
                });

                // Yield control to prevent UI blocking
                await new Promise(resolve => setImmediate(resolve));
            }

            // Record performance
            const executionTime = Date.now() - startTime;
            await this.database.recordPerformanceMetric(
                'Payroll Audit', 'Full Processing', executionTime,
                process.memoryUsage().heapUsed / 1024 / 1024,
                employees.length + extractedItems.length, 1.0
            );

            this.activeOperations.delete(operationId);

            return {
                success: true,
                sessionId: sessionId,
                employeesStored: employees.length,
                itemsStored: extractedItems.length,
                executionTime: executionTime
            };

        } catch (error) {
            this.activeOperations.delete(operationId);
            console.error('❌ Payroll Audit integration error:', error);
            throw error;
        }
    }

    /**
     * Dictionary Manager Integration
     */
    async integrateDictionaryManager(dictionary) {
        const operationId = `dictionary_${Date.now()}`;
        const startTime = Date.now();

        try {
            this.activeOperations.set(operationId, 'Dictionary Management');

            await this.database.saveDictionary(dictionary);

            const executionTime = Date.now() - startTime;
            await this.database.recordPerformanceMetric(
                'Dictionary Manager', 'Save Dictionary', executionTime,
                process.memoryUsage().heapUsed / 1024 / 1024,
                Object.keys(dictionary).length, 1.0
            );

            this.activeOperations.delete(operationId);

            this.emit('dictionary-updated', {
                success: true,
                sections: Object.keys(dictionary).length,
                executionTime: executionTime
            });

            return { success: true };

        } catch (error) {
            this.activeOperations.delete(operationId);
            console.error('❌ Dictionary Manager integration error:', error);
            throw error;
        }
    }

    /**
     * Auto Learning Integration
     */
    async integrateAutoLearning(sessionId, pendingItems) {
        const operationId = `auto_learning_${Date.now()}`;
        const startTime = Date.now();

        try {
            this.activeOperations.set(operationId, 'Auto Learning Processing');

            // Process items in batches
            const batchSize = 50;
            let processed = 0;

            for (let i = 0; i < pendingItems.length; i += batchSize) {
                const batch = pendingItems.slice(i, i + batchSize);

                for (const item of batch) {
                    await this.database.addPendingItem(sessionId, item);
                    processed++;

                    // Emit real-time update
                    this.emit('auto-learning-update', {
                        type: 'item_added',
                        item: item,
                        progress: (processed / pendingItems.length) * 100
                    });
                }

                // Yield control to prevent UI blocking
                await new Promise(resolve => setImmediate(resolve));
            }

            const executionTime = Date.now() - startTime;
            await this.database.recordPerformanceMetric(
                'Auto Learning', 'Process Pending Items', executionTime,
                process.memoryUsage().heapUsed / 1024 / 1024,
                pendingItems.length, 1.0
            );

            this.activeOperations.delete(operationId);

            return {
                success: true,
                itemsProcessed: pendingItems.length,
                executionTime: executionTime
            };

        } catch (error) {
            this.activeOperations.delete(operationId);
            console.error('❌ Auto Learning integration error:', error);
            throw error;
        }
    }

    /**
     * Get database statistics for all modules
     */
    async getComprehensiveStats() {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }

        return await this.database.getSystemStatistics();
    }

    /**
     * Clean up old data
     */
    async performMaintenance(daysOld = 30) {
        if (!this.isInitialized) {
            throw new Error('Database not initialized');
        }

        console.log('🧹 Performing database maintenance...');

        const result = await this.database.cleanOldData(daysOld);

        console.log('✅ Database maintenance completed');

        this.emit('maintenance-completed', {
            success: true,
            recordsProcessed: result.processed
        });

        return result;
    }

    /**
     * Close database connection
     */
    async close() {
        if (this.database) {
            await this.database.close();
            this.isInitialized = false;
            console.log('🗄️ Database Integration Manager closed');
        }
    }
}

module.exports = DatabaseIntegrationManager;
