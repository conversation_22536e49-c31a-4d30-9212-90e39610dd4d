#!/usr/bin/env python3
"""
Test Pre-reporting Generate Report Buttons
Comprehensive test to verify that the Generate Report buttons in Interactive Pre-reporting UI
actually generate reports and send them to the Report Manager.
"""

import sqlite3
import json
import os
import sys
from pathlib import Path
from datetime import datetime

def test_pre_reporting_generate_report_flow():
    """Test the complete flow from pre-reporting to report generation"""
    
    print("🧪 TESTING PRE-REPORTING GENERATE REPORT BUTTONS")
    print("=" * 60)
    
    # 1. Check if pre-reporting data exists
    print("\n1. 📊 CHECKING PRE-REPORTING DATA:")
    
    db_path = Path("payroll_audit.db")
    if not db_path.exists():
        print("   ❌ Database not found")
        return False
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # Check for pre-reporting results
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results")
        pre_reporting_count = cursor.fetchone()[0]
        
        print(f"   Pre-reporting results: {pre_reporting_count} records")
        
        if pre_reporting_count == 0:
            print("   ⚠️ No pre-reporting data found - creating test data")
            create_test_pre_reporting_data(cursor)
            conn.commit()
            
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results")
            pre_reporting_count = cursor.fetchone()[0]
            print(f"   Created test data: {pre_reporting_count} records")
        
        # Get current session
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY session_id DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("   ❌ No session found in pre-reporting data")
            return False
        
        current_session = session_result[0]
        print(f"   Current session: {current_session}")
        
        # 2. Test the report generation API
        print("\n2. 🔧 TESTING REPORT GENERATION API:")
        
        # Test the phased process manager API
        try:
            sys.path.append('core')
            from phased_process_manager import PhasedProcessManager
            
            print("   ✅ PhasedProcessManager imported successfully")
            
            # Initialize manager
            manager = PhasedProcessManager(debug_mode=True)
            print("   ✅ PhasedProcessManager initialized")
            
            # Test generate_final_reports method
            test_config = {
                'session_id': current_session,
                'selected_changes': [],  # Will be populated from pre-reporting
                'report_formats': ['WORD', 'PDF', 'EXCEL'],
                'include_signature': True,
                'report_type': 'comprehensive'
            }
            
            print(f"   Testing with config: {test_config}")
            
            # Get selected changes from pre-reporting
            cursor.execute("""
                SELECT id, session_id, change_id, section, item_label, change_type, priority_level
                FROM pre_reporting_results 
                WHERE session_id = ? AND is_selected = 1
                LIMIT 10
            """, (current_session,))
            
            selected_changes = cursor.fetchall()
            test_config['selected_changes'] = [
                {
                    'id': row[0],
                    'session_id': row[1],
                    'change_id': row[2],
                    'section': row[3],
                    'item_label': row[4],
                    'change_type': row[5],
                    'priority_level': row[6]
                }
                for row in selected_changes
            ]
            
            print(f"   Selected changes for report: {len(test_config['selected_changes'])}")
            
            # Test the API call (method expects only session_id)
            result = manager.generate_final_reports(current_session)
            print(f"   Report generation result: {result}")
            
            if result.get('success'):
                print("   ✅ Report generation API working")
                
                # Check if reports were stored
                generated_reports = result.get('generated_reports', [])
                print(f"   Generated reports: {len(generated_reports)}")
                
                for report_type, file_path in generated_reports:
                    print(f"     - {report_type}: {file_path}")
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"       ✅ File exists ({file_size} bytes)")
                    else:
                        print(f"       ❌ File not found")
            else:
                print(f"   ❌ Report generation failed: {result.get('error')}")
                
        except Exception as e:
            print(f"   ❌ Report generation API test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 3. Check Report Manager integration
        print("\n3. 📋 CHECKING REPORT MANAGER INTEGRATION:")
        
        # Check if generated_reports table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='generated_reports'")
        if cursor.fetchone():
            print("   ✅ generated_reports table exists")
            
            cursor.execute("SELECT COUNT(*) FROM generated_reports")
            report_count = cursor.fetchone()[0]
            print(f"   Stored reports: {report_count}")
            
            if report_count > 0:
                cursor.execute("""
                    SELECT session_id, report_type, file_path, file_size, report_metadata
                    FROM generated_reports 
                    ORDER BY id DESC 
                    LIMIT 5
                """)
                
                recent_reports = cursor.fetchall()
                print("   Recent reports:")
                for session, rtype, fpath, fsize, metadata in recent_reports:
                    print(f"     - {rtype}: {fpath} ({fsize} bytes)")
        else:
            print("   ❌ generated_reports table not found")
        
        # Check if reports table exists (unified report manager)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='reports'")
        if cursor.fetchone():
            print("   ✅ reports table exists (unified report manager)")
            
            cursor.execute("SELECT COUNT(*) FROM reports")
            unified_report_count = cursor.fetchone()[0]
            print(f"   Unified reports: {unified_report_count}")
            
            if unified_report_count > 0:
                cursor.execute("""
                    SELECT report_id, report_type, report_category, title, file_paths
                    FROM reports 
                    ORDER BY created_at DESC 
                    LIMIT 5
                """)
                
                recent_unified_reports = cursor.fetchall()
                print("   Recent unified reports:")
                for rid, rtype, category, title, fpaths in recent_unified_reports:
                    print(f"     - {rid}: {rtype} ({category}) - {title}")
        else:
            print("   ❌ reports table not found (unified report manager)")
        
        # 4. Test the UI integration points
        print("\n4. 🖥️ TESTING UI INTEGRATION POINTS:")
        
        # Check if the JavaScript methods exist
        ui_file = Path("ui/interactive_pre_reporting.js")
        if ui_file.exists():
            with open(ui_file, 'r', encoding='utf-8', errors='ignore') as f:
                ui_content = f.read()
            
            # Check for key methods
            methods_to_check = [
                'proceedToReportGeneration',
                'generateFinalReports',
                'renderSharedConfigPanel'
            ]
            
            for method in methods_to_check:
                if method in ui_content:
                    print(f"   ✅ {method} method found in UI")
                else:
                    print(f"   ❌ {method} method missing in UI")
        else:
            print("   ❌ Interactive pre-reporting UI file not found")
        
        # Check main.js IPC handlers
        main_file = Path("main.js")
        if main_file.exists():
            with open(main_file, 'r', encoding='utf-8', errors='ignore') as f:
                main_content = f.read()
            
            # Check for IPC handlers
            handlers_to_check = [
                'generate-final-reports',
                'get-saved-reports',
                'view-report'
            ]
            
            for handler in handlers_to_check:
                if handler in main_content:
                    print(f"   ✅ {handler} IPC handler found")
                else:
                    print(f"   ❌ {handler} IPC handler missing")
        else:
            print("   ❌ main.js file not found")
        
        # 5. Test the complete workflow
        print("\n5. 🔄 TESTING COMPLETE WORKFLOW:")
        
        print("   Workflow steps:")
        print("   1. User clicks 'Generate Report' in pre-reporting UI")
        print("   2. UI calls proceedToReportGeneration() method")
        print("   3. Method calls window.api.generateFinalReports()")
        print("   4. Electron IPC handler 'generate-final-reports' is triggered")
        print("   5. Handler calls phased_process_manager.py generate-final-reports")
        print("   6. Python generates reports and stores metadata")
        print("   7. Reports appear in Report Manager")
        
        # Simulate the workflow
        print("\n   🧪 SIMULATING WORKFLOW:")
        
        # Step 1-4: Already tested above
        # Step 5: Test Python API directly
        try:
            import subprocess
            
            result = subprocess.run([
                sys.executable, 'core/phased_process_manager.py', 
                'generate-final-reports', 
                json.dumps(test_config)
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("   ✅ Python API call successful")
                try:
                    api_result = json.loads(result.stdout)
                    print(f"   API result: {api_result}")
                except:
                    print(f"   Raw output: {result.stdout[:200]}...")
            else:
                print(f"   ❌ Python API call failed: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ Workflow simulation failed: {e}")
        
        # 6. Final assessment
        print("\n6. 📋 FINAL ASSESSMENT:")
        
        issues_found = []
        
        if pre_reporting_count == 0:
            issues_found.append("No pre-reporting data available")
        
        # Check if report generation is working
        try:
            cursor.execute("SELECT COUNT(*) FROM generated_reports WHERE session_id = ?", (current_session,))
            session_reports = cursor.fetchone()[0]
            
            if session_reports == 0:
                issues_found.append("No reports generated for current session")
        except:
            issues_found.append("Cannot check generated reports")
        
        if issues_found:
            print("   ❌ ISSUES FOUND:")
            for issue in issues_found:
                print(f"     - {issue}")
            return False
        else:
            print("   ✅ ALL CHECKS PASSED")
            print("   🎯 Generate Report buttons should work correctly")
            return True
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        conn.close()

def create_test_pre_reporting_data(cursor):
    """Create test pre-reporting data for testing"""
    
    # Create a test session
    session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Insert test pre-reporting results
    test_data = [
        (session_id, 1, 'EARNINGS', 'BASIC SALARY', 'INCREASED', 'HIGH', 1),
        (session_id, 2, 'DEDUCTIONS', 'PAYE', 'DECREASED', 'MEDIUM', 1),
        (session_id, 3, 'LOANS', 'STAFF LOAN', 'NEW', 'HIGH', 1),
        (session_id, 4, 'ALLOWANCES', 'TRANSPORT', 'REMOVED', 'LOW', 0),
        (session_id, 5, 'EARNINGS', 'OVERTIME', 'INCREASED', 'MEDIUM', 1),
    ]
    
    for data in test_data:
        cursor.execute("""
            INSERT INTO pre_reporting_results 
            (session_id, change_id, section, item_label, change_type, priority_level, is_selected)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, data)

if __name__ == "__main__":
    success = test_pre_reporting_generate_report_flow()
    sys.exit(0 if success else 1)
