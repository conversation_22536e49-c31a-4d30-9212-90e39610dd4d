"""
CLEAN PAYROLL DICTIONARY MANAGER - NO UNICODE CORRUPTION
========================================================

This module provides 100% accurate payroll dictionary management for the hybrid extraction system.
All Unicode characters have been removed to prevent terminal corruption.

Features:
- Section-based dictionary management (6 sections)
- Auto-learning with real-time updates
- Standardization and validation
- Enterprise-grade merge strategies
- Clean ASCII-only output

Author: Payroll Auditor System
Version: 2.0 (Unicode-Free)
"""

import json
import os
import sys
import re
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# Global configuration
DICTIONARY_PATH = os.path.join(os.path.dirname(__file__), 'payroll_dictionary.json')
AUTO_LEARNING_PATH = os.path.join(os.path.dirname(__file__), 'auto_learning_data.json')

# Global data structures
REAL_TIME_DATA = {
    'pending_items': [],
    'approved_items': [],
    'rejected_items': [],
    'session_stats': {
        'items_detected': 0,
        'items_approved': 0,
        'items_rejected': 0,
        'accuracy_rate': 100.0
    }
}

AUTO_LEARNING_QUEUE = {}
ACTIVITY_LOG = []

# Auto-learning configuration
AUTO_LEARNING_CONFIG = {
    'require_100_percent': True,
    'confidence_threshold': 1.0,
    'auto_approval_threshold': 1.0,
    'max_improvement_iterations': 15,
    'enable_ai_classification': False,
    'enable_pattern_learning': True,
    'enable_comprehensive_testing': True
}

# Default dictionary structure with all 6 required sections
# MOCK.txt COMPLIANCE: Fixed items marked with is_fixed=True (cannot be deleted)
DEFAULT_DICTIONARY = {
    "PERSONAL DETAILS": {
        "items": {
            "Employee No.": {
                "format": "[TC][.]",
                "value_format": "Alphanumeric",
                "include_in_report": True,
                "standard_key": "employee_id",
                "is_fixed": True,  # MOCK.txt: Fixed item - cannot be deleted
                "validation_rules": {
                    "pattern": "^(COP|PW|SEC|E|PTS|PGH)\\d{4}$",
                    "required": True,
                    "data_type": "string"
                }
            },
            "Employee Name": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True,
                "standard_key": "employee_name",
                "is_fixed": True,  # MOCK.txt: Fixed item - cannot be deleted
                "validation_rules": {
                    "pattern": "^[A-Z][A-Za-z\\s\\.\\-]{2,50}$",
                    "required": True,
                    "data_type": "string"
                }
            },
            "Department": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True,
                "standard_key": "department",
                "is_fixed": True,  # MOCK.txt: Fixed item - cannot be deleted
                "validation_rules": {
                    "required": True,
                    "data_type": "string"
                }
            },
            "SSF No.": {
                "format": "[TC][.]",
                "value_format": "Alphanumeric",
                "include_in_report": True,
                "standard_key": "ssf_number",
                "is_fixed": True,  # MOCK.txt: Fixed item - cannot be deleted
                "validation_rules": {
                    "required": True,
                    "data_type": "string"
                }
            },
            "Ghana Card ID": {
                "format": "[TC]",
                "value_format": "Alphanumeric",
                "include_in_report": True,
                "standard_key": "ghana_card_id",
                "is_fixed": True,  # MOCK.txt: Fixed item - cannot be deleted
                "validation_rules": {
                    "required": True,
                    "data_type": "string"
                }
            },
            "Section": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True,
                "standard_key": "section",
                "is_fixed": True,  # MOCK.txt: Fixed item - cannot be deleted
                "validation_rules": {
                    "required": True,
                    "data_type": "string"
                }
            },
            "Job Title": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True,
                "standard_key": "job_title",
                "is_fixed": True,  # MOCK.txt: Fixed item - cannot be deleted
                "validation_rules": {
                    "required": True,
                    "data_type": "string"
                }
            }
        }
    },
    "EARNINGS": {
        "items": {
            "BASIC SALARY": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "basic_salary",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "GROSS SALARY": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "gross_salary",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "NET PAY": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "net_pay",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            }
        }
    },
    "DEDUCTIONS": {
        "items": {
            "SSF EMPLOYEE": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "ssf_employee",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "INCOME TAX": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "income_tax",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "TAXABLE SALARY": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "taxable_salary",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "TITHES": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "tithes",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "TOTAL DEDUCTIONS": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "total_deductions",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            }
        }
    },
    "EMPLOYERS CONTRIBUTION": {
        "items": {
            "SSF EMPLOYER": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "ssf_employer"
            }
        }
    },
    "LOANS": {
        "items": {
            "LOAN NAME": {
                "format": "[UC]",
                "value_format": "Text",
                "include_in_report": True,
                "standard_key": "loan_name",
                "is_fixed": True  # MOCK.txt: Fixed sub-section - cannot be deleted
            },
            "BALANCE B/F": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "balance_bf",
                "is_fixed": True  # MOCK.txt: Fixed sub-section - cannot be deleted
            },
            "CURRENT DEDUCTION": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "current_deduction",
                "is_fixed": True  # MOCK.txt: Fixed sub-section - cannot be deleted
            },
            "OUTSTANDING BALANCE": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "outstanding_balance",
                "is_fixed": True  # MOCK.txt: Fixed sub-section - cannot be deleted
            }
        }
    },
    "EMPLOYERS CONTRIBUTION": {
        "items": {
            "SSF EMPLOYER": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "ssf_employer",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "SAVING SCHEME EMPLOYER": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True,
                "standard_key": "saving_scheme_employer",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            }
        }
    },
    "EMPLOYEE BANK DETAILS": {
        "items": {
            "Bank": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True,
                "standard_key": "bank_name",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "Account No.": {
                "format": "[TC][.]",
                "value_format": "Alphanumeric",
                "include_in_report": True,
                "standard_key": "bank_account_number",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            },
            "Branch": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True,
                "standard_key": "bank_branch",
                "is_fixed": True  # MOCK.txt: Fixed item - cannot be deleted
            }
        }
    }
}

def clean_dictionary(dictionary, _depth=0):
    """Clean the dictionary to remove any NaN values and fix regex patterns."""
    if _depth > 10:
        print(f"WARNING: Dictionary cleaning depth limit reached at {_depth}")
        return dictionary

    if isinstance(dictionary, dict):
        cleaned = {}
        for key, value in dictionary.items():
            if value is not None and str(value).lower() != 'nan':
                cleaned[key] = clean_dictionary(value, _depth + 1)
        return cleaned
    elif isinstance(dictionary, list):
        return [clean_dictionary(item, _depth + 1) for item in dictionary if item is not None and str(item).lower() != 'nan']
    else:
        return dictionary

def load_dictionary():
    """Load the dictionary from file or create it with defaults if it doesn't exist."""
    try:
        if os.path.exists(DICTIONARY_PATH):
            try:
                with open(DICTIONARY_PATH, 'r') as f:
                    dictionary = json.load(f)

                # Clean the dictionary to remove any NaN values
                dictionary = clean_dictionary(dictionary)

                # Check if the dictionary has the required sections
                required_sections = [
                    "PERSONAL DETAILS", "EARNINGS", "DEDUCTIONS",
                    "EMPLOYERS CONTRIBUTION", "LOANS", "EMPLOYEE BANK DETAILS"
                ]

                # Add missing sections instead of resetting entire dictionary
                for section in required_sections:
                    if section not in dictionary:
                        print(f"Adding missing section: {section}", file=sys.stderr)
                        dictionary[section] = {"items": {}}

                return dictionary

            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading dictionary: {e}. Creating new one.", file=sys.stderr)
                return DEFAULT_DICTIONARY.copy()
        else:
            print("Dictionary file not found. Creating new one.", file=sys.stderr)
            return DEFAULT_DICTIONARY.copy()

    except Exception as e:
        print(f"Error in load_dictionary: {e}", file=sys.stderr)
        return DEFAULT_DICTIONARY.copy()

def save_dictionary(dictionary):
    """Save the dictionary to file."""
    try:
        # Clean the dictionary before saving
        cleaned_dictionary = clean_dictionary(dictionary)

        # Ensure directory exists
        os.makedirs(os.path.dirname(DICTIONARY_PATH), exist_ok=True)

        # Save the dictionary
        with open(DICTIONARY_PATH, 'w') as f:
            json.dump(cleaned_dictionary, f, indent=2)

        print(f"Dictionary saved successfully to {DICTIONARY_PATH}", file=sys.stderr)
        return True
    except Exception as e:
        print(f"Error saving dictionary {DICTIONARY_PATH}: {e}", file=sys.stderr)
        return False

def add_extracted_item(section, item_name, value):
    """Add an extracted item to the dictionary with MOCK.txt rules and standardization."""
    try:
        # Create manager instance to use MOCK.txt functions
        manager = DictionaryManager()

        # Check if item should be added based on MOCK.txt rules
        if not manager.should_add_to_dictionary(section, item_name):
            print(f"[*] Item {item_name} in {section} not added - MOCK.txt rules")
            return item_name

        # Load current dictionary
        dictionary = load_dictionary()

        # Ensure section exists
        if section not in dictionary:
            dictionary[section] = {"items": {}}

        # Check if item already exists
        if item_name in dictionary[section]["items"]:
            # Item exists, return standardized name
            return item_name

        # Add to pending items for approval (don't auto-add to dictionary)
        add_to_pending_items(section, item_name, value)

        print(f"[*] New item added for approval: {section}.{item_name}")

        return item_name

    except Exception as e:
        print(f"Error adding extracted item: {e}")
        return item_name

def add_to_pending_items(section, item_name, value):
    """Add item to pending approval queue."""
    global REAL_TIME_DATA

    item_id = str(uuid.uuid4())
    pending_item = {
        'id': item_id,
        'item_name': item_name,
        'value': str(value),
        'section': section,
        'confidence': 1.0,
        'source': '100% Accurate Hybrid Extractor',
        'timestamp': datetime.now().isoformat(),
        'status': 'pending'
    }

    REAL_TIME_DATA['pending_items'].append(pending_item)
    REAL_TIME_DATA['session_stats']['items_detected'] += 1

    return item_id

def get_pending_items_for_ui():
    """Get pending items for UI display."""
    global REAL_TIME_DATA
    return REAL_TIME_DATA.get('pending_items', [])

def approve_pending_item(item_id, section=None):
    """Approve a pending item and add it to the dictionary."""
    global REAL_TIME_DATA

    try:
        # Find the item in pending list
        item_to_approve = None
        for item in REAL_TIME_DATA['pending_items']:
            if item['id'] == item_id:
                item_to_approve = item
                break

        if not item_to_approve:
            print(f"Item with ID {item_id} not found in pending items")
            return False

        # Load dictionary with a fresh copy to avoid stale data
        dictionary = load_dictionary()

        # Use provided section or item's section
        target_section = section or item_to_approve['section']
        item_name = item_to_approve['item_name']

        # Debug information
        print(f"Adding to dictionary: Section '{target_section}', Item '{item_name}'")

        # Ensure section exists
        if target_section not in dictionary:
            print(f"Creating new section in dictionary: {target_section}")
            dictionary[target_section] = {"items": {}}
        elif "items" not in dictionary[target_section]:
            print(f"Section {target_section} exists but has no items key, creating it")
            dictionary[target_section]["items"] = {}

        # Check if item already exists to avoid duplicate
        if item_name in dictionary[target_section]["items"]:
            print(f"Item {item_name} already exists in dictionary section {target_section}")
            REAL_TIME_DATA['session_stats']['items_approved'] += 1
            print(f"[+] Approved existing item: {target_section}.{item_name}")
            return True
        else:
            # Add the item to the dictionary
            dictionary[target_section]["items"][item_name] = {
                "format": "[TC]",
                "value_format": "Numeric" if target_section in ["EARNINGS", "DEDUCTIONS", "LOANS", "EMPLOYERS CONTRIBUTION"] else "Text",
                "include_in_report": True,
                "standard_key": item_name.lower().replace(' ', '_'),
                "auto_added": True,
                "added_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "source": "auto_learning",
                "approved_by": "system"
            }
            
            # Save dictionary
            if save_dictionary(dictionary):
                # Move item from pending to approved
                REAL_TIME_DATA['pending_items'].remove(item_to_approve)
                item_to_approve['status'] = 'approved'
                item_to_approve['approved_date'] = datetime.now().isoformat()
                REAL_TIME_DATA['approved_items'].append(item_to_approve)
                REAL_TIME_DATA['session_stats']['items_approved'] += 1
                
                print(f"[+] Added and approved new item: {target_section}.{item_name}")
                return True
            else:
                print(f"[-] Failed to save dictionary after approving {item_name}")
                return False

    except Exception as e:
        print(f"Error approving item {item_id}: {e}")
        return False

def reject_pending_item(item_id):
    """Reject a pending item."""
    global REAL_TIME_DATA

    try:
        # Find the item in pending list
        item_to_reject = None
        item_index = -1

        for i, item in enumerate(REAL_TIME_DATA['pending_items']):
            if item['id'] == item_id:
                item_to_reject = item
                item_index = i
                break

        if not item_to_reject:
            print(f"Item with ID {item_id} not found in pending items", file=sys.stderr)
            return False

        # Remove item from pending list by index (safer)
        REAL_TIME_DATA['pending_items'].pop(item_index)

        # Update item status and add to rejected list
        item_to_reject['status'] = 'rejected'
        item_to_reject['rejected_date'] = datetime.now().isoformat()
        REAL_TIME_DATA['rejected_items'].append(item_to_reject)
        REAL_TIME_DATA['session_stats']['items_rejected'] += 1

        print(f"[-] Rejected item: {item_to_reject['section']}.{item_to_reject['item_name']}")
        return True

    except Exception as e:
        print(f"Error rejecting item {item_id}: {e}", file=sys.stderr)
        return False

def approve_all_pending_items():
    """Approve all pending items."""
    try:
        if not REAL_TIME_DATA['pending_items']:
            return True

        count = 0
        items_to_process = REAL_TIME_DATA['pending_items'].copy()

        for item in items_to_process:
            if approve_pending_item(item['id']):
                count += 1

        print(f"[+] Approved {count} items in bulk")
        return True

    except Exception as e:
        print(f"Error approving all items: {e}")
        return False

def reject_all_pending_items():
    """Reject all pending items."""
    try:
        global REAL_TIME_DATA

        if not REAL_TIME_DATA['pending_items']:
            print("[-] No pending items to reject")
            return True

        count = 0
        items_to_process = REAL_TIME_DATA['pending_items'].copy()

        print(f"[-] Starting bulk rejection of {len(items_to_process)} items")

        for item in items_to_process:
            if reject_pending_item(item['id']):
                count += 1

        # Force clear any remaining items (safety measure)
        REAL_TIME_DATA['pending_items'] = []

        print(f"[-] Rejected {count} items in bulk - pending list cleared")
        return True

    except Exception as e:
        print(f"Error rejecting all items: {e}")
        return False

def get_session_stats():
    """Get current session statistics."""
    global REAL_TIME_DATA

    stats = REAL_TIME_DATA['session_stats'].copy()

    # Calculate accuracy rate
    total_processed = stats['items_approved'] + stats['items_rejected']
    if total_processed > 0:
        stats['accuracy_rate'] = (stats['items_approved'] / total_processed) * 100
    else:
        stats['accuracy_rate'] = 100.0

    return stats

def clear_session_data():
    """Clear all session data."""
    global REAL_TIME_DATA

    REAL_TIME_DATA = {
        'pending_items': [],
        'approved_items': [],
        'rejected_items': [],
        'session_stats': {
            'items_detected': 0,
            'items_approved': 0,
            'items_rejected': 0,
            'accuracy_rate': 100.0
        }
    }

    print("[*] Session data cleared", file=sys.stderr)
    return True

def export_dictionary():
    """Export dictionary for backup."""
    try:
        dictionary = load_dictionary()
        export_path = DICTIONARY_PATH.replace('.json', '_backup.json')

        with open(export_path, 'w') as f:
            json.dump(dictionary, f, indent=2)

        print(f"[+] Dictionary exported to {export_path}")
        return export_path

    except Exception as e:
        print(f"Error exporting dictionary: {e}")
        return None

def import_dictionary(import_path):
    """Import dictionary from backup."""
    try:
        if not os.path.exists(import_path):
            print(f"Import file not found: {import_path}")
            return False

        with open(import_path, 'r') as f:
            imported_dict = json.load(f)

        # Validate structure
        required_sections = [
            "PERSONAL DETAILS", "EARNINGS", "DEDUCTIONS",
            "EMPLOYERS CONTRIBUTION", "LOANS", "EMPLOYEE BANK DETAILS"
        ]

        for section in required_sections:
            if section not in imported_dict:
                print(f"Invalid dictionary: missing section {section}")
                return False

        # Save imported dictionary
        if save_dictionary(imported_dict):
            print(f"[+] Dictionary imported from {import_path}")
            return True
        else:
            print(f"[-] Failed to save imported dictionary")
            return False

    except Exception as e:
        print(f"Error importing dictionary: {e}")
        return False

# Dictionary Manager Class for easier integration
class DictionaryManager:
    """Clean Dictionary Manager without Unicode corruption."""

    def __init__(self):
        self.dictionary = load_dictionary()

    def add_extracted_item(self, section, item_name, value):
        """Add an extracted item to the dictionary."""
        return add_extracted_item(section, item_name, value)

    def get_pending_items(self):
        """Get pending items for approval."""
        return get_pending_items_for_ui()

    def approve_item(self, item_id, section=None):
        """Approve a pending item."""
        return approve_pending_item(item_id, section)

    def reject_item(self, item_id):
        """Reject a pending item."""
        return reject_pending_item(item_id)

    def get_stats(self):
        """Get session statistics."""
        return get_session_stats()

    def clear_session(self):
        """Clear session data."""
        return clear_session_data()

    def export_backup(self):
        """Export dictionary backup."""
        return export_dictionary()

    def import_backup(self, path):
        """Import dictionary backup."""
        return import_dictionary(path)

    # MOCK.txt Implementation Functions
    def is_fixed_item(self, section, item_name):
        """Check if an item is fixed and cannot be deleted/modified."""
        try:
            dictionary = load_dictionary()
            section_data = dictionary.get(section, {})
            items = section_data.get('items', {})
            item_data = items.get(item_name, {})
            return item_data.get('is_fixed', False)
        except Exception as e:
            print(f"Error checking if item is fixed: {e}")
            return False

    def classify_loan_type(self, loan_name, employee_no=None):
        """
        Classify loan type based on dictionary fixed items (no hardcoded keywords).

        BUSINESS RULE: Fixed items in LOANS section = IN-HOUSE, all others = EXTERNAL
        """
        try:
            # Load dictionary to check fixed items
            dictionary = self.get_dictionary()
            loans_section = dictionary.get('LOANS', {})
            loans_items = loans_section.get('items', {})

            loan_name_upper = loan_name.upper()

            # Check if loan matches any fixed item in LOANS section
            for item_name, item_data in loans_items.items():
                if item_data.get('is_fixed', False):
                    # Skip column headers
                    if not item_data.get('is_column_header', False):
                        if item_name.upper() in loan_name_upper or loan_name_upper in item_name.upper():
                            return 'IN-HOUSE'

            # If not found in dictionary fixed items, classify as EXTERNAL
            return 'EXTERNAL'

        except Exception as e:
            print(f"Error classifying loan type: {e}")
            return 'EXTERNAL'

    def should_add_to_dictionary(self, section, item_name):
        """Determine if an item should be added to dictionary based on MOCK.txt rules."""
        try:
            # Fixed items should never be re-added
            if self.is_fixed_item(section, item_name):
                return False

            # Check if item already exists
            dictionary = load_dictionary()
            section_data = dictionary.get(section, {})
            items = section_data.get('items', {})

            if item_name in items:
                return False  # Already exists

            # Section-specific rules using ACTUAL payslip labels
            if section == 'PERSONAL DETAILS':
                # Only allow new personal details if they're not in fixed list
                fixed_personal = [
                    'Employee No.', 'Employee Name', 'SSF No.',
                    'Ghana Card ID', 'Section', 'Department', 'Job Title'
                ]
                return item_name not in fixed_personal

            elif section == 'EARNINGS':
                # Only allow new earnings if they're not fixed
                fixed_earnings = ['BASIC SALARY', 'GROSS SALARY', 'NET PAY']
                return item_name not in fixed_earnings

            elif section == 'DEDUCTIONS':
                # Only allow new deductions if they're not fixed
                fixed_deductions = [
                    'SSF EMPLOYEE', 'INCOME TAX', 'TAXABLE SALARY',
                    'TITHES', 'TOTAL DEDUCTIONS'
                ]
                return item_name not in fixed_deductions

            elif section == 'LOANS':
                # Only allow new loans if they're not fixed loan structure items
                fixed_loan_items = ['LOAN', 'BALANCE B/F', 'CURRENT DEDUCTION', 'OUST. BALANCE']
                return item_name not in fixed_loan_items

            elif section == 'EMPLOYERS CONTRIBUTION':
                # Only allow new contributions if they're not fixed
                fixed_contributions = ['SSF EMPLOYER', 'SAVING SCHEME (EMPLOYER)']
                return item_name not in fixed_contributions

            elif section == 'EMPLOYEE BANK DETAILS':
                # Only allow new bank details if they're not fixed
                fixed_bank = ['Bank', 'Account No.', 'Branch']
                return item_name not in fixed_bank

            return True  # Allow by default

        except Exception as e:
            print(f"Error checking if item should be added: {e}")
            return True

    def remove_item(self, section, item_name):
        """Remove an item from dictionary with fixed item protection."""
        try:
            # Check if item is fixed
            if self.is_fixed_item(section, item_name):
                print(f"Cannot remove fixed item: {item_name} in {section}")
                return False

            dictionary = load_dictionary()

            if section not in dictionary:
                return False

            section_data = dictionary[section]
            items = section_data.get('items', {})

            if item_name not in items:
                return False

            # Remove the item
            del items[item_name]

            # Save updated dictionary
            if save_dictionary(dictionary):
                print(f"Removed item: {item_name} from {section}")
                return True
            else:
                print(f"Failed to save dictionary after removing {item_name}")
                return False

        except Exception as e:
            print(f"Error removing item: {e}")
            return False

    def reset_to_defaults_with_protection(self):
        """Reset dictionary to clean state with real payslip labels marked as fixed."""
        try:
            # Create clean dictionary with real payslip labels and is_fixed properties
            clean_dict = {
                "PERSONAL DETAILS": {
                    "items": {
                        "Employee No.": {
                            "format": "[TC][.]",
                            "value_format": "Alphanumeric",
                            "include_in_report": True,
                            "standard_key": "employee_id",
                            "validation_rules": {
                                "pattern": "^(COP|PW|SEC|E|PTS|PGH)\\d{4}$",
                                "required": True,
                                "data_type": "string"
                            },
                            "variations": [],
                            "is_fixed": True
                        },
                        "Employee Name": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "employee_name",
                            "validation_rules": {
                                "pattern": "^[A-Z][A-Za-z\\s\\.\\-]{2,50}$",
                                "required": True,
                                "data_type": "string"
                            },
                            "variations": [],
                            "is_fixed": True
                        },
                        "Department": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "department",
                            "validation_rules": {
                                "required": True,
                                "data_type": "string"
                            },
                            "variations": [],
                            "is_fixed": True
                        },
                        "SSF No.": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "ssf_no.",
                            "variations": [],
                            "is_fixed": True
                        },
                        "Ghana Card ID": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "ghana_card_id",
                            "variations": [],
                            "is_fixed": True
                        },
                        "Section": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "section",
                            "variations": [],
                            "is_fixed": True
                        },
                        "Job Title": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "job_title",
                            "variations": [],
                            "is_fixed": True
                        }
                    }
                },
                "EARNINGS": {
                    "items": {
                        "BASIC SALARY": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "basic_salary",
                            "variations": [],
                            "is_fixed": True
                        },
                        "GROSS SALARY": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "gross_salary",
                            "variations": [],
                            "is_fixed": True
                        },
                        "NET PAY": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "net_pay",
                            "variations": [],
                            "is_fixed": True
                        }
                    }
                },
                "DEDUCTIONS": {
                    "items": {
                        "SSF EMPLOYEE": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "ssf_employee",
                            "variations": [],
                            "is_fixed": True
                        },
                        "INCOME TAX": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "income_tax",
                            "variations": [],
                            "is_fixed": True
                        },
                        "TAXABLE SALARY": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "taxable_salary",
                            "variations": [],
                            "is_fixed": True
                        },
                        "TITHES": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "tithes",
                            "variations": ["Tithe", "Church Contribution", "Church Tithe"],
                            "is_fixed": True
                        },
                        "TOTAL DEDUCTIONS": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "total_deductions",
                            "variations": [],
                            "is_fixed": True
                        }
                    }
                },
                "LOANS": {
                    "items": {
                        "LOAN": {
                            "format": "[UC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "loan_type",
                            "variations": [],
                            "is_fixed": True
                        },
                        "BALANCE B/F": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "balance_bf",
                            "variations": [],
                            "is_fixed": True
                        },
                        "CURRENT DEDUCTION": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "current_deduction",
                            "variations": [],
                            "is_fixed": True
                        },
                        "OUST. BALANCE": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "outstanding_balance",
                            "variations": [],
                            "is_fixed": True
                        }
                    }
                },
                "EMPLOYERS CONTRIBUTION": {
                    "items": {
                        "SSF EMPLOYER": {
                            "format": "[UC]",
                            "value_format": "Numeric with decimal places",
                            "include_in_report": True,
                            "standard_key": "ssf_employer",
                            "variations": [],
                            "is_fixed": True
                        },
                        "SAVING SCHEME (EMPLOYER)": {
                            "format": "[TC]",
                            "value_format": "Numeric",
                            "include_in_report": True,
                            "standard_key": "saving_scheme_(employer)",
                            "variations": [],
                            "is_fixed": True
                        }
                    }
                },
                "EMPLOYEE BANK DETAILS": {
                    "items": {
                        "Bank": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "bank_name",
                            "variations": [],
                            "is_fixed": True
                        },
                        "Account No.": {
                            "format": "[TC][.]",
                            "value_format": "Alphanumeric",
                            "include_in_report": True,
                            "standard_key": "bank_account_number",
                            "variations": [],
                            "is_fixed": True
                        },
                        "Branch": {
                            "format": "[TC]",
                            "value_format": "Text",
                            "include_in_report": True,
                            "standard_key": "bank_branch",
                            "variations": [],
                            "is_fixed": True
                        }
                    }
                }
            }

            # Save clean dictionary
            if save_dictionary(clean_dict):
                print("Dictionary reset to clean state with real payslip labels")
                return True
            else:
                print("Failed to save clean dictionary")
                return False

        except Exception as e:
            print(f"Error resetting dictionary: {e}")
            return False

    def _get_default_dictionary(self):
        """Get the default dictionary structure with all fixed items."""
        return {
            "PERSONAL DETAILS": {
                "items": {
                    "EMPLOYEE NO.": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "EMPLOYEE NO.",
                        "is_fixed": True,
                        "variations": ["Employee No.", "Staff No.", "ID"]
                    },
                    "EMPLOYEE NAME": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "EMPLOYEE NAME",
                        "is_fixed": True,
                        "variations": ["Name", "Full Name", "Staff Name"]
                    },
                    "SSF NO.": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "SSF NO.",
                        "is_fixed": True,
                        "variations": ["SSF Number", "Social Security"]
                    },
                    "GHANA CARD ID": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "GHANA CARD ID",
                        "is_fixed": True,
                        "variations": ["Ghana Card", "National ID"]
                    },
                    "SECTION": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "SECTION",
                        "is_fixed": True,
                        "variations": ["Section", "Unit"]
                    },
                    "DEPARTMENT": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "DEPARTMENT",
                        "is_fixed": True,
                        "variations": ["Dept", "Division"]
                    },
                    "JOB TITLE": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "JOB TITLE",
                        "is_fixed": True,
                        "variations": ["Position", "Role", "Title"]
                    }
                }
            },
            "EARNINGS": {
                "items": {
                    "BASIC SALARY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "BASIC SALARY",
                        "is_fixed": True,
                        "variations": ["Basic Pay", "Base Salary"]
                    },
                    "GROSS SALARY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "GROSS SALARY",
                        "is_fixed": True,
                        "variations": ["Gross Pay", "Total Earnings"]
                    },
                    "NET PAY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "NET PAY",
                        "is_fixed": True,
                        "variations": ["Net Salary", "Take Home", "Final Pay"]
                    }
                }
            },
            "DEDUCTIONS": {
                "items": {
                    "SSF EMPLOYEE": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "SSF EMPLOYEE",
                        "is_fixed": True,
                        "variations": ["SSF Deduction", "Social Security"]
                    },
                    "INCOME TAX": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "INCOME TAX",
                        "is_fixed": True,
                        "variations": ["Tax", "PAYE"]
                    },
                    "TAXABLE SALARY": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "TAXABLE SALARY",
                        "is_fixed": True,
                        "variations": ["Taxable Income", "Taxable Pay"]
                    },
                    "TITHES": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "TITHES",
                        "is_fixed": True,
                        "variations": ["Tithe", "Church Contribution"]
                    },
                    "TOTAL DEDUCTIONS": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "TOTAL DEDUCTIONS",
                        "is_fixed": True,
                        "variations": ["Total Deduction", "Deductions Total"]
                    }
                }
            },
            "LOANS": {
                "items": {
                    "LOAN NAME": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "LOAN NAME",
                        "is_fixed": True,
                        "variations": ["Loan Type", "Loan Description"]
                    },
                    "BALANCE B/F": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "BALANCE B/F",
                        "is_fixed": True,
                        "variations": ["Balance Brought Forward", "Previous Balance"]
                    },
                    "CURRENT DEDUCTION": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "CURRENT DEDUCTION",
                        "is_fixed": True,
                        "variations": ["Monthly Deduction", "Current Payment"]
                    },
                    "OUTSTANDING BALANCE": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "OUTSTANDING BALANCE",
                        "is_fixed": True,
                        "variations": ["Remaining Balance", "Balance Outstanding"]
                    }
                }
            },
            "EMPLOYERS CONTRIBUTION": {
                "items": {
                    "SSF EMPLOYER": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "SSF EMPLOYER",
                        "is_fixed": True,
                        "variations": ["SSF Contribution", "Employer SSF"]
                    },
                    "SAVING SCHEME EMPLOYER": {
                        "format": "amount",
                        "value_format": "currency",
                        "include_in_report": True,
                        "standardized_name": "SAVING SCHEME EMPLOYER",
                        "is_fixed": True,
                        "variations": ["Savings Scheme", "Employer Savings"]
                    }
                }
            },
            "EMPLOYEE BANK DETAILS": {
                "items": {
                    "BANK": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "BANK",
                        "is_fixed": True,
                        "variations": ["Bank Name", "Financial Institution"]
                    },
                    "ACCOUNT NO.": {
                        "format": "text",
                        "value_format": "alphanumeric",
                        "include_in_report": True,
                        "standardized_name": "ACCOUNT NO.",
                        "is_fixed": True,
                        "variations": ["Account Number", "Bank Account"]
                    },
                    "BRANCH": {
                        "format": "text",
                        "value_format": "text",
                        "include_in_report": True,
                        "standardized_name": "BRANCH",
                        "is_fixed": True,
                        "variations": ["Bank Branch", "Branch Name"]
                    }
                }
            }
        }

# Command line interface
if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]

        if command == 'get-pending':
            # Get pending items
            items = get_pending_items_for_ui()
            print(json.dumps(items))

        elif command == 'approve-item' and len(sys.argv) > 2:
            # Approve a pending item
            item_id = sys.argv[2]
            section = sys.argv[3] if len(sys.argv) > 3 else None
            success = approve_pending_item(item_id, section)
            print('true' if success else 'false')

        elif command == 'reject-item' and len(sys.argv) > 2:
            # Reject a pending item
            item_id = sys.argv[2]
            success = reject_pending_item(item_id)
            print('true' if success else 'false')

        elif command == 'approve-all':
            # Approve all pending items
            success = approve_all_pending_items()
            print('true' if success else 'false')

        elif command == 'reject-all':
            # Reject all pending items
            success = reject_all_pending_items()
            print('true' if success else 'false')

        elif command == 'clear-session':
            # Clear all session data
            success = clear_session_data()
            print('true' if success else 'false')

        elif command == 'get-stats':
            # Get session statistics
            stats = get_session_stats()
            print(json.dumps(stats))

        elif command == 'get-dictionary':
            # Get the actual dictionary data - suppress debug output
            import io
            import contextlib

            # Capture stderr to suppress debug messages
            stderr_capture = io.StringIO()
            with contextlib.redirect_stderr(stderr_capture):
                dictionary = load_dictionary()

            # Output clean JSON only
            print(json.dumps(dictionary))

        elif command == 'clear-session':
            # Clear session data
            success = clear_session_data()
            print('true' if success else 'false')

        elif command == 'export':
            # Export dictionary
            path = export_dictionary()
            print(path if path else 'false')

        elif command == 'save' and len(sys.argv) > 2:
            # Save dictionary from file path
            try:
                file_path = sys.argv[2]
                if not os.path.exists(file_path):
                    print(f"File not found: {file_path}", file=sys.stderr)
                    print('false')
                else:
                    # Load dictionary from file
                    with open(file_path, 'r', encoding='utf-8') as f:
                        dictionary_data = json.load(f)

                    # Save the dictionary
                    success = save_dictionary(dictionary_data)
                    print('true' if success else 'false')
            except Exception as e:
                print(f"Error in save command: {e}", file=sys.stderr)
                print('false')

        elif command == 'import' and len(sys.argv) > 2:
            # Import dictionary
            import_path = sys.argv[2]
            success = import_dictionary(import_path)
            print('true' if success else 'false')

        elif command == 'is-fixed' and len(sys.argv) > 3:
            # Check if item is fixed
            section = sys.argv[2]
            item_name = sys.argv[3]
            manager = DictionaryManager()
            is_fixed = manager.is_fixed_item(section, item_name)
            print('true' if is_fixed else 'false')

        elif command == 'classify-loan' and len(sys.argv) > 2:
            # Classify loan type
            loan_name = sys.argv[2]
            employee_no = sys.argv[3] if len(sys.argv) > 3 else None
            manager = DictionaryManager()
            loan_type = manager.classify_loan_type(loan_name, employee_no)
            print(loan_type)

        elif command == 'should-add' and len(sys.argv) > 3:
            # Check if item should be added
            section = sys.argv[2]
            item_name = sys.argv[3]
            manager = DictionaryManager()
            should_add = manager.should_add_to_dictionary(section, item_name)
            print('true' if should_add else 'false')

        elif command == 'remove-item' and len(sys.argv) > 3:
            # Remove item with protection
            section = sys.argv[2]
            item_name = sys.argv[3]
            manager = DictionaryManager()
            success = manager.remove_item(section, item_name)
            print('true' if success else 'false')

        elif command == 'reset-protected':
            # Reset dictionary with fixed item protection
            manager = DictionaryManager()
            success = manager.reset_to_defaults_with_protection()
            print('true' if success else 'false')

        else:
            print(json.dumps({'error': 'Unknown command'}))
    else:
        # Interactive mode - show help
        print("Clean Payroll Dictionary Manager")
        print("Available commands:")
        print("  get-pending      - Get pending items")
        print("  approve-item     - Approve an item")
        print("  reject-item      - Reject an item")
        print("  approve-all      - Approve all items")
        print("  reject-all       - Reject all items")
        print("  get-stats        - Get session stats")
        print("  get-dictionary   - Get dictionary data")
        print("  clear-session    - Clear session data")
        print("  save             - Save dictionary from JSON")
        print("  export           - Export dictionary")
        print("  import           - Import dictionary")
        print("  is-fixed         - Check if item is fixed")
        print("  classify-loan    - Classify loan type")
        print("  should-add       - Check if item should be added")
        print("  remove-item      - Remove item with protection")
        print("  reset-protected  - Reset with fixed item protection")
