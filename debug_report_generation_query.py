#!/usr/bin/env python3
"""Debug the report generation query"""

import sqlite3

def debug_query():
    conn = sqlite3.connect('payroll_audit.db')
    cursor = conn.cursor()
    
    try:
        # Get current session
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY session_id DESC LIMIT 1")
        session_result = cursor.fetchone()
        current_session = session_result[0]
        
        print(f"🔍 DEBUGGING REPORT GENERATION QUERY")
        print(f"Session: {current_session}")
        print("=" * 50)
        
        # 1. Check pre_reporting_results
        print("\n1. 📊 PRE_REPORTING_RESULTS:")
        cursor.execute("""
            SELECT id, change_id, section, item_label, change_type, selected_for_report
            FROM pre_reporting_results 
            WHERE session_id = ?
        """, (current_session,))
        
        pre_reporting_rows = cursor.fetchall()
        print(f"   Total records: {len(pre_reporting_rows)}")
        
        selected_count = 0
        for row in pre_reporting_rows:
            if row[5] == 1:  # selected_for_report
                selected_count += 1
                print(f"   ✅ Selected: ID={row[0]}, change_id={row[1]}, {row[2]}.{row[3]} ({row[4]})")
            else:
                print(f"   ❌ Not selected: ID={row[0]}, change_id={row[1]}, {row[2]}.{row[3]} ({row[4]})")
        
        print(f"   Selected for report: {selected_count}")
        
        # 2. Check comparison_results
        print("\n2. 📊 COMPARISON_RESULTS:")
        cursor.execute("""
            SELECT id, employee_id, employee_name, section_name, item_label, change_type
            FROM comparison_results 
            WHERE session_id = ?
        """, (current_session,))
        
        comparison_rows = cursor.fetchall()
        print(f"   Total records: {len(comparison_rows)}")
        
        if comparison_rows:
            for row in comparison_rows[:5]:  # Show first 5
                print(f"   ID={row[0]}, {row[3]}.{row[4]} ({row[5]})")
        else:
            print("   ❌ No comparison results found!")
        
        # 3. Test the actual query used by report generation
        print("\n3. 🔍 TESTING REPORT GENERATION QUERY:")
        
        query = '''SELECT cr.id, cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                          cr.previous_value, cr.current_value, cr.change_type, cr.priority,
                          cr.numeric_difference, cr.percentage_change,
                          pr.bulk_category, pr.bulk_size
                   FROM comparison_results cr
                   JOIN pre_reporting_results pr ON cr.id = pr.change_id
                   LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
                   WHERE cr.session_id = ? AND pr.selected_for_report = 1
                     AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
                     AND (
                       (cr.change_type = 'NEW' AND (di.include_new = 1 OR di.include_new IS NULL)) OR
                       (cr.change_type IN ('INCREASED', 'INCREASE') AND (di.include_increase = 1 OR di.include_increase IS NULL)) OR
                       (cr.change_type IN ('DECREASED', 'DECREASE') AND (di.include_decrease = 1 OR di.include_decrease IS NULL)) OR
                       (cr.change_type = 'REMOVED' AND (di.include_removed = 1 OR di.include_removed IS NULL)) OR
                       (cr.change_type = 'NO_CHANGE' AND (di.include_no_change = 1)) OR
                       (cr.change_type = 'CHANGED' AND (di.include_increase = 1 OR di.include_increase IS NULL))
                     )'''
        
        cursor.execute(query, (current_session,))
        report_rows = cursor.fetchall()
        
        print(f"   Query result: {len(report_rows)} records")
        
        if report_rows:
            for row in report_rows:
                print(f"   ✅ {row[3]}.{row[4]} ({row[7]})")
        else:
            print("   ❌ No records returned by report generation query")
        
        # 4. Debug step by step
        print("\n4. 🔍 STEP-BY-STEP DEBUG:")
        
        # Step 1: Basic join
        cursor.execute("""
            SELECT COUNT(*)
            FROM comparison_results cr
            JOIN pre_reporting_results pr ON cr.id = pr.change_id
            WHERE cr.session_id = ?
        """, (current_session,))
        
        join_count = cursor.fetchone()[0]
        print(f"   Step 1 - Basic join: {join_count} records")
        
        # Step 2: Add selected filter
        cursor.execute("""
            SELECT COUNT(*)
            FROM comparison_results cr
            JOIN pre_reporting_results pr ON cr.id = pr.change_id
            WHERE cr.session_id = ? AND pr.selected_for_report = 1
        """, (current_session,))
        
        selected_join_count = cursor.fetchone()[0]
        print(f"   Step 2 - With selected filter: {selected_join_count} records")
        
        # Step 3: Add dictionary join
        cursor.execute("""
            SELECT COUNT(*)
            FROM comparison_results cr
            JOIN pre_reporting_results pr ON cr.id = pr.change_id
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND pr.selected_for_report = 1
        """, (current_session,))
        
        dict_join_count = cursor.fetchone()[0]
        print(f"   Step 3 - With dictionary join: {dict_join_count} records")
        
        # Step 4: Add include_in_report filter
        cursor.execute("""
            SELECT COUNT(*)
            FROM comparison_results cr
            JOIN pre_reporting_results pr ON cr.id = pr.change_id
            LEFT JOIN dictionary_items di ON cr.item_label = di.item_name
            WHERE cr.session_id = ? AND pr.selected_for_report = 1
              AND (di.include_in_report = 1 OR di.include_in_report IS NULL)
        """, (current_session,))
        
        include_filter_count = cursor.fetchone()[0]
        print(f"   Step 4 - With include_in_report filter: {include_filter_count} records")
        
        # 5. Check if the issue is with change_id matching
        print("\n5. 🔍 CHECKING CHANGE_ID MATCHING:")
        
        cursor.execute("""
            SELECT pr.change_id, cr.id
            FROM pre_reporting_results pr
            LEFT JOIN comparison_results cr ON pr.change_id = cr.id
            WHERE pr.session_id = ? AND pr.selected_for_report = 1
        """, (current_session,))
        
        matching_rows = cursor.fetchall()
        print(f"   Change ID matching check:")
        
        for pr_change_id, cr_id in matching_rows:
            if cr_id is None:
                print(f"   ❌ pre_reporting change_id {pr_change_id} has no matching comparison_results")
            else:
                print(f"   ✅ pre_reporting change_id {pr_change_id} matches comparison_results id {cr_id}")
        
        # 6. Recommendation
        print("\n6. 💡 RECOMMENDATION:")
        
        if join_count == 0:
            print("   ❌ ISSUE: No matching records between comparison_results and pre_reporting_results")
            print("   🔧 FIX: Need to ensure change_id in pre_reporting_results matches id in comparison_results")
        elif selected_join_count == 0:
            print("   ❌ ISSUE: No records have selected_for_report = 1")
            print("   🔧 FIX: Update pre_reporting_results to set selected_for_report = 1 for desired records")
        elif dict_join_count < selected_join_count:
            print("   ⚠️ ISSUE: Dictionary join is reducing record count")
            print("   🔧 FIX: Check if item_label names match between comparison_results and dictionary_items")
        elif include_filter_count < dict_join_count:
            print("   ⚠️ ISSUE: include_in_report filter is excluding records")
            print("   🔧 FIX: Check dictionary toggle states")
        else:
            print("   ✅ All steps look good - issue might be in change type filtering")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

if __name__ == "__main__":
    debug_query()
