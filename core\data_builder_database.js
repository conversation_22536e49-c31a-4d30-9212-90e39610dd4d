/**
 * DATA BUILDER DATABASE
 * SQLite integration for high-performance data management
 * Solves UI freezing and enables fast queries for 2900+ employees
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class DataBuilderDatabase {
    constructor(dbPath = null) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'data_builder.db');
        this.db = null;
        this.isConnected = false;
        
        // Ensure data directory exists
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        
        this.initializeDatabase();
    }

    /**
     * Initialize SQLite database with optimized schema
     */
    async initializeDatabase() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('❌ Database connection error:', err);
                    reject(err);
                    return;
                }
                
                console.log('🗄️ DATA BUILDER DATABASE CONNECTED');
                this.isConnected = true;
                
                // Create optimized tables
                this.createTables()
                    .then(() => {
                        console.log('✅ Database tables initialized');
                        resolve();
                    })
                    .catch(reject);
            });
        });
    }

    /**
     * Create optimized database tables
     */
    async createTables() {
        const tables = [
            // Employee master table
            `CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id TEXT UNIQUE NOT NULL,
                employee_name TEXT,
                department TEXT,
                section TEXT,
                job_title TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Extracted items table (normalized)
            `CREATE TABLE IF NOT EXISTS extracted_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id TEXT NOT NULL,
                section TEXT NOT NULL,
                label TEXT NOT NULL,
                value TEXT,
                formatted_value REAL,
                format_type TEXT,
                confidence REAL DEFAULT 1.0,
                extraction_source TEXT DEFAULT 'Perfect Section-Aware Extractor',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(employee_id)
            )`,
            
            // Column definitions table
            `CREATE TABLE IF NOT EXISTS column_definitions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                column_name TEXT UNIQUE NOT NULL,
                original_label TEXT,
                section TEXT,
                format_type TEXT,
                value_type TEXT,
                include_in_report BOOLEAN DEFAULT 1,
                occurrence_count INTEGER DEFAULT 0,
                first_seen_employee TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Processing sessions table
            `CREATE TABLE IF NOT EXISTS processing_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                total_employees INTEGER,
                processed_employees INTEGER,
                total_columns INTEGER,
                data_deficits INTEGER,
                processing_errors INTEGER,
                started_at DATETIME,
                completed_at DATETIME,
                status TEXT DEFAULT 'active'
            )`,
            
            // Data deficits tracking
            `CREATE TABLE IF NOT EXISTS data_deficits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id TEXT NOT NULL,
                missing_field TEXT NOT NULL,
                field_type TEXT,
                severity TEXT DEFAULT 'warning',
                detected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(employee_id)
            )`
        ];

        for (const tableSQL of tables) {
            await this.runQuery(tableSQL);
        }

        // Create performance indexes
        await this.createIndexes();
    }

    /**
     * Create performance indexes
     */
    async createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_employee_id ON employees(employee_id)',
            'CREATE INDEX IF NOT EXISTS idx_extracted_items_employee ON extracted_items(employee_id)',
            'CREATE INDEX IF NOT EXISTS idx_extracted_items_section ON extracted_items(section)',
            'CREATE INDEX IF NOT EXISTS idx_extracted_items_label ON extracted_items(label)',
            'CREATE INDEX IF NOT EXISTS idx_column_definitions_name ON column_definitions(column_name)',
            'CREATE INDEX IF NOT EXISTS idx_data_deficits_employee ON data_deficits(employee_id)',
            'CREATE INDEX IF NOT EXISTS idx_data_deficits_field ON data_deficits(missing_field)'
        ];

        for (const indexSQL of indexes) {
            await this.runQuery(indexSQL);
        }
    }

    /**
     * Run SQL query with promise wrapper
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     */
    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ lastID: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * Get query results
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     */
    getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Get all query results
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     */
    getAllQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Store employee data
     * @param {Object} employeeData - Employee data
     */
    async storeEmployee(employeeData) {
        const { employeeId, employeeName, department, section, jobTitle } = employeeData;
        
        const sql = `INSERT OR REPLACE INTO employees 
                     (employee_id, employee_name, department, section, job_title, updated_at)
                     VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`;
        
        return await this.runQuery(sql, [employeeId, employeeName, department, section, jobTitle]);
    }

    /**
     * Store extracted items in bulk
     * @param {Array} items - Array of extracted items
     */
    async storeExtractedItems(items) {
        const sql = `INSERT INTO extracted_items 
                     (employee_id, section, label, value, formatted_value, format_type, confidence, extraction_source)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
        
        // Use transaction for bulk insert performance
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                this.db.run('BEGIN TRANSACTION');
                
                const stmt = this.db.prepare(sql);
                
                for (const item of items) {
                    const formattedValue = this.parseNumericValue(item.value, item.format);
                    stmt.run([
                        item.employeeId,
                        item.section,
                        item.label,
                        item.value,
                        formattedValue,
                        item.format,
                        item.confidence || 1.0,
                        item.source || 'Perfect Section-Aware Extractor'
                    ]);
                }
                
                stmt.finalize((err) => {
                    if (err) {
                        this.db.run('ROLLBACK');
                        reject(err);
                    } else {
                        this.db.run('COMMIT', (commitErr) => {
                            if (commitErr) {
                                reject(commitErr);
                            } else {
                                resolve({ inserted: items.length });
                            }
                        });
                    }
                });
            });
        });
    }

    /**
     * Parse numeric value for database storage
     * @param {string} value - Raw value
     * @param {string} format - Value format
     */
    parseNumericValue(value, format) {
        if (format === 'currency' || format === 'numeric') {
            const numericValue = parseFloat(value.replace(/,/g, ''));
            return isNaN(numericValue) ? null : numericValue;
        }
        return null;
    }

    /**
     * Store column definition
     * @param {string} columnName - Column name
     * @param {Object} definition - Column definition
     */
    async storeColumnDefinition(columnName, definition) {
        const sql = `INSERT OR REPLACE INTO column_definitions 
                     (column_name, original_label, section, format_type, value_type, 
                      include_in_report, occurrence_count, first_seen_employee)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
        
        return await this.runQuery(sql, [
            columnName,
            definition.originalLabel,
            definition.section,
            definition.format,
            definition.valueType,
            definition.includeInReport ? 1 : 0,
            definition.occurrenceCount || 1,
            definition.firstSeenIn
        ]);
    }

    /**
     * Get all column definitions
     */
    async getColumnDefinitions() {
        const sql = `SELECT * FROM column_definitions ORDER BY 
                     CASE 
                         WHEN column_name IN ('EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT') THEN 1
                         WHEN section = 'PERSONAL DETAILS' THEN 2
                         WHEN section = 'EARNINGS' THEN 3
                         WHEN section = 'DEDUCTIONS' THEN 4
                         WHEN section = 'LOANS' THEN 5
                         WHEN section = 'EMPLOYERS CONTRIBUTION' THEN 6
                         WHEN section = 'EMPLOYEE BANK DETAILS' THEN 7
                         ELSE 8
                     END, column_name`;
        
        return await this.getAllQuery(sql);
    }

    /**
     * Get all employee data with extracted items
     */
    async getAllEmployeeData() {
        const sql = `SELECT 
                         e.employee_id,
                         e.employee_name,
                         e.department,
                         e.section,
                         e.job_title,
                         ei.section as item_section,
                         ei.label,
                         ei.value,
                         ei.formatted_value,
                         ei.format_type
                     FROM employees e
                     LEFT JOIN extracted_items ei ON e.employee_id = ei.employee_id
                     ORDER BY e.employee_id, ei.section, ei.label`;
        
        return await this.getAllQuery(sql);
    }

    /**
     * Get employee data for Excel export (optimized)
     */
    async getExcelData() {
        // Get column definitions
        const columns = await this.getColumnDefinitions();
        
        // Get all employee data
        const employeeData = await this.getAllEmployeeData();
        
        // Group by employee
        const employeeMap = new Map();
        
        for (const row of employeeData) {
            if (!employeeMap.has(row.employee_id)) {
                employeeMap.set(row.employee_id, {
                    'EMPLOYEE NO.': row.employee_id,
                    'EMPLOYEE NAME': row.employee_name,
                    'DEPARTMENT': row.department,
                    'SECTION': row.section,
                    'JOB TITLE': row.job_title
                });
            }
            
            if (row.label) {
                const columnName = this.resolveColumnName(row.label, row.item_section);
                const employee = employeeMap.get(row.employee_id);
                employee[columnName] = row.formatted_value !== null ? row.formatted_value : row.value;
            }
        }
        
        return {
            columns: columns.map(col => ({
                key: col.column_name,
                header: col.column_name,
                width: this.calculateColumnWidth(col.column_name, col.format_type),
                format: col.format_type
            })),
            rows: Array.from(employeeMap.values())
        };
    }

    /**
     * Resolve column name with section prefix
     * @param {string} label - Item label
     * @param {string} section - Item section
     */
    resolveColumnName(label, section) {
        // This should match the logic in DataBuilderStore
        return `${section}.${label}`;
    }

    /**
     * Calculate column width
     * @param {string} columnName - Column name
     * @param {string} formatType - Format type
     */
    calculateColumnWidth(columnName, formatType) {
        const baseWidth = Math.max(columnName.length, 10);
        
        if (formatType === 'currency') {
            return Math.max(baseWidth, 15);
        } else if (formatType === 'alphanumeric') {
            return Math.max(baseWidth, 12);
        } else {
            return Math.max(baseWidth, 20);
        }
    }

    /**
     * Store data deficit
     * @param {string} employeeId - Employee ID
     * @param {string} missingField - Missing field name
     * @param {string} fieldType - Field type
     * @param {string} severity - Severity level
     */
    async storeDataDeficit(employeeId, missingField, fieldType = 'unknown', severity = 'warning') {
        const sql = `INSERT INTO data_deficits 
                     (employee_id, missing_field, field_type, severity)
                     VALUES (?, ?, ?, ?)`;
        
        return await this.runQuery(sql, [employeeId, missingField, fieldType, severity]);
    }

    /**
     * Get data deficits summary
     */
    async getDataDeficitsSummary() {
        const sql = `SELECT 
                         missing_field,
                         COUNT(*) as affected_employees,
                         ROUND(COUNT(*) * 100.0 / (SELECT COUNT(DISTINCT employee_id) FROM employees), 2) as percentage,
                         severity
                     FROM data_deficits
                     GROUP BY missing_field, severity
                     ORDER BY affected_employees DESC`;
        
        return await this.getAllQuery(sql);
    }

    /**
     * Create processing session
     * @param {string} sessionId - Session ID
     * @param {number} totalEmployees - Total employees to process
     */
    async createProcessingSession(sessionId, totalEmployees) {
        const sql = `INSERT INTO processing_sessions 
                     (session_id, total_employees, started_at)
                     VALUES (?, ?, CURRENT_TIMESTAMP)`;
        
        return await this.runQuery(sql, [sessionId, totalEmployees]);
    }

    /**
     * Update processing session
     * @param {string} sessionId - Session ID
     * @param {Object} updates - Updates to apply
     */
    async updateProcessingSession(sessionId, updates) {
        const fields = [];
        const values = [];
        
        for (const [key, value] of Object.entries(updates)) {
            fields.push(`${key} = ?`);
            values.push(value);
        }
        
        if (fields.length === 0) return;
        
        values.push(sessionId);
        
        const sql = `UPDATE processing_sessions 
                     SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
                     WHERE session_id = ?`;
        
        return await this.runQuery(sql, values);
    }

    /**
     * Clear all data (for reset functionality)
     */
    async clearAllData() {
        const tables = ['extracted_items', 'data_deficits', 'column_definitions', 'employees', 'processing_sessions'];
        
        for (const table of tables) {
            await this.runQuery(`DELETE FROM ${table}`);
        }
        
        console.log('🗑️ All data cleared from database');
    }

    /**
     * Close database connection
     */
    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err);
                    } else {
                        console.log('🗄️ Database connection closed');
                    }
                    this.isConnected = false;
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * Get database statistics
     */
    async getStatistics() {
        const stats = {};
        
        stats.totalEmployees = await this.getQuery('SELECT COUNT(*) as count FROM employees');
        stats.totalExtractedItems = await this.getQuery('SELECT COUNT(*) as count FROM extracted_items');
        stats.totalColumns = await this.getQuery('SELECT COUNT(*) as count FROM column_definitions');
        stats.totalDeficits = await this.getQuery('SELECT COUNT(*) as count FROM data_deficits');
        
        return {
            employees: stats.totalEmployees.count,
            extractedItems: stats.totalExtractedItems.count,
            columns: stats.totalColumns.count,
            dataDeficits: stats.totalDeficits.count
        };
    }
}

module.exports = DataBuilderDatabase;
