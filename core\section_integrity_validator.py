#!/usr/bin/env python3
"""
Section Integrity Validator
Validates that section assignments are preserved from extraction to auto-learning
"""

import sqlite3
import sys
import os

def validate_section_integrity(session_id=None):
    """Validate section integrity for a session"""
    db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    if not session_id:
        # Get latest session
        cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
        result = cursor.fetchone()
        session_id = result[0] if result else None
    
    if not session_id:
        return {"success": False, "error": "No session found"}
    
    # Check for section mismatches
    cursor.execute("""
        SELECT 
            e.item_label,
            e.section_name as extraction_section,
            a.section_name as auto_learning_section
        FROM extracted_data e
        JOIN auto_learning_results a ON (
            e.session_id = a.session_id AND 
            e.item_label = a.item_label
        )
        WHERE e.session_id = ? AND e.period_type = 'current'
        AND e.section_name != a.section_name
    """, (session_id,))
    
    mismatches = cursor.fetchall()
    
    conn.close()
    
    return {
        "success": True,
        "session_id": session_id,
        "mismatches": len(mismatches),
        "items": [{"item": item, "extraction": ext, "auto_learning": auto} 
                 for item, ext, auto in mismatches]
    }

if __name__ == "__main__":
    session_id = sys.argv[1] if len(sys.argv) > 1 else None
    result = validate_section_integrity(session_id)
    print(f"Session: {result.get('session_id')}")
    print(f"Mismatches: {result.get('mismatches', 0)}")
    if result.get('items'):
        for item in result['items']:
            print(f"  {item['item']}: {item['extraction']} → {item['auto_learning']}")
