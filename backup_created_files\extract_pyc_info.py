#!/usr/bin/env python3
"""
Extract information from compiled .pyc file
"""

import marshal
import dis
import types
import sys

def extract_pyc_info(pyc_file):
    """Extract basic information from .pyc file"""
    try:
        with open(pyc_file, 'rb') as f:
            # Skip the header (16 bytes for Python 3.11)
            f.read(16)
            
            # Load the code object
            code = marshal.load(f)
            
            print(f"=== ANALYZING {pyc_file} ===")
            print(f"Code object name: {code.co_name}")
            print(f"Filename: {code.co_filename}")
            print(f"First line number: {code.co_firstlineno}")
            print(f"Argument count: {code.co_argcount}")
            print(f"Local variables: {code.co_varnames}")
            print(f"Constants: {code.co_consts[:10]}...")  # First 10 constants
            print(f"Names: {code.co_names[:20]}...")  # First 20 names
            
            print("\n=== DISASSEMBLY (first 50 instructions) ===")
            dis.dis(code)
            
            # Try to find class and function names
            print("\n=== EXTRACTED INFORMATION ===")
            
            # Look for class definitions
            if 'PerfectSectionAwareExtractor' in code.co_names:
                print("✅ Found: PerfectSectionAwareExtractor class")
            
            # Look for method names
            methods = [name for name in code.co_names if name.startswith('extract') or name.startswith('_')]
            print(f"Potential methods: {methods[:10]}")
            
            # Look for imports
            imports = [name for name in code.co_names if name in ['fitz', 'json', 'os', 'sys', 'time', 'datetime']]
            print(f"Imports found: {imports}")
            
            return True
            
    except Exception as e:
        print(f"Error analyzing {pyc_file}: {e}")
        return False

if __name__ == "__main__":
    pyc_file = "__pycache__/perfect_section_aware_extractor.cpython-311.pyc"
    extract_pyc_info(pyc_file)
