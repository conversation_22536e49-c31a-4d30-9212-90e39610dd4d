#!/usr/bin/env python3
"""Test the timeout protections to prevent infinite hanging"""

import sys
import time

def test_timeout_protections():
    print("🧪 TESTING TIMEOUT PROTECTIONS")
    print("=" * 40)
    
    try:
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        # Create manager instance
        manager = PhasedProcessManager(debug_mode=True)
        
        # Get current session
        import sqlite3
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session = cursor.fetchone()[0]
        manager.session_id = session
        conn.close()
        
        print(f"Testing with session: {session}")
        
        # 1. Test _check_interruption timeout protection
        print("\n1. 🔄 Testing _check_interruption timeout protection:")
        
        # Simulate pause condition
        manager.is_paused = True
        manager.is_stopped = False
        
        start_time = time.time()
        try:
            manager._check_interruption()
            elapsed = time.time() - start_time
            print(f"   ✅ _check_interruption completed in {elapsed:.1f}s")
            print(f"   ✅ Auto-resume worked (is_paused: {manager.is_paused})")
        except Exception as e:
            print(f"   ❌ _check_interruption failed: {e}")
        
        # 2. Test _verify_phase_completion timeout protection
        print("\n2. 🔍 Testing _verify_phase_completion timeout protection:")
        
        try:
            start_time = time.time()
            result = manager._verify_phase_completion('EXTRACTION', 1)
            elapsed = time.time() - start_time
            print(f"   ✅ _verify_phase_completion completed in {elapsed:.1f}s")
            print(f"   ✅ Result: {result}")
        except Exception as e:
            print(f"   ❌ _verify_phase_completion failed: {e}")
        
        # 3. Test _get_phase_data_count timeout protection
        print("\n3. 📊 Testing _get_phase_data_count timeout protection:")
        
        try:
            start_time = time.time()
            count = manager._get_phase_data_count('EXTRACTION')
            elapsed = time.time() - start_time
            print(f"   ✅ _get_phase_data_count completed in {elapsed:.1f}s")
            print(f"   ✅ Count: {count}")
        except Exception as e:
            print(f"   ❌ _get_phase_data_count failed: {e}")
        
        # 4. Test overall timeout behavior
        print("\n4. ⏱️ Testing overall timeout behavior:")
        
        # Reset pause state
        manager.is_paused = False
        manager.is_stopped = False
        
        # Test that normal operations complete quickly
        operations = [
            ('Phase verification', lambda: manager._verify_phase_completion('EXTRACTION', 0)),
            ('Data count', lambda: manager._get_phase_data_count('EXTRACTION')),
            ('Interruption check', lambda: manager._check_interruption())
        ]
        
        all_passed = True
        for op_name, operation in operations:
            try:
                start_time = time.time()
                result = operation()
                elapsed = time.time() - start_time
                
                if elapsed < 10:  # Should complete within 10 seconds
                    print(f"   ✅ {op_name}: {elapsed:.1f}s (FAST)")
                else:
                    print(f"   ⚠️ {op_name}: {elapsed:.1f}s (SLOW)")
                    all_passed = False
            except Exception as e:
                print(f"   ❌ {op_name}: Failed - {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def summarize_fixes():
    print("\n" + "=" * 50)
    print("📋 TIMEOUT PROTECTION FIXES SUMMARY")
    print("=" * 50)
    
    print("✅ FIXED INFINITE HANGING POINTS:")
    print("   1. _check_interruption() - Added 5-minute timeout")
    print("   2. _verify_phase_completion() - Added 30-second timeout")
    print("   3. Phase status update - Added 15-second timeout")
    print("   4. Database operations - Already had 30-second timeout")
    
    print("\n🎯 HOW THIS FIXES THE STUCK PROCESS:")
    print("   • Prevents infinite loops in pause/resume logic")
    print("   • Prevents hanging on database verification")
    print("   • Prevents hanging on phase status updates")
    print("   • Forces process to continue even if operations timeout")
    
    print("\n🚀 EXPECTED BEHAVIOR:")
    print("   • Extraction completes → Phase verification (max 30s)")
    print("   • Phase status update (max 15s)")
    print("   • Interruption check (max 5min)")
    print("   • Comparison phase starts automatically")
    print("   • No more infinite hanging!")

if __name__ == "__main__":
    print("🔧 TESTING TIMEOUT PROTECTIONS FOR STUCK PROCESS")
    print("This will verify the underlying hanging issues are fixed\n")
    
    success = test_timeout_protections()
    summarize_fixes()
    
    if success:
        print("\n🎉 TIMEOUT PROTECTIONS WORKING!")
        print("✅ The underlying hanging issues have been resolved")
        print("✅ Future audit processes will not get stuck")
        print("✅ Current running job should continue (if still active)")
    else:
        print("\n⚠️ TIMEOUT PROTECTIONS NEED REFINEMENT")
        print("Some operations may still be vulnerable to hanging")
    
    sys.exit(0 if success else 1)
