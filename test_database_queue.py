#!/usr/bin/env python3
"""
Test Database Queue System
Tests the new file-based database queue to ensure orderly access
"""

import sys
import os
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor

def test_database_queue():
    """Test the database queue system with multiple concurrent processes"""
    print("🧪 TESTING DATABASE QUEUE SYSTEM")
    print("=" * 50)
    
    try:
        # Import the database manager with queue system
        from core.python_database_manager import PythonDatabaseManager
        
        def database_operation(process_name, operation_type, delay=1):
            """Simulate a database operation"""
            try:
                print(f"[TEST] {process_name}: Starting {operation_type} operation...")
                
                # Create database manager with process name
                db_manager = PythonDatabaseManager(process_name=process_name)
                
                if operation_type == "READ":
                    # Simulate read operation
                    result = db_manager.execute_query("SELECT COUNT(*) as count FROM audit_sessions")
                    print(f"[TEST] {process_name}: Read {len(result)} rows")
                    
                elif operation_type == "WRITE":
                    # Simulate write operation
                    test_session_id = f"test_{process_name}_{int(time.time())}"
                    db_manager.execute_update(
                        "INSERT OR IGNORE INTO audit_sessions (session_id, status, created_at) VALUES (?, ?, datetime('now'))",
                        (test_session_id, "test")
                    )
                    print(f"[TEST] {process_name}: Wrote test session {test_session_id}")
                
                # Simulate processing time
                time.sleep(delay)
                
                print(f"[TEST] {process_name}: ✅ {operation_type} operation completed")
                return True
                
            except Exception as e:
                print(f"[TEST] {process_name}: ❌ {operation_type} operation failed: {e}")
                return False
        
        # Test 1: Sequential operations (should work smoothly)
        print("\n📊 TEST 1: Sequential Operations")
        print("-" * 30)
        
        success1 = database_operation("Process1", "READ", 0.5)
        success2 = database_operation("Process2", "WRITE", 0.5)
        success3 = database_operation("Process3", "READ", 0.5)
        
        print(f"Sequential test: {'✅ PASSED' if all([success1, success2, success3]) else '❌ FAILED'}")
        
        # Test 2: Concurrent operations (should queue properly)
        print("\n📊 TEST 2: Concurrent Operations (Queue Test)")
        print("-" * 30)
        
        # Create multiple threads to simulate concurrent access
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            
            # Submit multiple operations simultaneously
            futures.append(executor.submit(database_operation, "ConcurrentRead1", "READ", 1))
            futures.append(executor.submit(database_operation, "ConcurrentWrite1", "WRITE", 1))
            futures.append(executor.submit(database_operation, "ConcurrentRead2", "READ", 1))
            futures.append(executor.submit(database_operation, "ConcurrentWrite2", "WRITE", 1))
            
            # Wait for all operations to complete
            results = [future.result() for future in futures]
            
        print(f"Concurrent test: {'✅ PASSED' if all(results) else '❌ FAILED'}")
        
        # Test 3: Check for lock files cleanup
        print("\n📊 TEST 3: Lock File Cleanup")
        print("-" * 30)
        
        db_path = os.path.join(os.path.dirname(__file__), 'data', 'templar_payroll_auditor.db')
        lock_dir = os.path.join(os.path.dirname(db_path), '.db_locks')
        lock_file = os.path.join(lock_dir, 'database.lock')
        
        if os.path.exists(lock_file):
            # Check if lock file is empty (should be after operations complete)
            try:
                with open(lock_file, 'r') as f:
                    content = f.read().strip()
                if not content:
                    print("✅ Lock file cleanup: PASSED (file is empty)")
                else:
                    print(f"⚠️ Lock file cleanup: WARNING (file contains: {content[:50]}...)")
            except:
                print("✅ Lock file cleanup: PASSED (file not accessible)")
        else:
            print("✅ Lock file cleanup: PASSED (no lock file exists)")
        
        print("\n🎯 OVERALL RESULT")
        print("=" * 50)
        print("✅ Database queue system test completed!")
        print("📋 Check the logs above for queue management messages")
        print("🔍 Look for '[DB-QUEUE]' messages showing orderly access")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_database_queue()
