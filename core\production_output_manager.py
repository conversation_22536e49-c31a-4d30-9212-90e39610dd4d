#!/usr/bin/env python3
"""
PRODUCTION OUTPUT MANAGER
Ensures clean JSON output for IPC communication while preserving debug capabilities
"""

import sys
import json
import os
from typing import Any, Dict, Optional
from contextlib import contextmanager

class ProductionOutputManager:
    """
    Manages output streams to ensure clean JSON responses for production IPC calls
    while preserving debug capabilities for development
    """

    def __init__(self, debug: bool = False, log_file: Optional[str] = None):
        self.debug = debug
        self.log_file = log_file
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr

        # Setup log file if specified
        if self.log_file:
            os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

    def debug_print(self, message: str, force: bool = False):
        """
        Print debug message only if debug is enabled or forced
        Always goes to stderr to avoid contaminating JSON output
        """
        if self.debug or force:
            # Always send debug to stderr to avoid JSON contamination
            print(f"[DEBUG] {message}", file=sys.stderr)
            sys.stderr.flush()

            # Also log to file if specified
            if self.log_file:
                try:
                    with open(self.log_file, 'a', encoding='utf-8') as f:
                        f.write(f"[DEBUG] {message}\n")
                except Exception:
                    pass  # Silently fail to avoid breaking main functionality

    def error_print(self, message: str):
        """
        Print error message to stderr
        """
        print(f"[ERROR] {message}", file=sys.stderr)

        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"[ERROR] {message}\n")
            except Exception:
                pass

    def json_response(self, data: Any):
        """
        Output clean JSON response to stdout
        This is the ONLY method that should write to stdout in production scripts
        """
        try:
            json_str = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            print(json_str, file=sys.stdout)
            sys.stdout.flush()
        except Exception as e:
            # Fallback error response
            error_response = {"success": False, "error": f"JSON serialization failed: {str(e)}"}
            print(json.dumps(error_response), file=sys.stdout)
            sys.stdout.flush()

    def simple_response(self, value: str):
        """
        Output simple string response (like 'true'/'false') to stdout
        """
        print(value, file=sys.stdout)
        sys.stdout.flush()

    @contextmanager
    def capture_output(self):
        """
        Context manager to capture and suppress stdout during operations
        that might produce unwanted output
        """
        if not self.debug:
            # Redirect stdout to devnull in production
            with open(os.devnull, 'w') as devnull:
                old_stdout = sys.stdout
                sys.stdout = devnull
                try:
                    yield
                finally:
                    sys.stdout = old_stdout
        else:
            # In debug mode, allow normal output
            yield

# Global instance for easy access
_output_manager = None

def get_output_manager(debug: bool = False, log_file: Optional[str] = None) -> ProductionOutputManager:
    """
    Get or create global output manager instance
    """
    global _output_manager
    if _output_manager is None:
        # Default log file location
        if log_file is None:
            log_file = os.path.join('logs', 'production_debug.log')
        _output_manager = ProductionOutputManager(debug=debug, log_file=log_file)
    return _output_manager

def debug_print(message: str, force: bool = False):
    """Convenience function for debug printing"""
    get_output_manager().debug_print(message, force)

def error_print(message: str):
    """Convenience function for error printing"""
    get_output_manager().error_print(message)

def json_response(data: Any):
    """Convenience function for JSON responses"""
    get_output_manager().json_response(data)

def simple_response(value: str):
    """Convenience function for simple responses"""
    get_output_manager().simple_response(value)

def setup_production_mode(debug: bool = False, log_file: Optional[str] = None):
    """
    Setup production mode with proper output management
    Call this at the beginning of any script that will be called from IPC
    """
    global _output_manager
    _output_manager = ProductionOutputManager(debug=debug, log_file=log_file)
    return _output_manager

# Decorator for production-safe functions
def production_safe(debug: bool = False):
    """
    Decorator to make functions production-safe by managing output
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            setup_production_mode(debug=debug)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_print(f"Function {func.__name__} failed: {str(e)}")
                json_response({"success": False, "error": str(e)})
                return None
        return wrapper
    return decorator

def test_output_manager():
    """Test the output manager functionality"""
    print("🧪 TESTING PRODUCTION OUTPUT MANAGER")
    print("=" * 50)

    # Test debug mode
    print("\n📝 Testing DEBUG MODE:")
    manager = ProductionOutputManager(debug=True)
    manager.debug_print("This debug message should appear")
    manager.json_response({"test": "debug_mode", "success": True})

    # Test production mode
    print("\n🏭 Testing PRODUCTION MODE:")
    manager = ProductionOutputManager(debug=False)
    manager.debug_print("This debug message should NOT appear")
    manager.json_response({"test": "production_mode", "success": True})

    # Test with capture
    print("\n🔇 Testing OUTPUT CAPTURE:")
    with manager.capture_output():
        print("This should be suppressed in production mode")

    print("\n✅ Output manager test completed")

if __name__ == "__main__":
    test_output_manager()
