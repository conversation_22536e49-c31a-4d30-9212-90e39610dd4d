#!/usr/bin/env python3
"""
Comprehensive Toggle Analysis for Pre-Reporting UI
Analyzes the discrepancy between expected high priority items and actual moderate priority items shown.
"""

import sqlite3
import json
from pathlib import Path

def comprehensive_toggle_analysis():
    """Perform comprehensive analysis of toggle states and pre-reporting data"""
    
    print("🔍 COMPREHENSIVE TOGGLE ANALYSIS FOR PRE-REPORTING UI")
    print("=" * 70)
    
    # Connect to database
    db_path = Path("payroll_audit.db")
    if not db_path.exists():
        print("❌ Database not found!")
        return
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 1. Get current session
        cursor.execute("SELECT DISTINCT session_id FROM comparison_results ORDER BY session_id DESC LIMIT 1")
        session_result = cursor.fetchone()
        current_session = session_result[0] if session_result else None
        
        print(f"📊 Current session: {current_session}")
        
        # 2. Analyze the actual pre-reporting data in detail
        print("\n🔍 DETAILED PRE-REPORTING DATA ANALYSIS:")
        
        cursor.execute("""
            SELECT cr.id, cr.employee_id, cr.employee_name, cr.section, cr.item_label,
                   cr.previous_value, cr.current_value, cr.change_type, cr.priority_level,
                   pr.bulk_category, pr.is_selected
            FROM comparison_results cr
            LEFT JOIN pre_reporting_results pr ON cr.id = pr.change_id
            WHERE cr.session_id = ?
            ORDER BY cr.section, cr.item_label
        """, (current_session,))
        
        all_data = cursor.fetchall()
        
        print(f"Total records: {len(all_data)}")
        print("\nDetailed breakdown:")
        
        priority_mapping = {
            'PERSONAL DETAILS': 'HIGH',
            'EARNINGS': 'HIGH', 
            'DEDUCTIONS': 'HIGH',
            'BANK DETAILS': 'HIGH',
            'LOANS': 'MODERATE',
            'ALLOWANCES': 'LOW',
            'EMPLOYER CONTRIBUTIONS': 'LOW'
        }
        
        for row in all_data:
            change_id, emp_id, emp_name, section, item, prev_val, curr_val, change_type, priority_level, bulk_cat, selected = row
            expected_priority = priority_mapping.get(section.upper() if section else 'UNKNOWN', 'UNKNOWN')
            
            print(f"  📋 {section}.{item}:")
            print(f"     Employee: {emp_id} ({emp_name})")
            print(f"     Change: {change_type} ({prev_val} → {curr_val})")
            print(f"     Priority in DB: {priority_level}")
            print(f"     Expected Priority: {expected_priority}")
            print(f"     Bulk Category: {bulk_cat}")
            print(f"     Selected: {selected}")
            
            if priority_level != expected_priority:
                print(f"     ⚠️ PRIORITY MISMATCH: Expected {expected_priority}, got {priority_level}")
            print()
        
        # 3. Check dictionary state
        print("\n🔧 DICTIONARY STATE ANALYSIS:")
        
        cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
        sections_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM dictionary_items")
        items_count = cursor.fetchone()[0]
        
        print(f"Dictionary sections: {sections_count}")
        print(f"Dictionary items: {items_count}")
        
        if items_count == 0:
            print("⚠️ Dictionary is empty - this explains why toggle states are not working!")
            
            # Check if we can populate dictionary from current data
            print("\n🔧 ATTEMPTING TO ANALYZE AVAILABLE ITEMS:")
            
            cursor.execute("""
                SELECT DISTINCT section, item_label
                FROM comparison_results
                WHERE session_id = ?
                ORDER BY section, item_label
            """, (current_session,))
            
            unique_items = cursor.fetchall()
            
            print(f"Unique section/item combinations in data: {len(unique_items)}")
            for section, item in unique_items:
                expected_priority = priority_mapping.get(section.upper() if section else 'UNKNOWN', 'UNKNOWN')
                print(f"  {section}.{item} (Expected: {expected_priority})")
        
        else:
            # Analyze existing dictionary
            cursor.execute("""
                SELECT ds.section_name, di.item_name, 
                       di.include_new, di.include_increase, di.include_decrease,
                       di.include_removed, di.include_no_change, di.include_in_report
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                ORDER BY ds.section_name, di.item_name
            """)
            
            dictionary_items = cursor.fetchall()
            
            print("Dictionary toggle states:")
            for row in dictionary_items:
                section, item, new, inc, dec, rem, no_change, include = row
                print(f"  {section}.{item}:")
                print(f"    Include in report: {bool(include)}")
                print(f"    Change detection: NEW={bool(new)}, INC={bool(inc)}, DEC={bool(dec)}, REM={bool(rem)}, NO_CHANGE={bool(no_change)}")
        
        # 4. Check if filtering is being applied
        print("\n🔍 FILTERING APPLICATION ANALYSIS:")
        
        # Check if there are items that should be filtered out
        cursor.execute("""
            SELECT cr.section, cr.item_label, cr.change_type, COUNT(*) as count
            FROM comparison_results cr
            WHERE cr.session_id = ?
            GROUP BY cr.section, cr.item_label, cr.change_type
            ORDER BY count DESC
        """, (current_session,))
        
        all_changes = cursor.fetchall()
        
        print(f"All changes in comparison_results: {len(all_changes)}")
        for section, item, change_type, count in all_changes:
            print(f"  {section}.{item}.{change_type}: {count} occurrences")
        
        # Check what made it to pre_reporting_results
        cursor.execute("""
            SELECT pr.section, pr.item_label, pr.change_type, COUNT(*) as count
            FROM pre_reporting_results pr
            WHERE pr.session_id = ?
            GROUP BY pr.section, pr.item_label, pr.change_type
            ORDER BY count DESC
        """, (current_session,))
        
        pre_reporting_changes = cursor.fetchall()
        
        print(f"\nChanges in pre_reporting_results: {len(pre_reporting_changes)}")
        for section, item, change_type, count in pre_reporting_changes:
            print(f"  {section}.{item}.{change_type}: {count} occurrences")
        
        # 5. Priority Analysis
        print("\n⭐ PRIORITY ANALYSIS:")
        
        # Count by priority in comparison_results
        cursor.execute("""
            SELECT priority_level, COUNT(*) as count
            FROM comparison_results
            WHERE session_id = ?
            GROUP BY priority_level
            ORDER BY count DESC
        """, (current_session,))
        
        priority_counts = cursor.fetchall()
        
        print("Priority distribution in comparison_results:")
        for priority, count in priority_counts:
            print(f"  {priority}: {count} changes")
        
        # Count by priority in pre_reporting_results
        cursor.execute("""
            SELECT priority_level, COUNT(*) as count
            FROM pre_reporting_results
            WHERE session_id = ?
            GROUP BY priority_level
            ORDER BY count DESC
        """, (current_session,))
        
        pre_priority_counts = cursor.fetchall()
        
        print("\nPriority distribution in pre_reporting_results:")
        for priority, count in pre_priority_counts:
            print(f"  {priority}: {count} changes")
        
        # 6. User Issue Analysis
        print("\n🎯 USER ISSUE ANALYSIS:")
        print("User expected HIGH priority items but only saw LOANS (MODERATE priority)")
        
        # Check what should be HIGH priority
        high_priority_sections = ['PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'BANK DETAILS']
        
        high_priority_in_comparison = 0
        high_priority_in_pre_reporting = 0
        
        for section in high_priority_sections:
            cursor.execute("""
                SELECT COUNT(*) FROM comparison_results 
                WHERE session_id = ? AND UPPER(section) = ?
            """, (current_session, section))
            
            comp_count = cursor.fetchone()[0]
            high_priority_in_comparison += comp_count
            
            cursor.execute("""
                SELECT COUNT(*) FROM pre_reporting_results 
                WHERE session_id = ? AND UPPER(section) = ?
            """, (current_session, section))
            
            pre_count = cursor.fetchone()[0]
            high_priority_in_pre_reporting += pre_count
            
            print(f"  {section}: {comp_count} in comparison → {pre_count} in pre-reporting")
        
        # Check LOANS specifically
        cursor.execute("""
            SELECT COUNT(*) FROM comparison_results 
            WHERE session_id = ? AND UPPER(section) = 'LOANS'
        """, (current_session,))
        
        loans_in_comparison = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM pre_reporting_results 
            WHERE session_id = ? AND UPPER(section) = 'LOANS'
        """, (current_session,))
        
        loans_in_pre_reporting = cursor.fetchone()[0]
        
        print(f"  LOANS: {loans_in_comparison} in comparison → {loans_in_pre_reporting} in pre-reporting")
        
        # 7. Conclusions
        print("\n📋 CONCLUSIONS:")
        
        if items_count == 0:
            print("❌ MAJOR ISSUE: Dictionary is empty!")
            print("   - Toggle states cannot work without dictionary items")
            print("   - Include/exclude functionality is non-functional")
            print("   - Change detection toggles are non-functional")
        
        if high_priority_in_comparison > high_priority_in_pre_reporting:
            print("❌ HIGH PRIORITY FILTERING ISSUE:")
            print(f"   - {high_priority_in_comparison} high priority items in comparison")
            print(f"   - Only {high_priority_in_pre_reporting} made it to pre-reporting")
            print("   - High priority items are being filtered out incorrectly")
        
        if loans_in_pre_reporting > 0 and high_priority_in_pre_reporting == 0:
            print("❌ PRIORITY INVERSION CONFIRMED:")
            print("   - LOANS (moderate priority) are showing in pre-reporting")
            print("   - HIGH priority items are missing from pre-reporting")
            print("   - This matches the user's reported issue")
        
        # 8. Recommendations
        print("\n💡 RECOMMENDATIONS:")
        
        if items_count == 0:
            print("1. 🔧 Populate the dictionary with current data items")
            print("2. 🔧 Set appropriate toggle states for each item")
            print("3. 🔧 Ensure dictionary is loaded before pre-reporting")
        
        print("4. 🔧 Check priority assignment logic in comparison phase")
        print("5. 🔧 Verify include_in_report filtering is not too restrictive")
        print("6. 🔧 Test change detection toggle enforcement")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

if __name__ == "__main__":
    comprehensive_toggle_analysis()
