#!/usr/bin/env python3
"""
Enhanced Duplicate Prevention for Auto-Learning System
Comprehensive checking against dictionary_items table and auto_learning_results
"""

import sqlite3
import os
from typing import Optional, Tuple

class EnhancedDuplicate<PERSON>he<PERSON>:
    """Enhanced duplicate checker for auto-learning system"""
    
    def __init__(self, database_path: Optional[str] = None):
        self.database_path = database_path or self._get_database_path()
        
    def _get_database_path(self) -> Optional[str]:
        """Get the database path"""
        db_paths = [
            r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
            r"data\templar_payroll_auditor.db",
            r"templar_payroll_auditor.db"
        ]
        
        for path in db_paths:
            if os.path.exists(path):
                return path
        return None
    
    def is_item_duplicate(self, section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
        """
        Comprehensive duplicate check for auto-learning items
        
        Args:
            section_name: Section name (e.g., 'LOANS', 'EARNINGS')
            item_name: Item name to check
            session_id: Current session ID (optional)
            
        Returns:
            Tuple of (is_duplicate: bool, reason: str)
        """
        if not self.database_path:
            return False, "Database not available"
        
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # 1. Check if item exists in dictionary_items table
            cursor.execute("""
                SELECT di.id, di.item_name, di.auto_learned, ds.section_name
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                WHERE ds.section_name = ? AND di.item_name = ?
            """, (section_name, item_name))
            
            dict_result = cursor.fetchone()
            
            if dict_result:
                item_id, name, auto_learned, section = dict_result
                source = "auto-learned" if auto_learned else "manual"
                conn.close()
                return True, f"Already exists in dictionary_items (ID:{item_id}, Source:{source})"
            
            # 2. Check if item exists in dictionary_items with variations
            cursor.execute("""
                SELECT di.id, di.item_name, di.auto_learned, ds.section_name
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                WHERE ds.section_name = ? AND (
                    UPPER(di.item_name) = UPPER(?) OR
                    UPPER(di.standard_key) = UPPER(?)
                )
            """, (section_name, item_name, item_name))
            
            variation_result = cursor.fetchone()
            
            if variation_result:
                item_id, name, auto_learned, section = variation_result
                source = "auto-learned" if auto_learned else "manual"
                conn.close()
                return True, f"Similar item exists in dictionary_items: '{name}' (ID:{item_id}, Source:{source})"
            
            # 3. Check if item is already pending approval in auto_learning_results
            cursor.execute("""
                SELECT id, session_id, auto_approved, dictionary_updated, created_at
                FROM auto_learning_results
                WHERE section_name = ? AND item_label = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (section_name, item_name))
            
            pending_result = cursor.fetchone()
            
            if pending_result:
                result_id, result_session, approved, dict_updated, created = pending_result
                
                # If it's from the current session, it's definitely a duplicate
                if session_id and result_session == session_id:
                    conn.close()
                    return True, f"Already detected in current session (ID:{result_id})"
                
                # If it's approved but not yet transferred, it's a duplicate
                if approved and not dict_updated:
                    conn.close()
                    return True, f"Already approved, pending transfer (ID:{result_id}, Session:{result_session})"
                
                # If it's pending approval from a recent session (within 24 hours)
                try:
                    from datetime import datetime, timedelta
                    created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
                    if datetime.now() - created_dt < timedelta(hours=24):
                        status = "approved" if approved else "pending"
                        conn.close()
                        return True, f"Recently {status} in session {result_session} (ID:{result_id})"
                except:
                    pass  # If datetime parsing fails, continue with other checks
            
            conn.close()
            return False, "Not a duplicate"
            
        except Exception as e:
            return False, f"Error checking duplicates: {e}"
    
    def should_add_to_auto_learning(self, section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
        """
        Determine if an item should be added to auto-learning queue
        
        Args:
            section_name: Section name
            item_name: Item name
            session_id: Current session ID
            
        Returns:
            Tuple of (should_add: bool, reason: str)
        """
        is_duplicate, reason = self.is_item_duplicate(section_name, item_name, session_id)
        
        if is_duplicate:
            return False, f"Duplicate prevented: {reason}"
        else:
            return True, "New item, safe to add"
    
    def clean_duplicate_auto_learning_entries(self, session_id: str = None) -> int:
        """
        Clean duplicate entries from auto_learning_results table
        
        Args:
            session_id: Optional session ID to limit cleaning scope
            
        Returns:
            Number of duplicates removed
        """
        if not self.database_path:
            return 0
        
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Find duplicates in auto_learning_results where items already exist in dictionary_items
            query = """
                SELECT a.id, a.section_name, a.item_label
                FROM auto_learning_results a
                INNER JOIN dictionary_sections ds ON ds.section_name = a.section_name
                INNER JOIN dictionary_items di ON (
                    di.section_id = ds.id AND
                    di.item_name = a.item_label
                )
                WHERE a.auto_approved = 0 AND a.dictionary_updated = 0
            """
            
            params = []
            if session_id:
                query += ' AND a.session_id = ?'
                params.append(session_id)
            
            cursor.execute(query, params)
            duplicates = cursor.fetchall()
            
            if duplicates:
                # Remove the duplicates
                duplicate_ids = [str(dup[0]) for dup in duplicates]
                placeholders = ','.join(['?'] * len(duplicate_ids))
                
                cursor.execute(f"""
                    DELETE FROM auto_learning_results
                    WHERE id IN ({placeholders})
                """, duplicate_ids)
                
                conn.commit()
                
                print(f"   Cleaned {len(duplicates)} duplicate auto-learning entries")
                for dup in duplicates[:5]:  # Show first 5
                    print(f"     Removed: {dup[1]}.{dup[2]} (ID:{dup[0]})")
                
                if len(duplicates) > 5:
                    print(f"     ... and {len(duplicates) - 5} more")
            
            conn.close()
            return len(duplicates)
            
        except Exception as e:
            print(f"Error cleaning duplicates: {e}")
            return 0

# Global instance for easy access
duplicate_checker = EnhancedDuplicateChecker()

def is_item_duplicate(section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
    """Global function for duplicate checking"""
    return duplicate_checker.is_item_duplicate(section_name, item_name, session_id)

def should_add_to_auto_learning(section_name: str, item_name: str, session_id: str = None) -> Tuple[bool, str]:
    """Global function for auto-learning addition check"""
    return duplicate_checker.should_add_to_auto_learning(section_name, item_name, session_id)

def clean_duplicate_auto_learning_entries(session_id: str = None) -> int:
    """Global function for cleaning duplicates"""
    return duplicate_checker.clean_duplicate_auto_learning_entries(session_id)
