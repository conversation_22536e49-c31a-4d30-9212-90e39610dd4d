#!/usr/bin/env python3
"""
BANK ADVISER PERFECT EXTRACTOR
Dedicated copy of Perfect Section-Aware Extractor tuned specifically for Bank Adviser Module
Handles FINAL ADJUSTED PAYSLIP, ALLOWANCES PAYSLIP, and AWARDS PAYSLIP processing
"""

import fitz  # PyMuPDF
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class BankAdviserExtractionResult:
    """Enhanced data structure for Bank Adviser extraction results"""
    document_type: str
    employee_no: str
    employee_name: str
    department: str
    section: str = ""
    job_title: str = ""

    # Final Adjusted Payslip specific
    net_pay: float = 0.0
    gross_salary: float = 0.0
    taxable_salary: float = 0.0
    total_deductions: float = 0.0
    bank: str = ""
    account_no: str = ""
    branch: str = ""

    # Allowances specific
    allowance_type: str = ""
    allowance_gross_amount: float = 0.0
    allowance_tax_amount: float = 0.0
    allowance_payable_amount: float = 0.0

    # Awards specific
    award_type: str = ""
    award_gross_amount: float = 0.0
    award_tax_amount: float = 0.0
    award_payable_amount: float = 0.0

    # Extraction metadata
    extraction_confidence: float = 1.0
    extraction_source: str = "Bank Adviser Perfect Extractor"
    extraction_timestamp: str = ""
    section_detected: str = ""

class BankAdviserPerfectExtractor:
    """
    Bank Adviser Perfect Extractor - Dedicated copy tuned for Bank Adviser needs
    Based on Perfect Section-Aware Extractor with Bank Adviser specific optimizations
    """

    def __init__(self, debug: bool = False):
        self.debug = debug

        # Tax calculation settings
        self.tax_rates = {
            'allowances': 5.0,  # 5% for allowances (especially EDUCATIONAL SUBSIDY)
            'awards': 7.5,      # 7.5% for all awards
            'educational_subsidy': 5.0  # Specific rate for educational subsidy
        }

        # Bank Adviser specific section boundaries (optimized for target documents)
        self.section_boundaries = {
            'PERSONAL DETAILS': {'y_min': 80, 'y_max': 140},
            'EARNINGS': {'y_min': 140, 'y_max': 300, 'x_min': 0, 'x_max': 240},
            'DEDUCTIONS': {'y_min': 140, 'y_max': 320, 'x_min': 240, 'x_max': 600},
            'LOANS': {'y_min': 320, 'y_max': 400},
            'EMPLOYERS CONTRIBUTION': {'y_min': 400, 'y_max': 450},
            'EMPLOYEE BANK DETAILS': {'y_min': 450, 'y_max': 500}
        }

        # Bank Adviser specific field mappings (CORRECTED - KEY FIELDS FOR BANK ADVICE EXCEL)
        self.field_mappings = {
            'final_adjusted': {
                'required': ['EMPLOYEE NO.', 'EMPLOYEE NAME', 'NET PAY', 'BANK'],
                'optional': ['DEPARTMENT', 'SECTION', 'JOB TITLE', 'ACCOUNT NO.', 'BRANCH',
                           'GROSS SALARY', 'TAXABLE SALARY', 'TOTAL DEDUCTIONS']
            },
            'allowances': {
                'required': ['EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT', 'SECTION'],
                # NO HARDCODED EARNINGS ITEMS - Dynamic extraction only
            },
            'awards': {
                'required': ['EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT', 'SECTION', 'NET PAY'],
                # NO HARDCODED EARNINGS ITEMS - Dynamic extraction only
            }
        }

        # Initialize duplicate checker
        try:
            from core.duplicate_checker import DuplicateChecker
        except ImportError:
            # Fallback for when script is run as subprocess
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from core.duplicate_checker import DuplicateChecker

        self.duplicate_checker = DuplicateChecker(debug=self.debug)

        print("[BANK-ADVISER] BANK ADVISER PERFECT EXTRACTOR INITIALIZED")
        if self.debug:
            print(f"   [TAX-RATES] Tax Rates: Allowances {self.tax_rates['allowances']}%, Awards {self.tax_rates['awards']}%")
            print(f"   [TRACKER] New acquisition tracking enabled for Bank Adviser tables")
            print(f"   [DUPLICATE-CHECKER] Duplicate detection enabled (once per year rule)")

    def extract_final_adjusted_payslip(self, pdf_path: str, page_num: int = None,
                                     selected_sections: List[str] = None) -> List[BankAdviserExtractionResult]:
        """
        OPTIMIZED: Extract data from FINAL ADJUSTED PAYSLIP with section-aware processing
        Only processes pages containing selected sections for maximum efficiency
        """
        if page_num is None:
            print(f"[BANK-ADVISER] [FINAL ADJUSTED] OPTIMIZED Processing: {pdf_path}")
            if selected_sections:
                print(f"   [OPTIMIZATION] Target sections: {', '.join(selected_sections)}")
        else:
            print(f"[BANK-ADVISER] [FINAL ADJUSTED] Processing: {pdf_path} Page {page_num}")

        try:
            # OPTIMIZATION 1: Quick section detection to identify target pages
            if page_num is None and selected_sections:
                target_pages = self._identify_section_pages_fast(pdf_path, selected_sections)
                if self.debug:
                    print(f"   [OPTIMIZATION] Found {len(target_pages)} pages with target sections (vs all pages)")
            else:
                # Fallback to original behavior for single page or no filtering
                import fitz
                doc = fitz.open(pdf_path)
                total_pages = len(doc)
                doc.close()

                if page_num is None:
                    target_pages = list(range(1, total_pages + 1))
                else:
                    target_pages = [page_num]

            all_results = []
            processed_employees = 0
            skipped_pages = 0

            for current_page in target_pages:
                # OPTIMIZATION 2: Extract only key fields needed for Bank Advice Excel
                key_fields = ['EMPLOYEE NO.', 'EMPLOYEE NAME', 'NET PAY', 'BANK', 'ACCOUNT NO.', 'BRANCH', 'SECTION', 'DEPARTMENT']
                raw_data = self._extract_key_fields_only(pdf_path, current_page, key_fields)

                if 'error' in raw_data:
                    if self.debug:
                        print(f"   [PAGE] Page {current_page}: Extraction failed - {raw_data.get('error', 'Unknown error')}")
                    continue

                # OPTIMIZATION 3: Early section filtering (before creating full result object)
                employee_section = raw_data.get('SECTION', '')
                if selected_sections and len(selected_sections) > 0:
                    if not employee_section or employee_section.upper() not in [s.upper() for s in selected_sections]:
                        skipped_pages += 1
                        if self.debug:
                            print(f"   [SKIP] Page {current_page}: Section '{employee_section}' not in target sections")
                        continue

                # Create minimal extraction result with only essential data
                result = BankAdviserExtractionResult(
                    document_type="final_adjusted",
                    employee_no=raw_data.get('EMPLOYEE NO.', ''),
                    employee_name=raw_data.get('EMPLOYEE NAME', ''),
                    department=raw_data.get('DEPARTMENT', ''),
                    section=employee_section,
                    net_pay=self._parse_amount(raw_data.get('NET PAY', '0')),
                    bank=raw_data.get('BANK', ''),
                    account_no=raw_data.get('ACCOUNT NO.', ''),
                    branch=raw_data.get('BRANCH', ''),
                    extraction_confidence=1.0,
                    extraction_source="Bank Adviser Perfect Extractor - Optimized",
                    extraction_timestamp=datetime.now().isoformat(),
                    section_detected=employee_section
                )

                processed_employees += 1
                all_results.append(result)

                if self.debug:
                    print(f"   [SUCCESS] Page {current_page}: {result.employee_no} - {result.employee_name} ({employee_section})")

            # Summary logging with optimization metrics
            if page_num is None:
                total_pages = len(target_pages) + skipped_pages
                efficiency = (len(target_pages) / total_pages * 100) if total_pages > 0 else 0
                print(f"   [OPTIMIZED] Processed {processed_employees} employees from {len(target_pages)} pages")
                print(f"   [EFFICIENCY] {efficiency:.1f}% efficiency (skipped {skipped_pages} irrelevant pages)")
                if selected_sections:
                    print(f"   [FILTER] Target sections: {', '.join(selected_sections)}")

            return all_results

        except Exception as e:
            print(f"[ERROR] Final Adjusted extraction failed: {e}")
            return self._create_error_result('final_adjusted', str(e))

    def extract_allowances_payslip(self, pdf_path: str, page_num: int = None,
                                 tax_rate: float = None) -> List[BankAdviserExtractionResult]:
        """
        Extract data from ALLOWANCES PAYSLIP with dynamic allowance detection and tax calculation
        Processes all pages or specific page if page_num is provided
        """
        if page_num is None:
            print(f"[BANK-ADVISER] [ALLOWANCES] Processing all pages: {pdf_path}")
        else:
            print(f"[BANK-ADVISER] [ALLOWANCES] Processing: {pdf_path} Page {page_num}")

        if tax_rate is not None:
            self.tax_rates['allowances'] = tax_rate
            if self.debug:
                print(f"   [TAX-RATE] Using custom tax rate: {tax_rate}%")

        try:
            # Determine pages to process
            import fitz
            doc = fitz.open(pdf_path)
            total_pages = len(doc)
            doc.close()

            if page_num is None:
                pages_to_process = list(range(1, total_pages + 1))  # Process ALL pages
                if self.debug:
                    print(f"   [PAGES] Processing all {total_pages} pages")
            else:
                pages_to_process = [page_num]

            all_results = []

            for current_page in pages_to_process:
                # Extract all text elements for dynamic allowance detection
                all_elements = self._extract_all_text_elements(pdf_path, current_page)

                if not all_elements:
                    continue

                # Extract basic employee data for each page (each slip)
                raw_data = self._extract_by_coordinates(all_elements)
                if 'error' in raw_data:
                    continue

                # Dynamically find all allowance items in EARNINGS section for this employee
                allowance_items = self._find_allowance_items_dynamic(all_elements)

                if self.debug and allowance_items:
                    print(f"   [PAGE] Page {current_page}: Found {len(allowance_items)} allowance items")

                # Process allowances for this specific employee
                for allowance_type, gross_amount in allowance_items:
                    if self.debug:
                        print(f"      [PROCESSING] {allowance_type} = {gross_amount} for {raw_data.get('EMPLOYEE NO.', 'Unknown')}")

                    # Determine if this allowance type is taxable
                    is_taxable = self._is_allowance_taxable(allowance_type)

                    if is_taxable:
                        # Determine tax rate (special handling for EDUCATIONAL SUBSIDY)
                        if 'EDUCATIONAL' in allowance_type.upper() and 'SUBSIDY' in allowance_type.upper():
                            effective_tax_rate = self.tax_rates['educational_subsidy']
                        else:
                            effective_tax_rate = self.tax_rates['allowances']

                        # Calculate tax and payable amount
                        tax_amount = gross_amount * (effective_tax_rate / 100)
                        payable_amount = gross_amount - tax_amount
                    else:
                        # Non-taxable allowance
                        effective_tax_rate = 0.0
                        tax_amount = 0.0
                        payable_amount = gross_amount

                    result = BankAdviserExtractionResult(
                        document_type="allowances",
                        employee_no=raw_data.get('EMPLOYEE NO.', ''),
                        employee_name=raw_data.get('EMPLOYEE NAME', ''),
                        department=raw_data.get('DEPARTMENT', ''),
                        section=raw_data.get('SECTION', ''),
                        allowance_type=allowance_type,
                        allowance_gross_amount=gross_amount,
                        allowance_tax_amount=round(tax_amount, 2),
                        allowance_payable_amount=round(payable_amount, 2),
                        extraction_confidence=1.0,
                        extraction_source="Bank Adviser Perfect Extractor - Allowances",
                        extraction_timestamp=datetime.now().isoformat()
                    )

                    all_results.append(result)

                    if self.debug:
                        tax_status = "NON-TAXABLE" if not is_taxable else f"TAXABLE ({effective_tax_rate}%)"
                        print(f"   [SUCCESS] Allowance: {allowance_type} - {tax_status}")
                        print(f"   [AMOUNTS] Gross: {gross_amount}, Tax: {tax_amount:.2f}, Payable: {payable_amount:.2f}")

            # OPTIMIZATION: Immediate tracker processing for allowances
            if all_results:
                self._process_allowance_acquisitions_immediate(all_results)

            return all_results

        except Exception as e:
            print(f"[ERROR] Allowances extraction failed: {e}")
            return []

    def extract_awards_payslip(self, pdf_path: str, page_num: int = None,
                             tax_rate: float = None) -> List[BankAdviserExtractionResult]:
        """
        Extract data from AWARDS PAYSLIP with dynamic award detection and tax calculation
        Processes all pages or specific page if page_num is provided
        """
        if page_num is None:
            print(f"[BANK-ADVISER] [AWARDS] Processing all pages: {pdf_path}")
        else:
            print(f"[BANK-ADVISER] [AWARDS] Processing: {pdf_path} Page {page_num}")

        if tax_rate is not None:
            self.tax_rates['awards'] = tax_rate
            if self.debug:
                print(f"   [TAX-RATE] Using custom tax rate: {tax_rate}%")

        try:
            # Determine pages to process
            import fitz
            doc = fitz.open(pdf_path)
            total_pages = len(doc)
            doc.close()

            if page_num is None:
                pages_to_process = list(range(1, total_pages + 1))  # Process all pages for awards
                if self.debug:
                    print(f"   [PAGES] Processing all {total_pages} pages")
            else:
                pages_to_process = [page_num]

            all_results = []

            for current_page in pages_to_process:
                # Extract all text elements for dynamic award detection
                all_elements = self._extract_all_text_elements(pdf_path, current_page)

                if not all_elements:
                    continue

                # Extract basic employee data for each page (each slip)
                raw_data = self._extract_by_coordinates(all_elements)
                if 'error' in raw_data:
                    continue

                # Dynamically find all award items in EARNINGS section for this employee
                award_items = self._find_award_items_dynamic(all_elements)

                if self.debug and award_items:
                    print(f"   [PAGE] Page {current_page}: Found {len(award_items)} award items")

                # Process awards for this specific employee
                for award_type, gross_amount in award_items:
                    if self.debug:
                        print(f"      [PROCESSING] {award_type} = {gross_amount} for {raw_data.get('EMPLOYEE NO.', 'Unknown')}")

                    # All awards are taxable at the awards tax rate (7.5%)
                    effective_tax_rate = self.tax_rates['awards']

                    # Calculate tax and payable amount
                    tax_amount = gross_amount * (effective_tax_rate / 100)
                    payable_amount = gross_amount - tax_amount

                    result = BankAdviserExtractionResult(
                        document_type="awards",
                        employee_no=raw_data.get('EMPLOYEE NO.', ''),
                        employee_name=raw_data.get('EMPLOYEE NAME', ''),
                        department=raw_data.get('DEPARTMENT', ''),
                        section=raw_data.get('SECTION', ''),
                        net_pay=self._parse_amount(raw_data.get('NET PAY', '0')),
                        award_type=award_type,
                        award_gross_amount=gross_amount,
                        award_tax_amount=round(tax_amount, 2),
                        award_payable_amount=round(payable_amount, 2),
                        extraction_confidence=1.0,
                        extraction_source="Bank Adviser Perfect Extractor - Awards",
                        extraction_timestamp=datetime.now().isoformat()
                    )

                    all_results.append(result)

                    if self.debug:
                        print(f"   [SUCCESS] Award: {award_type} - TAXABLE ({effective_tax_rate}%)")
                        print(f"   [AMOUNTS] Gross: {gross_amount}, Tax: {tax_amount:.2f}, Payable: {payable_amount:.2f}")

            # OPTIMIZATION: Immediate tracker processing for awards
            if all_results:
                self._process_award_acquisitions_immediate(all_results)

            return all_results

        except Exception as e:
            print(f"[ERROR] Awards extraction failed: {e}")
            return []

    def _extract_perfect_data(self, pdf_path: str, page_num: int = 1) -> Dict:
        """
        Core extraction method using Perfect Section-Aware logic
        Tuned specifically for Bank Adviser document types
        """
        try:
            # Extract all text elements from PDF
            all_elements = self._extract_all_text_elements(pdf_path, page_num)

            if not all_elements:
                return {'error': 'No text elements found in PDF'}

            if self.debug:
                print(f"   [ELEMENTS] Found {len(all_elements)} text elements")

            # Use direct coordinate-based extraction for better accuracy
            extracted_data = self._extract_by_coordinates(all_elements)

            return extracted_data

        except Exception as e:
            return {'error': f'Extraction failed: {str(e)}'}

    def _extract_by_coordinates(self, all_elements: List[Dict]) -> Dict:
        """Extract data using direct coordinate analysis for better accuracy"""
        extracted_data = {}

        # Sort elements by position for easier processing
        sorted_elements = sorted(all_elements, key=lambda x: (x['y'], x['x']))

        # Define specific label-value mappings based on coordinate analysis
        target_labels = {
            'Employee No.': 'EMPLOYEE NO.',
            'Employee Name': 'EMPLOYEE NAME',
            'Department': 'DEPARTMENT',
            'Section': 'SECTION',
            'Job Title': 'JOB TITLE',
            'SSF No.': 'SSF NO.',
            'Ghana Card ID': 'GHANA CARD ID',
            'NET PAY': 'NET PAY',
            'GROSS SALARY': 'GROSS SALARY',
            'TAXABLE SALARY': 'TAXABLE SALARY',
            'TOTAL DEDUCTIONS': 'TOTAL DEDUCTIONS',
            'BASIC SALARY': 'BASIC SALARY',
            'Bank': 'BANK',
            'Account No.': 'ACCOUNT NO.',
            'Branch': 'BRANCH'
        }

        # Find each target label and its corresponding value
        for i, elem in enumerate(sorted_elements):
            text = elem['text'].strip()

            if text in target_labels:
                target_key = target_labels[text]

                # Find the value for this label
                value = self._find_value_for_label(elem, sorted_elements, i)

                if value:
                    extracted_data[target_key] = value
                    if self.debug:
                        print(f"   [FOUND] {target_key}: {value}")

        return extracted_data

    def _find_value_for_label(self, label_elem: Dict, all_elements: List[Dict], label_index: int) -> str:
        """Find value for a specific label using improved coordinate logic"""
        label_x = label_elem['x']
        label_y = label_elem['y']
        label_text = label_elem['text'].strip()

        if self.debug:
            print(f"   [SEARCH] Looking for value for '{label_text}' @ ({label_x:.1f}, {label_y:.1f})")

        # Look for values in proximity to the label
        candidates = []

        # Check all elements, not just those after the label
        for i, elem in enumerate(all_elements):
            if i == label_index:  # Skip the label itself
                continue

            elem_text = elem['text'].strip()

            # Skip empty text
            if not elem_text or len(elem_text) < 1:
                continue

            # Skip if this looks like another label
            if elem_text in ['Employee No.', 'Employee Name', 'Department', 'Section', 'Job Title',
                           'SSF No.', 'Ghana Card ID', 'NET PAY', 'GROSS SALARY', 'TAXABLE SALARY',
                           'TOTAL DEDUCTIONS', 'BASIC SALARY', 'Bank', 'Account No.', 'Branch']:
                continue

            # Skip decorative elements and headers
            if (elem_text.startswith('_') or elem_text in ['AMT (GHS)', 'EARNINGS', 'DEDUCTIONS',
                'THE CHURCH OF PENTECOST', 'March 2025', 'Period', 'Printed on:', 'Akatua by theSOFTtribe']):
                continue

            elem_x = elem['x']
            elem_y = elem['y']

            # Calculate distances
            x_distance = elem_x - label_x
            y_distance = elem_y - label_y

            # For specific positioning patterns based on the payslip layout
            # Same line (to the right) - most common pattern
            if abs(y_distance) < 8 and x_distance > 10 and x_distance < 300:
                distance_score = abs(x_distance)
                candidates.append((elem_text, distance_score, 1))  # Priority 1 for same line
                if self.debug:
                    print(f"      [CANDIDATE] Same line candidate: '{elem_text}' @ ({elem_x:.1f}, {elem_y:.1f}) distance: {distance_score:.1f}")

            # Below and aligned (for some labels)
            elif y_distance > 0 and y_distance < 25 and abs(x_distance) < 50:
                distance_score = abs(x_distance) + abs(y_distance)
                candidates.append((elem_text, distance_score, 2))  # Priority 2 for below
                if self.debug:
                    print(f"      [CANDIDATE] Below candidate: '{elem_text}' @ ({elem_x:.1f}, {elem_y:.1f}) distance: {distance_score:.1f}")

        # Return best candidate
        if candidates:
            candidates.sort(key=lambda x: (x[2], x[1]))  # Sort by priority, then distance
            best_value = candidates[0][0]
            if self.debug:
                print(f"      [MATCH] Best match: '{best_value}'")
            return best_value

        if self.debug:
            print(f"      [NO-MATCH] No value found for '{label_text}'")
        return ""

    def _extract_all_text_elements(self, pdf_path: str, page_num: int) -> List[Dict]:
        """Extract all text elements with coordinates from PDF page"""
        try:
            doc = fitz.open(pdf_path)
            page = doc[page_num - 1]  # Convert to 0-based index

            # Get text with detailed information
            text_dict = page.get_text("dict")
            elements = []

            for block in text_dict["blocks"]:
                if "lines" in block:  # Text block
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:  # Only include non-empty text
                                elements.append({
                                    'text': text,
                                    'x': span["bbox"][0],
                                    'y': span["bbox"][1],
                                    'width': span["bbox"][2] - span["bbox"][0],
                                    'height': span["bbox"][3] - span["bbox"][1],
                                    'font': span.get("font", ""),
                                    'size': span.get("size", 0),
                                    'flags': span.get("flags", 0)
                                })

            doc.close()
            return elements

        except Exception as e:
            print(f"[ERROR] Error extracting text elements: {e}")
            return []

    def _detect_section_boundaries(self, all_elements: List[Dict]) -> Dict:
        """
        Detect section boundaries dynamically
        Optimized for Bank Adviser document types
        """
        # Find main headings (bold text that indicates sections)
        main_headings = {}

        for elem in all_elements:
            text = elem['text'].strip().upper()

            # Check if this is a bold main heading
            is_bold = self._is_bold_text(elem)

            # Bank Adviser specific section patterns
            if is_bold and any(keyword in text for keyword in [
                'EARNINGS', 'DEDUCTIONS', 'LOANS', 'BANK', 'DETAILS', 'CONTRIBUTION'
            ]):
                main_headings[text] = elem

        # Create boundaries based on detected headings
        boundaries = {}

        # Sort headings by Y position
        sorted_headings = sorted(main_headings.items(), key=lambda x: x[1]['y'])

        # Create boundaries between sections
        for i, (section_name, elem) in enumerate(sorted_headings):
            y_start = elem['y']

            # Find next section or use document end
            if i + 1 < len(sorted_headings):
                y_end = sorted_headings[i + 1][1]['y']
            else:
                y_end = max(e['y'] for e in all_elements) + 50

            boundaries[section_name] = {
                'y_min': y_start,
                'y_max': y_end
            }

        # Add PERSONAL DETAILS (usually at top with no heading)
        if 'PERSONAL DETAILS' not in boundaries:
            min_y = min(e['y'] for e in all_elements)
            first_section_y = min(b['y_min'] for b in boundaries.values()) if boundaries else min_y + 100

            boundaries['PERSONAL DETAILS'] = {
                'y_min': min_y,
                'y_max': first_section_y
            }

        # Use fallback boundaries if detection failed
        if not boundaries:
            boundaries = self.section_boundaries.copy()

        return boundaries

    def _is_bold_text(self, elem: Dict) -> bool:
        """Check if text element is bold based on font flags"""
        flags = elem.get('flags', 0)
        # Font flag 16 typically indicates bold
        return bool(flags & 16)

    def _get_section_elements(self, all_elements: List[Dict], boundaries: Dict) -> List[Dict]:
        """Get elements within section boundaries"""
        section_elements = []

        for elem in all_elements:
            y = elem['y']
            x = elem['x']

            # Check Y boundaries
            if boundaries['y_min'] <= y <= boundaries['y_max']:
                # Check X boundaries if specified
                if 'x_min' in boundaries and 'x_max' in boundaries:
                    if boundaries['x_min'] <= x <= boundaries['x_max']:
                        section_elements.append(elem)
                else:
                    section_elements.append(elem)

        return section_elements

    def _extract_section_data(self, section_elements: List[Dict], section_name: str) -> Dict:
        """Extract label-value pairs from section elements"""
        extracted_data = {}

        if not section_elements:
            return extracted_data

        # Sort elements by position (Y first, then X)
        section_elements.sort(key=lambda x: (x['y'], x['x']))

        # Find label-value pairs
        pairs = self._find_label_value_pairs(section_elements, section_name)

        for pair in pairs:
            label = pair['label']['text'].strip()
            value = pair['value']['text'].strip()

            # Clean up label (remove colons, etc.)
            label = label.rstrip(':').strip()

            # Store the pair
            extracted_data[label] = value

        return extracted_data

    def _find_label_value_pairs(self, elements: List[Dict], section_name: str) -> List[Dict]:
        """Find label-value pairs in section elements"""
        pairs = []

        for i, elem in enumerate(elements):
            text = elem['text'].strip()

            # Skip empty or very short text
            if len(text) < 2:
                continue

            # Check if this looks like a label
            if self._is_likely_label(text, section_name):
                # Look for corresponding value
                value_elem = self._find_corresponding_value(elem, elements, i)

                if value_elem:
                    pairs.append({
                        'label': elem,
                        'value': value_elem
                    })

        return pairs

    def _is_likely_label(self, text: str, section_name: str) -> bool:
        """Check if text is likely a field label with improved detection"""
        text_upper = text.upper().strip()

        # Skip very short text
        if len(text_upper) < 3:
            return False

        # Bank Adviser specific label patterns (exact matches preferred)
        exact_labels = [
            'EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT', 'SECTION', 'JOB TITLE',
            'NET PAY', 'GROSS SALARY', 'TAXABLE SALARY', 'TOTAL DEDUCTIONS',
            'BANK', 'ACCOUNT NO.', 'BRANCH', 'BASIC SALARY', 'SSF NO.',
            'GHANA CARD ID', 'PERIOD'
        ]

        # Check for exact matches first
        for pattern in exact_labels:
            if text_upper == pattern or text_upper == pattern.rstrip('.'):
                return True

        # Partial matches for allowances and awards
        partial_patterns = [
            'EDUCATIONAL SUBSIDY', 'LEAVE ALLOWANCE', 'LEAVE CLAIMS',
            'LONG SERVICE AWARD', 'SERVICE AWARD', 'PERFORMANCE AWARD',
            'ALLOWANCE', 'AWARD', 'SUBSIDY'
        ]

        for pattern in partial_patterns:
            if pattern in text_upper:
                return True

        # General label characteristics
        if text.endswith(':') or text.endswith('.'):
            return True

        # Avoid financial amounts
        if self._is_likely_financial_amount(text):
            return False

        # Avoid common non-label text
        non_labels = ['AMT', 'GHS', 'BOLD', 'THE CHURCH', 'PENTECOST', 'MARCH', '2025']
        if any(non_label in text_upper for non_label in non_labels):
            return False

        return False

    def _find_corresponding_value(self, label_elem: Dict, all_elements: List[Dict],
                                label_index: int) -> Optional[Dict]:
        """Find the value element corresponding to a label with improved logic"""
        label_x = label_elem['x']
        label_y = label_elem['y']
        label_text = label_elem['text'].upper()

        # Look for value to the right or below the label
        candidates = []

        for i, elem in enumerate(all_elements):
            if i <= label_index:  # Skip elements before or at label
                continue

            elem_x = elem['x']
            elem_y = elem['y']
            elem_text = elem['text'].strip()

            # Skip if this element looks like another label
            if self._is_likely_label(elem_text, ""):
                continue

            # Skip empty or very short text
            if len(elem_text) < 1:
                continue

            # Calculate distance and position relationship
            x_distance = elem_x - label_x
            y_distance = elem_y - label_y
            total_distance = abs(x_distance) + abs(y_distance)

            # Check if element is positioned as a potential value
            is_to_right = x_distance > 0 and abs(y_distance) < 15  # Same line, to the right
            is_below = y_distance > 0 and y_distance < 25 and abs(x_distance) < 100  # Below and close

            if is_to_right or is_below:
                # Prefer elements to the right over elements below
                priority = 1 if is_to_right else 2
                candidates.append((elem, total_distance, priority))

        # Return best candidate (prioritize right-side values, then closest)
        if candidates:
            candidates.sort(key=lambda x: (x[2], x[1]))  # Sort by priority, then distance
            return candidates[0][0]

        return None

    def _identify_section_pages_fast(self, pdf_path: str, selected_sections: List[str]) -> List[int]:
        """
        OPTIMIZATION: Quickly identify which pages contain the selected sections
        Scans only first few elements of each page for section identification
        """
        target_pages = []

        try:
            import fitz
            doc = fitz.open(pdf_path)

            if self.debug:
                print(f"   [FAST-SCAN] Scanning {len(doc)} pages for sections: {selected_sections}")

            for page_num in range(len(doc)):
                page = doc[page_num]

                # Extract ALL text elements (no limits for payroll processing)
                text_dict = page.get_text("dict")
                quick_elements = []

                for block in text_dict["blocks"]:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text = span["text"].strip()
                                if text:
                                    quick_elements.append({
                                        'text': text,
                                        'y': span["bbox"][1]
                                    })

                # Check if this page contains any of the selected sections
                page_section = self._identify_section_from_elements(quick_elements)

                if page_section and page_section.upper() in [s.upper() for s in selected_sections]:
                    target_pages.append(page_num + 1)  # Convert to 1-based page numbering
                    if self.debug:
                        print(f"      [MATCH] Page {page_num + 1}: Contains section '{page_section}'")

            doc.close()

            if self.debug:
                print(f"   [FAST-SCAN] Found {len(target_pages)} target pages out of {len(doc)} total pages")

            return target_pages

        except Exception as e:
            if self.debug:
                print(f"   [FAST-SCAN] Error during fast scan: {e}, falling back to all pages")
            # Fallback to processing all pages
            import fitz
            doc = fitz.open(pdf_path)
            all_pages = list(range(1, len(doc) + 1))
            doc.close()
            return all_pages

    def _identify_section_from_elements(self, elements: List[Dict]) -> str:
        """
        OPTIMIZATION: Identify section from first few elements of a page
        Looks for section indicators in employee department/section fields
        """
        # Sort elements by Y position (top to bottom)
        sorted_elements = sorted(elements, key=lambda x: x['y'])

        # Look for section indicators in the first few elements
        for elem in sorted_elements[:15]:  # Check first 15 elements
            text = elem['text'].strip().upper()

            # Check for direct section names
            if text in ['HEADQUARTERS', 'ABUAKWA', 'SALAGA', 'MISSIONARIES']:
                return text

            # Check for department patterns that indicate sections
            if 'HEADQUARTERS' in text:
                return 'HEADQUARTERS'
            elif 'ABUAKWA' in text:
                return 'ABUAKWA'
            elif 'SALAGA' in text:
                return 'SALAGA'
            elif 'MISSIONARIES' in text:
                return 'MISSIONARIES'

        return ''

    def _extract_key_fields_only(self, pdf_path: str, page_num: int, key_fields: List[str]) -> Dict:
        """
        OPTIMIZATION: Extract only the specified key fields (not all fields)
        Reduces processing time and memory usage significantly
        """
        try:
            # Extract all text elements
            all_elements = self._extract_all_text_elements(pdf_path, page_num)

            if not all_elements:
                return {'error': 'No text elements found'}

            # Sort elements for processing
            sorted_elements = sorted(all_elements, key=lambda x: (x['y'], x['x']))

            # Extract only the key fields we need
            extracted_data = {}

            # Define label mappings for key fields only
            key_label_mappings = {
                'Employee No.': 'EMPLOYEE NO.',
                'Employee Name': 'EMPLOYEE NAME',
                'Department': 'DEPARTMENT',
                'Section': 'SECTION',
                'NET PAY': 'NET PAY',
                'Bank': 'BANK',
                'Account No.': 'ACCOUNT NO.',
                'Branch': 'BRANCH'
            }

            # Find only the key fields
            for i, elem in enumerate(sorted_elements):
                text = elem['text'].strip()

                if text in key_label_mappings and key_label_mappings[text] in key_fields:
                    target_key = key_label_mappings[text]

                    # Find value for this key field
                    value = self._find_value_for_label(elem, sorted_elements, i)

                    if value:
                        extracted_data[target_key] = value
                        if self.debug:
                            print(f"   [KEY-FIELD] {target_key}: {value}")

            return extracted_data

        except Exception as e:
            return {'error': f'Key fields extraction failed: {str(e)}'}

    def _is_likely_financial_amount(self, text: str) -> bool:
        """Check if text looks like a financial amount"""
        # Remove common currency symbols and separators
        cleaned = text.replace(',', '').replace('GHS', '').replace('₵', '').strip()

        try:
            float(cleaned)
            return True
        except ValueError:
            return False

    def _find_allowance_items(self, raw_data: Dict) -> List[Tuple[str, float]]:
        """Find allowance items from extracted data with enhanced detection"""
        allowance_items = []

        if self.debug:
            print(f"   [SEARCH] Searching for allowances in extracted data: {list(raw_data.keys())}")

        # Enhanced allowance patterns with priority (COMPREHENSIVE)
        allowance_patterns = [
            'EDUCATIONAL SUBSIDY', 'LEAVE ALLOWANCE', 'LEAVE CLAIMS', 'LEAVE PAYMENT',
            'TRANSPORT ALLOWANCE', 'HOUSING ALLOWANCE', 'MEDICAL ALLOWANCE',
            'FUEL ALLOWANCE', 'COMMUNICATION ALLOWANCE', 'ENTERTAINMENT ALLOWANCE',
            'RESPONSIBILITY ALLOWANCE', 'ACTING ALLOWANCE', 'PROFESSIONAL ALLOWANCE',
            'TECHNICAL ALLOWANCE', 'OVERTIME ALLOWANCE', 'SHIFT ALLOWANCE',
            'HAZARD ALLOWANCE', 'SPECIAL ALLOWANCE', 'MEAL ALLOWANCE',
            'UNIFORM ALLOWANCE', 'TRAVEL ALLOWANCE', 'TRAINING ALLOWANCE',
            'SUBSIDY', 'ALLOWANCE', 'CLAIMS', 'PAYMENT', 'BENEFIT'
        ]

        # Check extracted data for allowance patterns
        for key, value in raw_data.items():
            key_upper = key.upper().strip()

            # Skip if this looks like a deduction or other non-allowance item
            if any(exclude in key_upper for exclude in ['TAX', 'DEDUCTION', 'SSF', 'LOAN', 'ADVANCE']):
                continue

            # Check for allowance patterns
            if any(pattern in key_upper for pattern in allowance_patterns):
                amount = self._parse_amount(value)
                if amount > 0:
                    allowance_items.append((key, amount))
                    if self.debug:
                        print(f"   [FOUND] Found allowance: {key} = {amount}")

        # If no allowances found in extracted data, look for them in the document structure
        if not allowance_items:
            if self.debug:
                print(f"   [SEARCH] No allowances in extracted data, searching document structure...")

            # For allowance documents, look for the actual allowance amount (NOT GROSS SALARY)
            # The allowance amount should be the specific allowance item, not the total gross
            net_pay = self._parse_amount(raw_data.get('NET PAY', '0'))

            # Look for specific allowance amounts in the document
            allowance_amount = 0
            allowance_type = 'LEAVE ALLOWANCE'

            # Check for specific allowance items first
            for key, value in raw_data.items():
                key_upper = key.upper()
                if 'ALLOWANCE' in key_upper or 'LEAVE' in key_upper:
                    amount = self._parse_amount(value)
                    if amount > 0 and amount != net_pay:  # Don't use net pay as allowance amount
                        allowance_amount = amount
                        allowance_type = key
                        break

            # If no specific allowance found, use a reasonable portion of net pay
            if allowance_amount == 0 and net_pay > 0:
                # Use net pay as the allowance amount (this is the actual allowance payment)
                allowance_amount = net_pay

            if allowance_amount > 0:
                allowance_items.append((allowance_type, allowance_amount))
                if self.debug:
                    print(f"   [INFERRED] Inferred allowance: {allowance_type} = {allowance_amount}")

        return allowance_items

    def _find_allowance_items_dynamic(self, all_elements: List[Dict]) -> List[Tuple[str, float]]:
        """
        Dynamically find all allowance items in the EARNINGS section
        No hardcoded patterns - detects items based on document structure
        """
        allowance_items = []

        if self.debug:
            print(f"   [DYNAMIC] Dynamic allowance detection from {len(all_elements)} elements")

        # Find EARNINGS section boundaries
        earnings_start_y = None
        earnings_end_y = None

        for elem in all_elements:
            text = elem['text'].strip().upper()
            if text == 'EARNINGS':
                earnings_start_y = elem['y']
                if self.debug:
                    print(f"   [SECTION] EARNINGS section starts at Y: {earnings_start_y}")
                break

        # Find where EARNINGS section ends (usually at GROSS SALARY or next major section)
        if earnings_start_y:
            for elem in all_elements:
                text = elem['text'].strip().upper()
                y = elem['y']
                if y > earnings_start_y and (text in ['GROSS SALARY', 'NET PAY', 'TOTAL DEDUCTIONS', 'DEDUCTIONS']):
                    earnings_end_y = y
                    if self.debug:
                        print(f"   [SECTION] EARNINGS section ends at Y: {earnings_end_y} (found: {text})")
                    break

        if not earnings_start_y or not earnings_end_y:
            if self.debug:
                print(f"   [ERROR] Could not determine EARNINGS section boundaries")
            return allowance_items

        # Get all elements in EARNINGS section
        earnings_elements = []
        for elem in all_elements:
            if earnings_start_y < elem['y'] < earnings_end_y:
                earnings_elements.append(elem)

        if self.debug:
            print(f"   [ELEMENTS] Found {len(earnings_elements)} elements in EARNINGS section")

        # Sort by Y position to process line by line
        earnings_elements.sort(key=lambda x: (x['y'], x['x']))

        # Find allowance items by looking for label-value pairs
        i = 0
        while i < len(earnings_elements):
            elem = earnings_elements[i]
            text = elem['text'].strip()

            # Skip headers, decorative elements, pure numbers, and non-allowance items
            if (text.upper() in ['EARNINGS', 'AMT (GHS)', 'DEDUCTIONS', 'GROSS SALARY', 'NET PAY', 'INCOME TAX', 'TOTAL DEDUCTIONS'] or
                text.startswith('_') or
                self._is_likely_financial_amount(text)):
                i += 1
                continue

            # This could be an allowance label - look for corresponding value
            allowance_value = self._find_value_in_earnings_line(elem, earnings_elements)

            if allowance_value and allowance_value > 0:
                allowance_items.append((text, allowance_value))
                if self.debug:
                    print(f"   [DETECTED] Dynamic detection: {text} = {allowance_value}")

            i += 1

        return allowance_items

    def _find_value_in_earnings_line(self, label_elem: Dict, earnings_elements: List[Dict]) -> float:
        """Find the monetary value corresponding to an allowance label in the same line"""
        label_y = label_elem['y']
        label_x = label_elem['x']

        # Look for numeric values on the same line (within 5 pixels Y difference)
        for elem in earnings_elements:
            if abs(elem['y'] - label_y) <= 5 and elem['x'] > label_x:  # Same line, to the right
                text = elem['text'].strip()
                amount = self._parse_amount(text)
                if amount > 0:
                    return amount

        return 0.0

    def _is_allowance_taxable(self, allowance_type: str) -> bool:
        """
        Determine if an allowance type is taxable
        LEAVE ALLOWANCE and LEAVE CLAIMS are NOT taxable among allowance types
        """
        allowance_upper = allowance_type.upper()

        # Non-taxable allowance types (based on analysis of real documents)
        non_taxable_types = [
            'LEAVE ALLOWANCE',
            'LEAVE CLAIMS'  # Confirmed from document analysis - shows INCOME TAX: 0.00
        ]

        # Check if this allowance is non-taxable
        for non_taxable in non_taxable_types:
            if non_taxable in allowance_upper:
                if self.debug:
                    print(f"   [NON-TAXABLE] {allowance_type} is NON-TAXABLE")
                return False

        # All other allowances are taxable (including EDUCATIONAL SUBSIDY)
        if self.debug:
            print(f"   [TAXABLE] {allowance_type} is TAXABLE")
        return True

    def _find_award_items(self, raw_data: Dict) -> List[Tuple[str, float]]:
        """Find award items from extracted data with enhanced detection"""
        award_items = []

        if self.debug:
            print(f"   [SEARCH] Searching for awards in extracted data: {list(raw_data.keys())}")

        # Award patterns (COMPREHENSIVE - Extract ALL EARNINGS items for awards)
        award_patterns = [
            'LONG SERVICE AWARD', 'SERVICE AWARD', 'PERFORMANCE AWARD',
            'RECOGNITION AWARD', 'EXCELLENCE AWARD', 'ACHIEVEMENT AWARD',
            'PRODUCTIVITY AWARD', 'INNOVATION AWARD', 'LOYALTY AWARD',
            'DEDICATION AWARD', 'OUTSTANDING AWARD', 'MERIT AWARD',
            'BONUS', 'AWARD', 'INCENTIVE', 'REWARD', 'PRIZE', 'GIFT',
            'APPRECIATION', 'COMMENDATION', 'HONOR', 'TRIBUTE'
        ]

        # Check extracted data for award patterns
        for key, value in raw_data.items():
            key_upper = key.upper().strip()

            # Skip if this looks like a deduction
            if any(exclude in key_upper for exclude in ['TAX', 'DEDUCTION', 'SSF', 'LOAN']):
                continue

            # Check for award patterns
            if any(pattern in key_upper for pattern in award_patterns):
                amount = self._parse_amount(value)
                if amount > 0:
                    award_items.append((key, amount))
                    if self.debug:
                        print(f"   [FOUND] Found award: {key} = {amount}")

        # If no awards found in extracted data, be more aggressive
        if not award_items:
            if self.debug:
                print(f"   [SEARCH] No awards in extracted data, searching ALL earnings items...")

            # Extract ALL earnings items that are not basic salary components
            excluded_basic = ['BASIC SALARY', 'GROSS SALARY', 'NET PAY', 'TAXABLE SALARY', 'INCOME TAX', 'TOTAL DEDUCTIONS']

            for key, value in raw_data.items():
                key_upper = key.upper().strip()
                amount = self._parse_amount(value)

                # Skip basic salary components and deductions
                is_basic = any(basic in key_upper for basic in excluded_basic)
                is_deduction = any(ded in key_upper for ded in ['TAX', 'DEDUCTION', 'SSF', 'LOAN', 'PENSION'])

                if not is_basic and not is_deduction and amount > 0:
                    award_items.append((key, amount))
                    if self.debug:
                        print(f"   [EXTRACTED] Found earnings item: {key} = {amount}")

            # If still no items, use NET PAY as the actual award amount (NOT GROSS SALARY)
            if not award_items:
                net_pay = self._parse_amount(raw_data.get('NET PAY', '0'))

                # Look for specific award amounts in the document
                award_amount = 0
                award_type = 'LONG SERVICE AWARD'

                # Check for specific award items first
                for key, value in raw_data.items():
                    key_upper = key.upper()
                    if any(award_word in key_upper for award_word in ['AWARD', 'SERVICE', 'BONUS', 'RECOGNITION']):
                        amount = self._parse_amount(value)
                        if amount > 0 and amount != net_pay:  # Don't use net pay as award amount
                            award_amount = amount
                            award_type = key
                            break

                # If no specific award found, use net pay as the award amount
                if award_amount == 0 and net_pay > 0:
                    award_amount = net_pay

                if award_amount > 0:
                    award_items.append((award_type, award_amount))
                    if self.debug:
                        print(f"   [INFERRED] Inferred award: {award_type} = {award_amount}")

        return award_items

    def _find_award_items_dynamic(self, all_elements: List[Dict]) -> List[Tuple[str, float]]:
        """
        Dynamically find all award items in the EARNINGS section
        No hardcoded patterns - detects items based on document structure
        """
        award_items = []

        if self.debug:
            print(f"   [DYNAMIC] Dynamic award detection from {len(all_elements)} elements")

        # Find EARNINGS section boundaries
        earnings_start_y = None
        earnings_end_y = None

        for elem in all_elements:
            text = elem['text'].strip().upper()
            if text == 'EARNINGS':
                earnings_start_y = elem['y']
                if self.debug:
                    print(f"   [SECTION] EARNINGS section starts at Y: {earnings_start_y}")
                break

        # Find where EARNINGS section ends (usually at GROSS SALARY or next major section)
        if earnings_start_y:
            for elem in all_elements:
                text = elem['text'].strip().upper()
                y = elem['y']
                if y > earnings_start_y and (text in ['GROSS SALARY', 'NET PAY', 'TOTAL DEDUCTIONS', 'DEDUCTIONS']):
                    earnings_end_y = y
                    if self.debug:
                        print(f"   [SECTION] EARNINGS section ends at Y: {earnings_end_y} (found: {text})")
                    break

        if not earnings_start_y or not earnings_end_y:
            if self.debug:
                print(f"   [ERROR] Could not determine EARNINGS section boundaries")
            return award_items

        # Get all elements in EARNINGS section
        earnings_elements = []
        for elem in all_elements:
            if earnings_start_y < elem['y'] < earnings_end_y:
                earnings_elements.append(elem)

        if self.debug:
            print(f"   [ELEMENTS] Found {len(earnings_elements)} elements in EARNINGS section")

        # Sort by Y position to process line by line
        earnings_elements.sort(key=lambda x: (x['y'], x['x']))

        # Find award items by looking for label-value pairs
        i = 0
        while i < len(earnings_elements):
            elem = earnings_elements[i]
            text = elem['text'].strip()
            text_upper = text.upper()

            # Skip headers, decorative elements, pure numbers, and non-award items
            if (text_upper in ['EARNINGS', 'AMT (GHS)', 'DEDUCTIONS', 'GROSS SALARY', 'NET PAY', 'INCOME TAX', 'TOTAL DEDUCTIONS'] or
                text.startswith('_') or
                self._is_likely_financial_amount(text)):
                i += 1
                continue

            # Check if this looks like an award item (contains award-related keywords)
            award_keywords = ['AWARD', 'SERVICE', 'LONG', 'PERFORMANCE', 'RECOGNITION', 'BONUS']
            if any(keyword in text_upper for keyword in award_keywords):
                # This could be an award label - look for corresponding value
                award_value = self._find_value_in_earnings_line(elem, earnings_elements)

                if award_value and award_value > 0:
                    award_items.append((text, award_value))
                    if self.debug:
                        print(f"   [DETECTED] Dynamic detection: {text} = {award_value}")

            i += 1

        return award_items

    def _parse_amount(self, amount_str: str) -> float:
        """Parse amount string to float"""
        if not amount_str:
            return 0.0

        try:
            # Remove common currency symbols and formatting
            cleaned = str(amount_str).replace(',', '').replace('GHS', '').replace('₵', '').strip()
            return float(cleaned)
        except (ValueError, TypeError):
            return 0.0

    def _create_error_result(self, document_type: str, error_message: str) -> List[BankAdviserExtractionResult]:
        """Create error result for failed extractions"""
        return [BankAdviserExtractionResult(
            document_type=document_type,
            employee_no="",
            employee_name="",
            department="",
            extraction_confidence=0.0,
            extraction_source=f"Bank Adviser Perfect Extractor - {document_type} (ERROR)",
            extraction_timestamp=datetime.now().isoformat()
        )]

    def _process_allowance_acquisitions(self, results: List[BankAdviserExtractionResult]) -> None:
        """
        Process allowance acquisitions and feed to Bank Adviser tracker tables
        Applies new acquisition logic: current month vs previous month comparison
        """
        try:
            for result in results:
                if not result.employee_no or not result.allowance_type:
                    continue

                # Determine current period from extraction timestamp
                current_date = datetime.fromisoformat(result.extraction_timestamp.replace('Z', '+00:00'))
                current_month = f"{current_date.month:02d}"
                current_year = str(current_date.year)

                # Check if this is a new acquisition
                if self._is_new_allowance_acquisition(result, current_month, current_year):
                    # CORRECTED: Dynamic routing based on extracted allowance type
                    # Route to appropriate tracker table based on the actual extracted type
                    tracker_table = self._determine_allowance_tracker_table(result.allowance_type)

                    if tracker_table == 'educational_subsidy':
                        self._feed_to_educational_subsidy_tracker(result, current_month, current_year)
                    else:
                        # Default to leave_claims for all other allowance types
                        self._feed_to_leave_claims_tracker(result, current_month, current_year)

                    if self.debug:
                        print(f"   [TRACKER] NEW ALLOWANCE: {result.allowance_type} for {result.employee_no}")
                else:
                    if self.debug:
                        print(f"   [TRACKER] EXISTING ALLOWANCE: {result.allowance_type} for {result.employee_no} (skipped)")

        except Exception as e:
            print(f"[BANK-ADVISER] Error processing allowance acquisitions: {e}")

    def _determine_allowance_tracker_table(self, allowance_type: str) -> str:
        """
        CORRECTED: Determine tracker table based on extracted allowance type
        NO HARDCODED KEYWORDS - Uses exact extracted type for routing

        Based on user requirements:
        - LEAVE CLAIMS → leave_claims table
        - EDUCATIONAL SUBSIDY → educational_subsidy table
        - MOTOR VEHICLE MAINTENANCE → motor_vehicle_maintenance table
        - All other allowances → leave_claims table (default)
        """
        if not allowance_type:
            return 'leave_claims'  # Default

        allowance_type_upper = allowance_type.upper().strip()

        # Route to educational_subsidy if it's exactly "EDUCATIONAL SUBSIDY"
        if allowance_type_upper == 'EDUCATIONAL SUBSIDY':
            return 'educational_subsidy'

        # Route to motor_vehicle_maintenance if it contains motor vehicle keywords
        if any(keyword in allowance_type_upper for keyword in ['MOTOR VEHICLE', 'VEHICLE MAINT', 'CAR MAINT', 'VEHICLE MAINTENANCE']):
            return 'motor_vehicle_maintenance'

        # All other allowance types go to leave_claims (including "LEAVE CLAIMS")
        return 'leave_claims'

    def _process_award_acquisitions(self, results: List[BankAdviserExtractionResult]) -> None:
        """
        Process award acquisitions and feed to Bank Adviser tracker tables
        Applies new acquisition logic: current month vs previous month comparison
        """
        try:
            for result in results:
                if not result.employee_no or not result.award_type:
                    continue

                # Determine current period from extraction timestamp
                current_date = datetime.fromisoformat(result.extraction_timestamp.replace('Z', '+00:00'))
                current_month = f"{current_date.month:02d}"
                current_year = str(current_date.year)

                # Check if this is a new acquisition
                if self._is_new_award_acquisition(result, current_month, current_year):
                    # Feed to long service awards tracker
                    self._feed_to_long_service_awards_tracker(result, current_month, current_year)

                    if self.debug:
                        print(f"   [TRACKER] NEW AWARD: {result.award_type} for {result.employee_no}")
                else:
                    if self.debug:
                        print(f"   [TRACKER] EXISTING AWARD: {result.award_type} for {result.employee_no} (skipped)")

        except Exception as e:
            print(f"[BANK-ADVISER] Error processing award acquisitions: {e}")

    def set_tax_rates(self, allowance_rate: float = None, awards_rate: float = None,
                     educational_subsidy_rate: float = None):
        """Set custom tax rates"""
        if allowance_rate is not None:
            self.tax_rates['allowances'] = allowance_rate
            print(f"[TAX-RATE] Allowance tax rate set to: {allowance_rate}%")

        if awards_rate is not None:
            self.tax_rates['awards'] = awards_rate
            print(f"[TAX-RATE] Awards tax rate set to: {awards_rate}%")

        if educational_subsidy_rate is not None:
            self.tax_rates['educational_subsidy'] = educational_subsidy_rate
            print(f"[TAX-RATE] Educational subsidy tax rate set to: {educational_subsidy_rate}%")

    def _is_new_allowance_acquisition(self, result: BankAdviserExtractionResult, current_month: str, current_year: str) -> bool:
        """Check if allowance is a new acquisition (current month vs previous month)"""
        try:
            from core.unified_database import UnifiedDatabase
            db = UnifiedDatabase()

            # Calculate previous month
            month_int = int(current_month)
            year_int = int(current_year)

            if month_int == 1:
                prev_month = "12"
                prev_year = str(year_int - 1)
            else:
                prev_month = f"{month_int - 1:02d}"
                prev_year = current_year

            # Check if this allowance type existed for this employee in previous month
            # Check both leave_claims and educational_subsidy tables
            tables_to_check = ['leave_claims', 'educational_subsidy']

            for table in tables_to_check:
                previous_records = db.getAllQuery(
                    f"""SELECT COUNT(*) FROM {table}
                        WHERE employee_no = ? AND allowance_type = ?
                        AND period_year = ? AND period_month = ?""",
                    [result.employee_no, result.allowance_type, prev_year, prev_month]
                )

                if previous_records and previous_records[0][0] > 0:
                    return False  # Found in previous month, not new

            return True  # Not found in previous month, this is new

        except Exception as e:
            if self.debug:
                print(f"   [TRACKER] Error checking allowance acquisition: {e}")
            return True  # Default to new if check fails

    def _is_new_award_acquisition(self, result: BankAdviserExtractionResult, current_month: str, current_year: str) -> bool:
        """Check if award is a new acquisition (current month vs previous month)"""
        try:
            from core.unified_database import UnifiedDatabase
            db = UnifiedDatabase()

            # Calculate previous month
            month_int = int(current_month)
            year_int = int(current_year)

            if month_int == 1:
                prev_month = "12"
                prev_year = str(year_int - 1)
            else:
                prev_month = f"{month_int - 1:02d}"
                prev_year = current_year

            # Check if this award type existed for this employee in previous month
            previous_records = db.getAllQuery(
                """SELECT COUNT(*) FROM long_service_awards
                   WHERE employee_no = ? AND award_type = ?
                   AND period_year = ? AND period_month = ?""",
                [result.employee_no, result.award_type, prev_year, prev_month]
            )

            if previous_records and previous_records[0][0] > 0:
                return False  # Found in previous month, not new

            return True  # Not found in previous month, this is new

        except Exception as e:
            if self.debug:
                print(f"   [TRACKER] Error checking award acquisition: {e}")
            return True  # Default to new if check fails

    def _feed_to_leave_claims_tracker(self, result: BankAdviserExtractionResult, current_month: str, current_year: str) -> None:
        """Feed new leave claims to tracker table with duplicate checking"""
        try:
            from core.python_database_manager import get_database_instance

            db = get_database_instance()

            # Insert the record first
            db.execute_update(
                """INSERT INTO leave_claims
                   (employee_no, employee_name, department, allowance_type, payable_amount,
                    period_month, period_year, period_acquired, source_session)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    result.employee_no,
                    result.employee_name,
                    result.department,
                    result.allowance_type,
                    result.allowance_payable_amount,
                    current_month,
                    current_year,
                    f"{current_year}-{current_month}",
                    f"bank_adviser_{current_year}_{current_month}"
                )
            )

            # Get the ID of the inserted record
            cursor = db.connection.execute("SELECT last_insert_rowid()")
            new_record_id = cursor.fetchone()[0]

            # Check for duplicates
            is_duplicate = self.duplicate_checker.check_for_duplicate(
                employee_no=result.employee_no,
                claim_type=result.allowance_type,
                year=int(current_year),
                source_table='leave_claims',
                new_record_id=new_record_id
            )

            if self.debug:
                status = "DUPLICATE" if is_duplicate else "NEW"
                print(f"   [TRACKER] SAVED to leave_claims: {result.allowance_type} - GHS {result.allowance_payable_amount} ({status})")

        except Exception as e:
            print(f"[BANK-ADVISER] Error feeding to leave_claims tracker: {e}")

    def _feed_to_educational_subsidy_tracker(self, result: BankAdviserExtractionResult, current_month: str, current_year: str) -> None:
        """Feed new educational subsidy to tracker table with duplicate checking"""
        try:
            from core.python_database_manager import get_database_instance

            db = get_database_instance()

            # Insert the record first
            db.execute_update(
                """INSERT INTO educational_subsidy
                   (employee_no, employee_name, department, allowance_type, payable_amount,
                    period_month, period_year, period_acquired, source_session)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    result.employee_no,
                    result.employee_name,
                    result.department,
                    result.allowance_type,
                    result.allowance_payable_amount,
                    current_month,
                    current_year,
                    f"{current_year}-{current_month}",
                    f"bank_adviser_{current_year}_{current_month}"
                )
            )

            # Get the ID of the inserted record
            cursor = db.connection.execute("SELECT last_insert_rowid()")
            new_record_id = cursor.fetchone()[0]

            # Check for duplicates
            is_duplicate = self.duplicate_checker.check_for_duplicate(
                employee_no=result.employee_no,
                claim_type=result.allowance_type,
                year=int(current_year),
                source_table='educational_subsidy',
                new_record_id=new_record_id
            )

            if self.debug:
                status = "DUPLICATE" if is_duplicate else "NEW"
                print(f"   [TRACKER] SAVED to educational_subsidy: {result.allowance_type} - GHS {result.allowance_payable_amount} ({status})")

        except Exception as e:
            print(f"[BANK-ADVISER] Error feeding to educational_subsidy tracker: {e}")

    def _feed_to_long_service_awards_tracker(self, result: BankAdviserExtractionResult, current_month: str, current_year: str) -> None:
        """Feed new long service awards to tracker table with duplicate checking"""
        try:
            from core.python_database_manager import get_database_instance

            db = get_database_instance()

            # Insert the record first
            db.execute_update(
                """INSERT INTO long_service_awards
                   (employee_no, employee_name, department, award_type, payable_amount,
                    period_month, period_year, period_acquired, source_session)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    result.employee_no,
                    result.employee_name,
                    result.department,
                    result.award_type,
                    result.award_payable_amount,
                    current_month,
                    current_year,
                    f"{current_year}-{current_month}",
                    f"bank_adviser_{current_year}_{current_month}"
                )
            )

            # Get the ID of the inserted record
            cursor = db.connection.execute("SELECT last_insert_rowid()")
            new_record_id = cursor.fetchone()[0]

            # Check for duplicates
            is_duplicate = self.duplicate_checker.check_for_duplicate(
                employee_no=result.employee_no,
                claim_type=result.award_type,
                year=int(current_year),
                source_table='long_service_awards',
                new_record_id=new_record_id
            )

            if self.debug:
                status = "DUPLICATE" if is_duplicate else "NEW"
                print(f"   [TRACKER] SAVED to long_service_awards: {result.award_type} - GHS {result.award_payable_amount} ({status})")

        except Exception as e:
            print(f"[BANK-ADVISER] Error feeding to long_service_awards tracker: {e}")

    def _process_allowance_acquisitions_immediate(self, results: List[BankAdviserExtractionResult]):
        """
        OPTIMIZATION: Immediate tracker processing for allowances
        Process tracker items immediately during extraction for better efficiency
        """
        if self.debug:
            print(f"   [TRACKER] Immediate allowance tracker processing for {len(results)} items")

        current_month = datetime.now().strftime('%m')
        current_year = datetime.now().year

        for result in results:
            try:
                # Check if this is a new acquisition (only process new items)
                if self._is_new_allowance_acquisition(result, current_month, str(current_year)):
                    # CORRECTED: Dynamic routing based on extracted allowance type
                    # Route to appropriate tracker table based on the actual extracted type
                    tracker_table = self._determine_allowance_tracker_table(result.allowance_type)

                    if tracker_table == 'educational_subsidy':
                        self._feed_to_educational_subsidy_tracker(result, current_month, str(current_year))
                    else:
                        # Default to leave_claims for all other allowance types
                        self._feed_to_leave_claims_tracker(result, current_month, str(current_year))

                    if self.debug:
                        print(f"   [TRACKER] Processed new allowance: {result.allowance_type}")
                else:
                    if self.debug:
                        print(f"   [TRACKER] Skipped existing allowance: {result.allowance_type}")

            except Exception as e:
                print(f"Error in immediate allowance tracker processing: {e}")

    def _process_award_acquisitions_immediate(self, results: List[BankAdviserExtractionResult]):
        """
        OPTIMIZATION: Immediate tracker processing for awards
        Process tracker items immediately during extraction for better efficiency
        """
        if self.debug:
            print(f"   [TRACKER] Immediate award tracker processing for {len(results)} items")

        current_month = datetime.now().strftime('%m')
        current_year = datetime.now().year

        for result in results:
            try:
                # Check if this is a new acquisition (only process new items)
                if self._is_new_award_acquisition(result, current_month, str(current_year)):
                    # All awards go to long_service_awards table
                    self._feed_to_long_service_awards_tracker(result, current_month, str(current_year))

                    if self.debug:
                        print(f"   [TRACKER] Processed new award: {result.award_type}")
                else:
                    if self.debug:
                        print(f"   [TRACKER] Skipped existing award: {result.award_type}")

            except Exception as e:
                print(f"Error in immediate award tracker processing: {e}")

def main():
    """Command line interface for Bank Adviser Perfect Extractor"""
    import sys
    import json

    if len(sys.argv) < 3:
        print("Usage: python bank_adviser_perfect_extractor.py <document_type> <pdf_path> [tax_rate]")
        print("Document types: final_adjusted, allowances, awards")
        sys.exit(1)

    document_type = sys.argv[1]
    pdf_path = sys.argv[2]
    tax_rate = float(sys.argv[3]) if len(sys.argv) > 3 else None

    extractor = BankAdviserPerfectExtractor(debug=False)

    try:
        if document_type == 'final_adjusted':
            results = extractor.extract_final_adjusted_payslip(pdf_path)
        elif document_type == 'allowances':
            results = extractor.extract_allowances_payslip(pdf_path, tax_rate=tax_rate)
        elif document_type == 'awards':
            results = extractor.extract_awards_payslip(pdf_path, tax_rate=tax_rate)
        else:
            raise ValueError(f"Unknown document type: {document_type}")

        # Convert results to JSON-serializable format
        json_results = []
        for result in results:
            json_results.append({
                'document_type': result.document_type,
                'employee_no': result.employee_no,
                'employee_name': result.employee_name,
                'department': result.department,
                'section': result.section,
                'job_title': result.job_title,
                'net_pay': result.net_pay,
                'gross_salary': result.gross_salary,
                'taxable_salary': result.taxable_salary,
                'total_deductions': result.total_deductions,
                'bank': result.bank,
                'account_no': result.account_no,
                'branch': result.branch,
                'allowance_type': result.allowance_type,
                'allowance_gross_amount': result.allowance_gross_amount,
                'allowance_tax_amount': result.allowance_tax_amount,
                'allowance_payable_amount': result.allowance_payable_amount,
                'award_type': result.award_type,
                'award_gross_amount': result.award_gross_amount,
                'award_tax_amount': result.award_tax_amount,
                'award_payable_amount': result.award_payable_amount,
                'extraction_confidence': result.extraction_confidence,
                'extraction_source': result.extraction_source,
                'extraction_timestamp': result.extraction_timestamp,
                'section_detected': result.section_detected
            })

        print(json.dumps(json_results, indent=2))

    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'document_type': document_type,
            'pdf_path': pdf_path
        }
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    main()
