#!/usr/bin/env python3
"""Check pre-reporting table schema"""

import sqlite3

def check_schema():
    conn = sqlite3.connect('payroll_audit.db')
    cursor = conn.cursor()
    
    try:
        # Check pre_reporting_results schema
        cursor.execute('PRAGMA table_info(pre_reporting_results)')
        columns = cursor.fetchall()
        
        print("📊 PRE_REPORTING_RESULTS SCHEMA:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # Check if we need to add the column
        column_names = [col[1] for col in columns]
        
        if 'selected_for_report' not in column_names:
            print("\n🔧 ADDING selected_for_report COLUMN:")
            cursor.execute('ALTER TABLE pre_reporting_results ADD COLUMN selected_for_report INTEGER DEFAULT 1')
            
            # Copy is_selected values to selected_for_report
            cursor.execute('UPDATE pre_reporting_results SET selected_for_report = is_selected')
            conn.commit()
            print("   ✅ Column added and data copied")
        else:
            print("\n✅ selected_for_report column already exists")
        
        # Show sample data
        cursor.execute('SELECT session_id, is_selected, selected_for_report FROM pre_reporting_results LIMIT 5')
        rows = cursor.fetchall()
        
        print("\n📋 SAMPLE DATA:")
        for row in rows:
            print(f"   Session: {row[0]}, is_selected: {row[1]}, selected_for_report: {row[2]}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_schema()
