<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interactive Pre-Reporting UI Features</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #ui-container {
            min-height: 400px;
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Interactive Pre-Reporting UI Features Test</h1>
    
    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>UI Rendering Test</h2>
        <button onclick="testUIRendering()">🎨 Test UI Rendering</button>
        <div id="ui-container">
            <p>Click the button above to test UI rendering...</p>
        </div>
    </div>

    <!-- Load the interactive pre-reporting script -->
    <script src="ui/interactive_pre_reporting.js"></script>
    
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function testUIRendering() {
            addTestResult('🧪 Starting UI rendering test...', 'info');
            
            try {
                // Check if InteractivePreReporting class exists
                if (typeof InteractivePreReporting === 'undefined') {
                    addTestResult('❌ InteractivePreReporting class not found', 'error');
                    return;
                }
                addTestResult('✅ InteractivePreReporting class found', 'success');

                // Create mock data
                const mockData = [
                    {
                        id: 'test1',
                        employee_id: 'EMP001',
                        employee_name: 'John Doe',
                        section_name: 'EARNINGS',
                        item_label: 'Basic Salary',
                        previous_value: '5000',
                        current_value: '5500',
                        change_type: 'INCREASED',
                        priority: 'HIGH',
                        bulk_category: 'INDIVIDUAL',
                        bulk_size: 1
                    },
                    {
                        id: 'test2',
                        employee_id: 'EMP002',
                        employee_name: 'Jane Smith',
                        section_name: 'DEDUCTIONS',
                        item_label: 'Tax',
                        previous_value: '500',
                        current_value: '550',
                        change_type: 'INCREASED',
                        priority: 'MODERATE',
                        bulk_category: 'SMALL_BULK',
                        bulk_size: 5
                    }
                ];

                // Get container
                const container = document.getElementById('ui-container');
                container.innerHTML = '';

                // Initialize the UI
                const preReportingUI = new InteractivePreReporting(container, mockData);
                preReportingUI.initialize();

                addTestResult('✅ InteractivePreReporting initialized successfully', 'success');

                // Check for sort controls after a short delay
                setTimeout(() => {
                    const sortDropdown = document.getElementById('persistent-sort-dropdown');
                    if (sortDropdown) {
                        addTestResult('✅ Sort dropdown found in UI!', 'success');
                        addTestResult(`📊 Sort options: ${Array.from(sortDropdown.options).map(o => o.text).join(', ')}`, 'info');
                    } else {
                        addTestResult('❌ Sort dropdown NOT found in UI', 'error');
                    }

                    // Check for other UI elements
                    const bulkCategories = container.querySelector('.bulk-categories');
                    if (bulkCategories) {
                        addTestResult('✅ Bulk categories section found', 'success');
                    } else {
                        addTestResult('❌ Bulk categories section NOT found', 'error');
                    }

                    const persistentControls = container.querySelector('.persistent-sort-controls');
                    if (persistentControls) {
                        addTestResult('✅ Persistent sort controls found!', 'success');
                    } else {
                        addTestResult('❌ Persistent sort controls NOT found', 'error');
                    }

                    // Test sorting functionality
                    if (sortDropdown) {
                        addTestResult('🧪 Testing sort functionality...', 'info');
                        sortDropdown.value = 'employees';
                        sortDropdown.dispatchEvent(new Event('change'));
                        
                        setTimeout(() => {
                            const employeeGroups = container.querySelector('.employee-groups');
                            if (employeeGroups) {
                                addTestResult('✅ Employee grouping works!', 'success');
                            } else {
                                addTestResult('❌ Employee grouping failed', 'error');
                            }
                        }, 500);
                    }
                }, 1000);

            } catch (error) {
                addTestResult(`❌ Error during UI test: ${error.message}`, 'error');
                console.error('UI Test Error:', error);
            }
        }

        // Run basic checks on page load
        window.addEventListener('load', () => {
            addTestResult('🚀 Page loaded, running basic checks...', 'info');
            
            if (typeof InteractivePreReporting !== 'undefined') {
                addTestResult('✅ InteractivePreReporting class is available', 'success');
            } else {
                addTestResult('❌ InteractivePreReporting class is NOT available', 'error');
            }
        });
    </script>
</body>
</html>
