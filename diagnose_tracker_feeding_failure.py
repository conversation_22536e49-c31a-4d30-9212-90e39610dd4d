#!/usr/bin/env python3
"""Comprehensive diagnostic for TRACKER_FEEDING phase failure"""

import sqlite3
import sys
import os
import traceback

def diagnose_tracker_feeding_failure():
    """Diagnose why TRACKER_FEEDING phase is failing"""
    print("🔍 COMPREHENSIVE TRACKER_FEEDING FAILURE DIAGNOSIS")
    print("=" * 60)
    
    try:
        db_path = 'data/templar_payroll_auditor.db'
        if not os.path.exists(db_path):
            print("❌ Database not found")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("1. 📋 CURRENT SESSION ANALYSIS:")
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session_result = cursor.fetchone()
        
        if not session_result:
            print("   ❌ No current session found")
            return False
            
        session_id = session_result[0]
        print(f"   Current session: {session_id}")
        
        # 2. Check prerequisite data
        print("\n2. 📊 PREREQUISITE DATA CHECK:")
        
        # Check comparison results for NEW items
        cursor.execute('''
            SELECT COUNT(*) FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW'
        ''', (session_id,))
        new_items_count = cursor.fetchone()[0]
        print(f"   NEW items in comparison_results: {new_items_count}")
        
        if new_items_count == 0:
            print("   ⚠️ No NEW items found - this may be why TRACKER_FEEDING is not running")
        
        # Check dictionary_items table
        cursor.execute('SELECT COUNT(*) FROM dictionary_items')
        dict_items_count = cursor.fetchone()[0]
        print(f"   Dictionary items: {dict_items_count}")
        
        # 3. Check tracker tables schema
        print("\n3. 🏗️ TRACKER TABLES SCHEMA CHECK:")
        
        tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in tracker_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} records")
            except sqlite3.OperationalError as e:
                print(f"   ❌ {table}: Table missing or error - {e}")
        
        # 4. Test the actual TRACKER_FEEDING logic
        print("\n4. 🧪 TESTING TRACKER_FEEDING LOGIC:")
        
        try:
            # Import the phased process manager
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=True)
            manager.session_id = session_id
            
            print("   📋 Testing _load_new_items_for_tracking...")
            new_items = manager._load_new_items_for_tracking()
            print(f"   Found {len(new_items)} NEW items for tracking")
            
            if len(new_items) > 0:
                print("   Sample NEW items:")
                for i, item in enumerate(new_items[:3]):
                    print(f"     {i+1}. {item.get('employee_id', 'N/A')} - {item.get('item_label', 'N/A')} = {item.get('current_value', 'N/A')}")
                
                print("\n   📋 Testing loan type classification...")
                in_house_loan_types = manager._load_in_house_loan_types()
                print(f"   In-house loan types: {in_house_loan_types}")
                
                print("\n   📋 Testing tracker feeding execution...")
                options = {
                    'currentMonth': 7,
                    'currentYear': 2025,
                    'previousMonth': 6,
                    'previousYear': 2025
                }
                
                # Try to run the tracker feeding phase
                result = manager._phase_tracker_feeding(options)
                print(f"   TRACKER_FEEDING result: {result}")
                
                if not result:
                    print("   ❌ TRACKER_FEEDING phase returned False")
                else:
                    print("   ✅ TRACKER_FEEDING phase completed successfully")
                    
            else:
                print("   ⚠️ No NEW items found for tracking")
                print("   This explains why TRACKER_FEEDING might be skipped or failing")
                
        except Exception as e:
            print(f"   ❌ Error testing TRACKER_FEEDING logic: {e}")
            traceback.print_exc()
        
        # 5. Check for specific error conditions
        print("\n5. 🔍 SPECIFIC ERROR CONDITIONS:")
        
        # Check if dictionary_items has proper schema
        try:
            cursor.execute("PRAGMA table_info(dictionary_items)")
            columns = [row[1] for row in cursor.fetchall()]
            required_columns = ['include_in_report', 'include_new']
            
            for col in required_columns:
                if col in columns:
                    print(f"   ✅ dictionary_items.{col} column exists")
                else:
                    print(f"   ❌ dictionary_items.{col} column missing")
                    
        except Exception as e:
            print(f"   ❌ Error checking dictionary_items schema: {e}")
        
        # Check comparison_results schema
        try:
            cursor.execute("PRAGMA table_info(comparison_results)")
            columns = [row[1] for row in cursor.fetchall()]
            required_columns = ['change_type', 'current_value', 'item_label']
            
            for col in required_columns:
                if col in columns:
                    print(f"   ✅ comparison_results.{col} column exists")
                else:
                    print(f"   ❌ comparison_results.{col} column missing")
                    
        except Exception as e:
            print(f"   ❌ Error checking comparison_results schema: {e}")
        
        conn.close()
        
        # 6. Recommendations
        print("\n6. 💡 RECOMMENDATIONS:")
        print("=" * 30)
        
        if new_items_count == 0:
            print("   🎯 PRIMARY ISSUE: No NEW items found in comparison results")
            print("   📋 SOLUTION: Check why comparison phase is not detecting NEW items")
            print("   💡 This could be due to:")
            print("      - All employees exist in both current and previous periods")
            print("      - Comparison logic not properly detecting new items")
            print("      - Dictionary filtering excluding all new items")
        else:
            print("   🎯 NEW items exist, but TRACKER_FEEDING is still failing")
            print("   📋 SOLUTION: Check tracker table schema and permissions")
            print("   💡 Run the tracker feeding phase manually to see detailed errors")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    diagnose_tracker_feeding_failure()
