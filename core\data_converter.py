#!/usr/bin/env python3
"""
THE PAYROLL AUDITOR - DATA CONVERTER
Converts visual extraction results to standardized payroll auditor format.
Ensures consistent data structure across the entire system.
"""

from typing import Dict, List, Optional, Any

class PayrollDataConverter:
    """
    Converts visual extraction results to standardized employee data format.
    Provides consistent data structure for comparison and reporting.
    """

    def __init__(self):
        self.debug = True

    def convert_visual_to_employee_data(self, visual_data: Dict) -> Dict:
        """
        Convert visual extraction results to standardized employee data format.

        Args:
            visual_data: Raw visual extraction results

        Returns:
            Standardized employee data dictionary
        """
        if self.debug:
            print(f"🔄 Converting visual data to employee format")

        # Initialize standardized employee data structure
        employee = {
            "employee_id": None,
            "ssf_no": None,
            "ghana_card_id": None,
            "name": None,
            "job_title": None,
            "department": None,
            "section": None,
            "basic_salary": None,
            "gross_salary": None,
            "net_pay": None,
            "taxable_income": None,
            "total_deductions": None,
            "deductions": {},
            "earnings": {},
            "month": None,
            "year": None,
            "loan": None,
            "bank_details": {},
            "employer_contributions": {},
            "loan_details": {},
            "personal_details": {}
        }

        # Extract personal details
        personal_details = visual_data.get('PERSONAL DETAILS', {})
        if personal_details:
            employee["employee_id"] = personal_details.get('EMPLOYEE NO.')
            employee["name"] = personal_details.get('EMPLOYEE NAME')
            employee["ssf_no"] = personal_details.get('SSF NO.')
            employee["ghana_card_id"] = personal_details.get('GHANA CARD ID')
            employee["job_title"] = personal_details.get('JOB TITLE')
            employee["department"] = personal_details.get('DEPARTMENT')
            employee["section"] = personal_details.get('SECTION')

            # Store complete personal details
            employee["personal_details"] = personal_details

        # Extract earnings
        earnings = visual_data.get('EARNINGS', {})
        if earnings:
            # Handle special earnings fields
            if 'BASIC SALARY' in earnings:
                employee["basic_salary"] = self._clean_amount(earnings['BASIC SALARY'])
                del earnings['BASIC SALARY']  # Remove from general earnings

            if 'GROSS SALARY' in earnings:
                employee["gross_salary"] = self._clean_amount(earnings['GROSS SALARY'])
                del earnings['GROSS SALARY']  # Remove from general earnings

            if 'NET PAY' in earnings:
                employee["net_pay"] = self._clean_amount(earnings['NET PAY'])
                del earnings['NET PAY']  # Remove from general earnings

            # Store remaining earnings
            for item, value in earnings.items():
                employee["earnings"][item] = self._clean_amount(value)

        # Extract deductions
        deductions = visual_data.get('DEDUCTIONS', {})
        if deductions:
            # Handle special deduction fields
            if 'TOTAL DEDUCTIONS' in deductions:
                employee["total_deductions"] = self._clean_amount(deductions['TOTAL DEDUCTIONS'])
                del deductions['TOTAL DEDUCTIONS']  # Remove from general deductions

            if 'TAXABLE INCOME' in deductions:
                employee["taxable_income"] = self._clean_amount(deductions['TAXABLE INCOME'])
                del deductions['TAXABLE INCOME']  # Remove from general deductions

            # Store remaining deductions
            for item, value in deductions.items():
                employee["deductions"][item] = self._clean_amount(value)

        # Extract loans
        loans = visual_data.get('LOANS', {})
        if loans:
            # Convert to loan_details format
            for loan_name, current_deduction in loans.items():
                employee["loan_details"][loan_name] = {
                    "current_deduction": self._clean_amount(current_deduction),
                    "outstanding_balance": None  # Not available in current extraction
                }

            # Set legacy loan field (first loan's current deduction)
            if loans:
                first_loan_value = next(iter(loans.values()))
                employee["loan"] = self._clean_amount(first_loan_value)

        # Extract employer contributions
        employer_contrib = visual_data.get('EMPLOYERS CONTRIBUTION', {})
        if employer_contrib:
            for item, value in employer_contrib.items():
                employee["employer_contributions"][item] = self._clean_amount(value)

        # Extract bank details
        bank_details = visual_data.get('EMPLOYEE BANK DETAILS', {})
        if bank_details:
            employee["bank_details"] = bank_details

        if self.debug:
            print(f"   ✅ Converted employee: {employee.get('employee_id')} - {employee.get('name')}")

        return employee

    def convert_multiple_employees(self, visual_results: List[Dict]) -> List[Dict]:
        """
        Convert multiple visual extraction results to employee data format.

        Args:
            visual_results: List of visual extraction results

        Returns:
            List of standardized employee data dictionaries
        """
        employees = []

        for i, visual_data in enumerate(visual_results):
            if self.debug:
                print(f"🔄 Converting employee {i+1}/{len(visual_results)}")

            employee = self.convert_visual_to_employee_data(visual_data)

            if employee and employee.get("employee_id"):
                employees.append(employee)
            else:
                print(f"⚠️ Skipping invalid employee data at index {i}")

        if self.debug:
            print(f"✅ Successfully converted {len(employees)} employees")

        return employees

    def _clean_amount(self, amount_str: str) -> str:
        """Clean and format amount strings"""
        if not amount_str:
            return "0.00"

        # Remove any non-numeric characters except decimal point and comma
        cleaned = str(amount_str).replace(',', '').strip()

        try:
            # Convert to float and back to ensure proper formatting
            amount = float(cleaned)
            return f"{amount:.2f}"
        except (ValueError, TypeError):
            return str(amount_str)

    def validate_employee_data(self, employee: Dict) -> bool:
        """
        Validate that employee data contains required fields.

        Args:
            employee: Employee data dictionary

        Returns:
            True if valid, False otherwise
        """
        required_fields = ['employee_id', 'name']

        for field in required_fields:
            if not employee.get(field):
                if self.debug:
                    print(f"❌ Validation failed: Missing {field}")
                return False

        return True

    def get_employee_summary(self, employee: Dict) -> Dict:
        """
        Get a summary of employee data for quick overview.

        Args:
            employee: Employee data dictionary

        Returns:
            Summary dictionary
        """
        return {
            "employee_id": employee.get("employee_id"),
            "name": employee.get("name"),
            "department": employee.get("department"),
            "basic_salary": employee.get("basic_salary"),
            "gross_salary": employee.get("gross_salary"),
            "net_pay": employee.get("net_pay"),
            "total_earnings": len(employee.get("earnings", {})),
            "total_deductions": len(employee.get("deductions", {})),
            "total_loans": len(employee.get("loan_details", {})),
            "has_bank_details": bool(employee.get("bank_details")),
            "has_employer_contributions": bool(employee.get("employer_contributions"))
        }

# Utility functions for external use
def convert_visual_extraction_to_employees(visual_results: List[Dict]) -> List[Dict]:
    """
    Utility function to convert visual extraction results to employee data.

    Args:
        visual_results: List of visual extraction results

    Returns:
        List of standardized employee data dictionaries
    """
    converter = PayrollDataConverter()
    return converter.convert_multiple_employees(visual_results)

def validate_employees_data(employees: List[Dict]) -> List[Dict]:
    """
    Utility function to validate and filter employee data.

    Args:
        employees: List of employee data dictionaries

    Returns:
        List of valid employee data dictionaries
    """
    converter = PayrollDataConverter()
    valid_employees = []

    for employee in employees:
        if converter.validate_employee_data(employee):
            valid_employees.append(employee)

    return valid_employees

def get_employees_summary(employees: List[Dict]) -> List[Dict]:
    """
    Utility function to get summary of multiple employees.

    Args:
        employees: List of employee data dictionaries

    Returns:
        List of employee summary dictionaries
    """
    converter = PayrollDataConverter()
    return [converter.get_employee_summary(emp) for emp in employees]

# Test function removed - system ready for production usetion data
    sample_visual_data = {
        'PERSONAL DETAILS': {
            'EMPLOYEE NO.': 'PTS1234',
            'EMPLOYEE NAME': 'JOHN DOE',
            'DEPARTMENT': 'FINANCE',
            'JOB TITLE': 'ACCOUNTANT'
        },
        'EARNINGS': {
            'BASIC SALARY': '5000.00',
            'GROSS SALARY': '6000.00',
            'NET PAY': '4500.00',
            'ALLOWANCE': '1000.00'
        },
        'DEDUCTIONS': {
            'TAX': '500.00',
            'SSF': '300.00',
            'TOTAL DEDUCTIONS': '800.00'
        },
        'LOANS': {
            'STAFF LOAN': '200.00'
        }
    }

    converter = PayrollDataConverter()
    employee = converter.convert_visual_to_employee_data(sample_visual_data)

    print("📊 Conversion Results:")
    print(f"   Employee ID: {employee['employee_id']}")
    print(f"   Name: {employee['name']}")
    print(f"   Department: {employee['department']}")
    print(f"   Basic Salary: {employee['basic_salary']}")
    print(f"   Gross Salary: {employee['gross_salary']}")
    print(f"   Net Pay: {employee['net_pay']}")
    print(f"   Earnings: {len(employee['earnings'])} items")
    print(f"   Deductions: {len(employee['deductions'])} items")
    print(f"   Loans: {len(employee['loan_details'])} items")

    # Test validation
    is_valid = converter.validate_employee_data(employee)
    print(f"   Validation: {'✅ PASSED' if is_valid else '❌ FAILED'}")

    return employee

if __name__ == "__main__":
    test_data_conversion()
