/**
 * Comprehensive Test Suite for Error Fixes
 * Tests the fixes for Process Exit Code null and undefined beautifulContainer errors
 */

// Test 1: Process Exit Code Handling
async function testProcessExitCodeHandling() {
    console.log('🧪 Testing Process Exit Code Handling...');
    
    try {
        // Test the API call that was failing
        const response = await window.api.getLatestPreReportingData();
        
        if (response && response.success) {
            console.log('✅ Process Exit Code Test: API call successful');
            console.log('📊 Data received:', response.data ? response.data.length : 0, 'items');
            return true;
        } else {
            console.log('⚠️ Process Exit Code Test: API call returned error:', response?.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Process Exit Code Test: Exception caught:', error.message);
        return false;
    }
}

// Test 2: Beautiful Container Reference
function testBeautifulContainerReference() {
    console.log('🧪 Testing Beautiful Container Reference...');
    
    try {
        // Simulate the initializeInteractivePreReporting function
        const beautifulContainer = document.getElementById('beautiful-pre-reporting-container');
        
        if (beautifulContainer) {
            console.log('✅ Beautiful Container Test: Container found');
            return true;
        } else {
            console.log('⚠️ Beautiful Container Test: Container not found, but no error thrown');
            return true; // This is expected behavior now
        }
    } catch (error) {
        console.error('❌ Beautiful Container Test: Exception caught:', error.message);
        return false;
    }
}

// Test 3: Interactive Pre-Reporting Initialization
async function testInteractivePreReportingInit() {
    console.log('🧪 Testing Interactive Pre-Reporting Initialization...');
    
    try {
        // Check if the class is available
        if (typeof InteractivePreReporting === 'undefined') {
            console.log('⚠️ InteractivePreReporting class not loaded');
            return false;
        }
        
        // Find a container
        let container = document.getElementById('test-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'test-container';
            document.body.appendChild(container);
        }
        
        // Test initialization
        const preReportingUI = new InteractivePreReporting(container);
        
        if (preReportingUI && typeof preReportingUI.initialize === 'function') {
            console.log('✅ Interactive Pre-Reporting Test: Class instantiated successfully');
            return true;
        } else {
            console.log('❌ Interactive Pre-Reporting Test: Class missing initialize method');
            return false;
        }
    } catch (error) {
        console.error('❌ Interactive Pre-Reporting Test: Exception caught:', error.message);
        return false;
    }
}

// Test 4: Retry Logic
async function testRetryLogic() {
    console.log('🧪 Testing Retry Logic...');
    
    let retryCount = 0;
    const maxRetries = 3;
    
    function mockFailingFunction() {
        retryCount++;
        if (retryCount < 3) {
            throw new Error('Simulated failure');
        }
        return { success: true, message: 'Success after retries' };
    }
    
    try {
        let result;
        let attempts = 0;
        
        while (attempts < maxRetries) {
            try {
                result = mockFailingFunction();
                break;
            } catch (error) {
                attempts++;
                if (attempts >= maxRetries) {
                    throw error;
                }
                console.log(`🔄 Retry attempt ${attempts}/${maxRetries}`);
                await new Promise(resolve => setTimeout(resolve, 100)); // Short delay for test
            }
        }
        
        if (result && result.success) {
            console.log('✅ Retry Logic Test: Function succeeded after retries');
            return true;
        } else {
            console.log('❌ Retry Logic Test: Function failed after all retries');
            return false;
        }
    } catch (error) {
        console.error('❌ Retry Logic Test: Exception caught:', error.message);
        return false;
    }
}

// Test 5: Error Message Handling
function testErrorMessageHandling() {
    console.log('🧪 Testing Error Message Handling...');
    
    try {
        // Test different error scenarios
        const testCases = [
            { code: null, signal: 'SIGTERM', expected: 'Process terminated by signal SIGTERM' },
            { code: null, signal: null, expected: 'Process terminated unexpectedly' },
            { code: 1, signal: null, expected: 'Process exited with code 1' }
        ];
        
        let allPassed = true;
        
        testCases.forEach((testCase, index) => {
            let errorMsg;
            
            if (testCase.code === null) {
                errorMsg = testCase.signal ? 
                    `Process terminated by signal ${testCase.signal}` : 
                    'Process terminated unexpectedly';
            } else {
                errorMsg = `Process exited with code ${testCase.code}`;
            }
            
            if (errorMsg === testCase.expected) {
                console.log(`✅ Error Message Test ${index + 1}: Correct message generated`);
            } else {
                console.log(`❌ Error Message Test ${index + 1}: Expected "${testCase.expected}", got "${errorMsg}"`);
                allPassed = false;
            }
        });
        
        return allPassed;
    } catch (error) {
        console.error('❌ Error Message Test: Exception caught:', error.message);
        return false;
    }
}

// Main test runner
async function runAllTests() {
    console.log('🚀 Starting Comprehensive Error Fix Tests...');
    console.log('=' .repeat(50));
    
    const tests = [
        { name: 'Process Exit Code Handling', fn: testProcessExitCodeHandling },
        { name: 'Beautiful Container Reference', fn: testBeautifulContainerReference },
        { name: 'Interactive Pre-Reporting Init', fn: testInteractivePreReportingInit },
        { name: 'Retry Logic', fn: testRetryLogic },
        { name: 'Error Message Handling', fn: testErrorMessageHandling }
    ];
    
    const results = [];
    
    for (const test of tests) {
        console.log(`\n🧪 Running: ${test.name}`);
        try {
            const result = await test.fn();
            results.push({ name: test.name, passed: result });
            console.log(result ? '✅ PASSED' : '❌ FAILED');
        } catch (error) {
            console.error(`❌ FAILED with exception: ${error.message}`);
            results.push({ name: test.name, passed: false });
        }
    }
    
    // Summary
    console.log('\n' + '=' .repeat(50));
    console.log('📊 TEST RESULTS SUMMARY:');
    console.log('=' .repeat(50));
    
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    
    results.forEach(result => {
        console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
    });
    
    console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
    
    if (passed === total) {
        console.log('🎉 ALL TESTS PASSED! Error fixes are working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Please review the fixes.');
    }
    
    return { passed, total, success: passed === total };
}

// Auto-run tests when script loads
if (typeof window !== 'undefined') {
    window.runErrorFixTests = runAllTests;
    console.log('🧪 Error fix tests loaded. Run window.runErrorFixTests() to execute.');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runAllTests };
}
