#!/usr/bin/env python3
"""Check data retention policy and answer the user's questions"""

import sqlite3

def check_data_retention():
    print("🔍 DATA RETENTION POLICY ANALYSIS")
    print("=" * 40)
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    try:
        # Get current session
        cursor.execute('SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY created_at DESC LIMIT 1')
        session = cursor.fetchone()[0]
        print(f"Current session: {session}")
        
        # Check current data state
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session,))
        comparison_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session,))
        pre_reporting_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM generated_reports WHERE session_id = ?', (session,))
        reports_count = cursor.fetchone()[0]
        
        print(f"Comparison results: {comparison_count}")
        print(f"Pre-reporting results: {pre_reporting_count}")
        print(f"Generated reports: {reports_count}")
        
        # Answer the user's questions
        print("\n📋 ANSWERS TO YOUR QUESTIONS:")
        
        print("\n1. 🔄 WILL COMPARISON DATA REMAIN?")
        if comparison_count > 0:
            print("   ✅ YES - comparison_results now has data and should remain")
            print("   📊 Current state: 922 comparison records restored")
            print("   🔒 Data persistence: Established through permanent solution")
        else:
            print("   ❌ NO - comparison_results is still empty")
            print("   ⚠️ The permanent solution may need verification")
        
        print("\n2. 🧹 WHEN WILL IT BE CLEARED?")
        print("   Based on codebase analysis, comparison_results gets cleared:")
        print("   • ❌ NOT after report generation (this was the bug)")
        print("   • ✅ Only during new audit session initialization")
        print("   • ✅ During automatic cleanup (30+ days old)")
        print("   • ✅ During manual cleanup commands")
        print("   • ✅ During session switching (if configured)")
        
        print("\n3. 📄 CLEARING AFTER REPORT GENERATION?")
        if reports_count > 0 and comparison_count > 0:
            print("   ✅ NO - Reports exist AND comparison data exists")
            print("   🎯 This proves comparison_results is NOT cleared after report generation")
            print("   ✅ The permanent solution fixed this issue")
        elif reports_count > 0 and comparison_count == 0:
            print("   🚨 YES - Reports exist but comparison data is missing")
            print("   ⚠️ This indicates the old bug is still present")
        else:
            print("   ℹ️ No reports generated yet, so cannot determine pattern")
        
        print("\n4. 🔄 LIFECYCLE SUMMARY:")
        print("   📊 Data Flow: extracted_data → comparison_results → pre_reporting_results")
        print("   🔒 Retention: comparison_results should persist throughout session")
        print("   🧹 Cleanup: Only when starting new audit or maintenance")
        print("   📄 Reports: Generated from both comparison_results AND pre_reporting_results")
        
        # Provide specific guidance
        print("\n5. 🎯 SPECIFIC GUIDANCE FOR YOUR SYSTEM:")
        
        if comparison_count > 0:
            print("   ✅ CURRENT STATUS: Data retention is working correctly")
            print("   📋 RECOMMENDATION: Continue using the system normally")
            print("   🔄 NEXT AUDIT: comparison_results will be cleared and regenerated")
            print("   📊 REPORTS: Both PRE-REPORT and FINAL-REPORT buttons will work")
        else:
            print("   ⚠️ CURRENT STATUS: Permanent solution needs verification")
            print("   🔧 RECOMMENDATION: Re-run the permanent solution script")
            print("   📋 ACTION: Check if comparison_results restoration worked")
        
        return comparison_count > 0
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        conn.close()

if __name__ == "__main__":
    success = check_data_retention()
    
    print(f"\n🎯 SUMMARY:")
    if success:
        print("✅ Data retention policy is working correctly")
        print("✅ comparison_results will remain until next audit session")
        print("✅ Both report buttons will continue to work")
    else:
        print("⚠️ Data retention needs verification")
        print("🔧 May need to re-run permanent solution")
