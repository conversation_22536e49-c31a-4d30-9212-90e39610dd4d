#!/usr/bin/env python3
"""Diagnose the current stuck job without interrupting it"""

import sqlite3
import sys
from datetime import datetime

def diagnose_current_job():
    print("🔍 DIAGNOSING CURRENT STUCK JOB (NON-INTRUSIVE)")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # 1. Get current session
        print("1. 📊 CURRENT SESSION STATUS:")
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        result = cursor.fetchone()
        if result:
            session = result[0]
            print(f"   Current session: {session}")
        else:
            print("   ❌ No current session found")
            return False
        
        # 2. Check actual data state
        print("\n2. 📊 ACTUAL DATA STATE:")
        
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))
        extracted_count = cursor.fetchone()[0]
        print(f"   Extracted data: {extracted_count} records")
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison data: {comparison_count} records")
        
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   Pre-reporting data: {pre_reporting_count} records")
        
        # 3. Check phase status tracking
        print("\n3. 📋 PHASE STATUS TRACKING:")
        cursor.execute('SELECT phase_name, status, started_at, completed_at, data_count FROM session_phases WHERE session_id = ?', (session,))
        phases = cursor.fetchall()
        
        if phases:
            for phase_name, status, started_at, completed_at, data_count in phases:
                print(f"   {phase_name}: {status}")
                if started_at:
                    print(f"     Started: {started_at}")
                if completed_at:
                    print(f"     Completed: {completed_at}")
                if data_count:
                    print(f"     Data count: {data_count}")
        else:
            print("   ⚠️ No phase tracking found")
        
        # 4. Check for process locks
        print("\n4. 🔒 PROCESS LOCKS:")
        try:
            cursor.execute('SELECT * FROM session_locks')
            locks = cursor.fetchall()
            if locks:
                print("   🚨 Active locks found:")
                for lock in locks:
                    print(f"     {lock}")
            else:
                print("   ✅ No active locks")
        except sqlite3.OperationalError:
            print("   ℹ️ No session_locks table")
        
        # 5. Identify the specific issue
        print("\n5. 🎯 ISSUE IDENTIFICATION:")
        
        if extracted_count > 0:
            # Check if EXTRACTION phase is marked as completed
            extraction_status = None
            for phase_name, status, started_at, completed_at, data_count in phases:
                if phase_name == 'EXTRACTION':
                    extraction_status = status
                    break
            
            if extraction_status != 'COMPLETED':
                print("   🚨 ISSUE: EXTRACTION has data but status not COMPLETED")
                print("   💡 CAUSE: Running job started before the fix was applied")
                print("   🔧 SOLUTION: Need to update status for running job")
                return "EXTRACTION_STATUS_NOT_UPDATED"
            else:
                print("   ✅ EXTRACTION status is COMPLETED")
                
                # Check comparison phase
                comparison_status = None
                for phase_name, status, started_at, completed_at, data_count in phases:
                    if phase_name == 'COMPARISON':
                        comparison_status = status
                        break
                
                if comparison_status == 'NOT_STARTED':
                    print("   🚨 ISSUE: COMPARISON phase not triggered")
                    print("   💡 CAUSE: Phase transition logic may be stuck")
                    print("   🔧 SOLUTION: Need to trigger comparison phase")
                    return "COMPARISON_NOT_TRIGGERED"
                elif comparison_status == 'IN_PROGRESS':
                    print("   🔄 COMPARISON phase is running")
                    return "COMPARISON_IN_PROGRESS"
                else:
                    print(f"   ℹ️ COMPARISON status: {comparison_status}")
                    return "UNKNOWN_COMPARISON_STATE"
        else:
            print("   🚨 ISSUE: No extracted data found")
            print("   💡 CAUSE: Extraction may have failed silently")
            return "NO_EXTRACTED_DATA"
    
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        return "DIAGNOSIS_ERROR"
    
    finally:
        conn.close()

def provide_solution(issue_type):
    print("\n" + "=" * 50)
    print("🔧 SOLUTION FOR RUNNING JOB")
    print("=" * 50)
    
    if issue_type == "EXTRACTION_STATUS_NOT_UPDATED":
        print("📋 ISSUE: Running job started before fix was applied")
        print("🔧 SOLUTION: Update phase status for running job")
        print("\n💡 SAFE APPROACH (won't interrupt running job):")
        print("   1. Update EXTRACTION phase status to COMPLETED")
        print("   2. Set COMPARISON phase status to READY")
        print("   3. Let the running process continue")
        
        return """
# Safe fix for running job
import sqlite3
from datetime import datetime

conn = sqlite3.connect('data/templar_payroll_auditor.db')
cursor = conn.cursor()

# Get current session
cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
session = cursor.fetchone()[0]

# Get extraction count
cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))
extracted_count = cursor.fetchone()[0]

# Update EXTRACTION to COMPLETED
cursor.execute('''
    UPDATE session_phases 
    SET status = 'COMPLETED', 
        completed_at = ?,
        data_count = ?
    WHERE session_id = ? AND phase_name = 'EXTRACTION'
''', (datetime.now().isoformat(), extracted_count, session))

# Set COMPARISON to READY
cursor.execute('''
    UPDATE session_phases 
    SET status = 'READY'
    WHERE session_id = ? AND phase_name = 'COMPARISON'
''', (session,))

conn.commit()
conn.close()
print(f"✅ Fixed phase status for running job")
"""
    
    elif issue_type == "COMPARISON_NOT_TRIGGERED":
        print("📋 ISSUE: COMPARISON phase not triggered")
        print("🔧 SOLUTION: Manually trigger comparison phase")
        
    elif issue_type == "NO_EXTRACTED_DATA":
        print("📋 ISSUE: No extracted data found")
        print("🔧 SOLUTION: Extraction may have failed - check terminal output")
        
    else:
        print("📋 ISSUE: Unknown state")
        print("🔧 SOLUTION: May need manual intervention")

if __name__ == "__main__":
    print("🚨 DIAGNOSING CURRENT STUCK JOB")
    print("This will check the state without interrupting the running process\n")
    
    issue = diagnose_current_job()
    
    if issue:
        provide_solution(issue)
        
        if issue == "EXTRACTION_STATUS_NOT_UPDATED":
            print("\n🚀 QUICK FIX SCRIPT:")
            print("Run this to fix the running job:")
            print("python -c \"")
            print("import sqlite3")
            print("from datetime import datetime")
            print("conn = sqlite3.connect('data/templar_payroll_auditor.db')")
            print("cursor = conn.cursor()")
            print("cursor.execute('SELECT session_id FROM current_session WHERE id = 1')")
            print("session = cursor.fetchone()[0]")
            print("cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))")
            print("count = cursor.fetchone()[0]")
            print("cursor.execute('UPDATE session_phases SET status = \\\"COMPLETED\\\", completed_at = ?, data_count = ? WHERE session_id = ? AND phase_name = \\\"EXTRACTION\\\"', (datetime.now().isoformat(), count, session))")
            print("cursor.execute('UPDATE session_phases SET status = \\\"READY\\\" WHERE session_id = ? AND phase_name = \\\"COMPARISON\\\"', (session,))")
            print("conn.commit()")
            print("conn.close()")
            print("print('✅ Fixed running job')")
            print("\"")
    
    print(f"\n🎯 ISSUE: {issue}")
    print("Use the solution above to fix the running job")
