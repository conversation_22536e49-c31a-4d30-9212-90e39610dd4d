#!/usr/bin/env python3
"""Final test of report generation with detailed debugging"""

import sys
import os
import sqlite3
import contextlib
import io

def test_final_report_generation():
    print("🔧 FINAL REPORT GENERATION TEST")
    print("=" * 40)
    
    try:
        # Get session
        conn = sqlite3.connect('payroll_audit.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY session_id DESC LIMIT 1")
        session = cursor.fetchone()[0]
        print(f"Session: {session}")
        
        # Test the exact query that should work
        print("\n1. Testing the fixed query directly:")
        
        query = '''SELECT cr.id, cr.employee_id, cr.employee_name, cr.section, cr.item_label,
                          cr.previous_value, cr.current_value, cr.change_type, cr.priority_level,
                          0 as numeric_difference,
                          0 as percentage_change,
                          COALESCE(pr.bulk_category, 'INDIVIDUAL') as bulk_category,
                          1 as bulk_size
                   FROM comparison_results cr
                   JOIN pre_reporting_results pr ON cr.id = pr.change_id
                   WHERE cr.session_id = ? AND pr.selected_for_report = 1
                   ORDER BY cr.priority_level DESC, cr.section, cr.employee_id'''
        
        cursor.execute(query, (session,))
        results = cursor.fetchall()
        
        print(f"Query results: {len(results)} records")
        
        if results:
            print("✅ Query working! Sample results:")
            for i, row in enumerate(results[:2]):
                print(f"  {i+1}. {row[3]}.{row[4]} ({row[7]}) - {row[2]}")
        else:
            print("❌ Query returned 0 results")
            conn.close()
            return False
        
        conn.close()
        
        # Test the PhasedProcessManager method directly
        print("\n2. Testing PhasedProcessManager._load_selected_changes_for_reporting:")
        
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        # Create manager and set session
        manager = PhasedProcessManager(debug_mode=False)
        manager.session_id = session
        
        # Test the method directly
        with contextlib.redirect_stdout(io.StringIO()):
            selected_changes = manager._load_selected_changes_for_reporting()
        
        print(f"Selected changes: {len(selected_changes)}")
        
        if selected_changes:
            print("✅ _load_selected_changes_for_reporting working!")
            for i, change in enumerate(selected_changes[:2]):
                print(f"  {i+1}. {change['section_name']}.{change['item_label']} ({change['change_type']})")
            
            # Now test the full report generation
            print("\n3. Testing full report generation:")
            
            with contextlib.redirect_stdout(io.StringIO()):
                result = manager.generate_final_reports(session)
            
            print(f"Report generation result: {result}")
            
            if result.get('success'):
                print("🎉 SUCCESS! Reports generated!")
                
                # Check database
                conn = sqlite3.connect('payroll_audit.db')
                cursor = conn.cursor()
                
                cursor.execute("SELECT COUNT(*) FROM generated_reports WHERE session_id = ?", (session,))
                count = cursor.fetchone()[0]
                
                print(f"Reports in database: {count}")
                
                if count > 0:
                    cursor.execute("""
                        SELECT report_type, file_path, file_size 
                        FROM generated_reports 
                        WHERE session_id = ?
                    """, (session,))
                    
                    reports = cursor.fetchall()
                    print("Generated reports:")
                    for report_type, file_path, file_size in reports:
                        exists = "✅" if os.path.exists(file_path) else "❌"
                        print(f"  {exists} {report_type}: {file_path} ({file_size} bytes)")
                
                conn.close()
                return True
            else:
                print(f"❌ Report generation failed: {result.get('error')}")
                return False
        else:
            print("❌ _load_selected_changes_for_reporting returned 0 results")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_report_generation()
    if success:
        print("\n🎉 COMPLETE SUCCESS! Generate Report buttons are now working!")
    else:
        print("\n❌ Still has issues - needs more debugging")
    sys.exit(0 if success else 1)
