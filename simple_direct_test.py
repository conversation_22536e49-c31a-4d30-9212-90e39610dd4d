#!/usr/bin/env python3
"""Simple direct test to see actual failures"""

import sqlite3
import sys
import os

def test_direct():
    print("🔍 DIRECT TEST OF REPORT GENERATION")
    print("=" * 40)
    
    try:
        # 1. Test database connection
        print("1. Testing database connection...")
        conn = sqlite3.connect('payroll_audit.db')
        cursor = conn.cursor()
        print("   ✅ Database connected")
        
        # 2. Get session
        print("\n2. Getting session...")
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY session_id DESC LIMIT 1")
        session_result = cursor.fetchone()
        if session_result:
            session = session_result[0]
            print(f"   ✅ Session found: {session}")
        else:
            print("   ❌ No session found")
            return False
        
        # 3. Test the fixed query
        print("\n3. Testing fixed query...")
        query = '''SELECT cr.id, cr.employee_id, cr.employee_name, cr.section, cr.item_label,
                          cr.previous_value, cr.current_value, cr.change_type, cr.priority_level
                   FROM comparison_results cr
                   JOIN pre_reporting_results pr ON cr.id = pr.change_id
                   WHERE cr.session_id = ? AND pr.selected_for_report = 1'''
        
        cursor.execute(query, (session,))
        results = cursor.fetchall()
        print(f"   Query returned: {len(results)} records")
        
        if results:
            print("   ✅ Query working")
            for i, row in enumerate(results[:2]):
                print(f"     {i+1}. {row[3]}.{row[4]} ({row[7]})")
        else:
            print("   ❌ Query returned 0 results")
            
            # Debug why
            print("\n   Debugging query failure...")
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session,))
            cr_count = cursor.fetchone()[0]
            print(f"     comparison_results for session: {cr_count}")
            
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ? AND selected_for_report = 1", (session,))
            pr_count = cursor.fetchone()[0]
            print(f"     pre_reporting_results selected: {pr_count}")
            
            cursor.execute("""
                SELECT COUNT(*) 
                FROM comparison_results cr
                JOIN pre_reporting_results pr ON cr.id = pr.change_id
                WHERE cr.session_id = ?
            """, (session,))
            join_count = cursor.fetchone()[0]
            print(f"     JOIN result: {join_count}")
            
            return False
        
        conn.close()
        
        # 4. Test PhasedProcessManager import
        print("\n4. Testing PhasedProcessManager import...")
        try:
            sys.path.append('core')
            from phased_process_manager import PhasedProcessManager
            print("   ✅ Import successful")
        except Exception as e:
            print(f"   ❌ Import failed: {e}")
            return False
        
        # 5. Test manager initialization
        print("\n5. Testing manager initialization...")
        try:
            manager = PhasedProcessManager(debug_mode=False)
            print("   ✅ Manager created")
        except Exception as e:
            print(f"   ❌ Manager creation failed: {e}")
            return False
        
        # 6. Test the method directly
        print("\n6. Testing _load_selected_changes_for_reporting...")
        try:
            manager.session_id = session
            selected_changes = manager._load_selected_changes_for_reporting()
            print(f"   Method returned: {len(selected_changes)} changes")
            
            if selected_changes:
                print("   ✅ Method working")
                for i, change in enumerate(selected_changes[:2]):
                    print(f"     {i+1}. {change.get('section_name', 'N/A')}.{change.get('item_label', 'N/A')}")
            else:
                print("   ❌ Method returned 0 changes")
                return False
        except Exception as e:
            print(f"   ❌ Method failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 7. Test full report generation
        print("\n7. Testing full report generation...")
        try:
            result = manager.generate_final_reports(session)
            print(f"   Result: {result}")
            
            if result.get('success'):
                print("   ✅ Report generation successful")
                
                # Check database
                conn = sqlite3.connect('payroll_audit.db')
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM generated_reports WHERE session_id = ?", (session,))
                count = cursor.fetchone()[0]
                print(f"   Reports in database: {count}")
                conn.close()
                
                return True
            else:
                print(f"   ❌ Report generation failed: {result.get('error')}")
                return False
        except Exception as e:
            print(f"   ❌ Report generation exception: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_direct()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}")
    sys.exit(0 if success else 1)
