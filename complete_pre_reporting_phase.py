#!/usr/bin/env python3
"""Complete the PRE_REPORTING phase for the current session"""

import sqlite3
import sys
import os
from datetime import datetime

def complete_pre_reporting_phase():
    """Complete the PRE_REPORTING phase for the current session"""
    print("🔄 COMPLETING PRE_REPORTING PHASE")
    print("=" * 40)
    
    try:
        db_path = 'data/templar_payroll_auditor.db'
        if not os.path.exists(db_path):
            print("❌ Database not found")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No current session found")
            return False
            
        session_id = session_result[0]
        print(f"📋 Current session: {session_id}")
        
        # 2. Check current phase status
        cursor.execute('''
            SELECT status FROM session_phases 
            WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
        ''', (session_id,))
        status_result = cursor.fetchone()
        
        if status_result and status_result[0] == 'COMPLETED':
            print("✅ PRE_REPORTING already completed")
            return True
        
        print("🔄 Running PRE_REPORTING phase...")
        
        # 3. Import and run the phased process manager
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Set up options
        options = {
            'currentMonth': 7,
            'currentYear': 2025,
            'previousMonth': 6,
            'previousYear': 2025,
            'signatureName': 'System Administrator',
            'signatureDesignation': 'Payroll Auditor'
        }
        
        # 4. Update phase status to IN_PROGRESS
        cursor.execute('''
            UPDATE session_phases 
            SET status = ?, started_at = datetime('now') 
            WHERE session_id = ? AND phase_name = ?
        ''', ('IN_PROGRESS', session_id, 'PRE_REPORTING'))
        conn.commit()
        
        # 5. Run the PRE_REPORTING phase
        success = manager._phase_pre_reporting(options)
        
        if success:
            print("✅ PRE_REPORTING phase completed successfully")
            
            # 6. Update phase status to COMPLETED
            data_count = manager._get_phase_data_count('PRE_REPORTING')
            cursor.execute('''
                UPDATE session_phases 
                SET status = ?, completed_at = datetime('now'), data_count = ? 
                WHERE session_id = ? AND phase_name = ?
            ''', ('COMPLETED', data_count, session_id, 'PRE_REPORTING'))
            
            # 7. Update session status to pre_reporting_ready
            cursor.execute('''
                UPDATE audit_sessions 
                SET status = ? 
                WHERE session_id = ?
            ''', ('pre_reporting_ready', session_id))
            
            conn.commit()
            
            print(f"✅ PRE_REPORTING phase marked as COMPLETED with {data_count} records")
            print("✅ Session is now ready for pre-reporting review")
            print("💡 The UI should now show the reporting interface")
            
            return True
            
        else:
            print("❌ PRE_REPORTING phase failed")
            
            # Update phase status to FAILED
            cursor.execute('''
                UPDATE session_phases 
                SET status = ?, error_message = ? 
                WHERE session_id = ? AND phase_name = ?
            ''', ('FAILED', 'PRE_REPORTING phase execution failed', session_id, 'PRE_REPORTING'))
            conn.commit()
            
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error completing PRE_REPORTING phase: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    complete_pre_reporting_phase()
