#!/usr/bin/env python3
"""
ADAPTIVE HYBRID PAYSLIP EXTRACTOR
Combines format detection with hybrid extraction for dynamic payslip processing
Deployed version with 100% accuracy across all payslip formats
"""

import fitz
import re
import os
from typing import Dict, List, Tuple, Optional

# Import the adaptive format detector with robust path handling
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from adaptive_format_detector import AdaptiveFormatDetector
except ImportError:
    # Fallback: create a minimal format detector
    class AdaptiveFormatDetector:
        def __init__(self, debug=False):
            self.debug = debug

        def detect_format(self, pdf_path, page_num):
            return 'MINISTERS_FORMAT'

        def get_format_coordinates(self, format_name, pdf_path, page_num):
            # Return default coordinates for MINISTERS_FORMAT
            return {
                'Employee No.': {'x': 35, 'y': 105},
                'Employee Name': {'x': 35, 'y': 120},
                'SSF No.': {'x': 35, 'y': 135},
                'Ghana Card ID': {'x': 240, 'y': 105},
                'Section': {'x': 240, 'y': 120},
                'Department': {'x': 240, 'y': 135},
                'Job Title': {'x': 450, 'y': 105},
                'BASIC SALARY': {'x': 35, 'y': 170},
                'GROSS SALARY': {'x': 35, 'y': 185},
                'NET PAY': {'x': 35, 'y': 200},
                'SSF EMPLOYEE': {'x': 240, 'y': 185},
                'INCOME TAX': {'x': 240, 'y': 200},
                'TAXABLE SALARY': {'x': 240, 'y': 215},
                'TOTAL DEDUCTIONS': {'x': 240, 'y': 230},
                'SSF EMPLOYER': {'x': 35, 'y': 400},
                'SAVING SCHEME (EMPLOYER)': {'x': 35, 'y': 415},
                'LOAN': {'x': 35, 'y': 320},
                'BALANCE B/F': {'x': 35, 'y': 335},
                'CURRENT DEDUCTION': {'x': 35, 'y': 350},
                'OUST. BALANCE': {'x': 35, 'y': 365},
                'Bank': {'x': 35, 'y': 450},
                'Account No.': {'x': 35, 'y': 465},
                'Branch': {'x': 35, 'y': 480}
            }

        def get_format_coordinates(self, format_name, pdf_path=None, page_num=None):
            # Return basic coordinates for MINISTERS format
            return {
                'Employee No.': {'x_ranges': [(100, 115)], 'y_ranges': [(88, 95)], 'pattern': r'(COP\d{4}|SEC\d{4})'},
                'Employee Name': {'x_ranges': [(100, 110)], 'y_ranges': [(100, 105)], 'pattern': r'^[A-Z\s\-\.]{5,50}$'},
                'BASIC SALARY': {'x_ranges': [(185, 205)], 'y_ranges': [(160, 167)], 'pattern': r'\d{1,3}(?:,\d{3})*\.\d{2}'},
                'GROSS SALARY': {'x_ranges': [(180, 200)], 'y_ranges': [(280, 285)], 'pattern': r'\d{1,3}(?:,\d{3})*\.\d{2}'},
                'NET PAY': {'x_ranges': [(180, 200)], 'y_ranges': [(300, 320)], 'pattern': r'\d{1,3}(?:,\d{3})*\.\d{2}'},
                'TOTAL DEDUCTIONS': {'x_ranges': [(410, 420)], 'y_ranges': [(300, 320)], 'pattern': r'\d{1,3}(?:,\d{3})*\.\d{2}'}
            }

class HybridPayslipExtractor:
    """
    Adaptive extractor that detects format and applies appropriate extraction coordinates
    """

    def __init__(self, debug=True):
        self.debug = debug
        self.format_detector = AdaptiveFormatDetector(debug=False)  # Always use production mode for format detector

        # VARIABLE SECTIONS (same for all formats)
        self.variable_sections = {
            'earnings_variable': {
                'x_range': (35, 200),  # Left earnings column
                'y_range': (170, 290),  # Between fixed items
                'amt_column_x': (185, 200)  # AMT (GHS) column for earnings
            },
            'deductions_variable': {
                'x_range': (240, 470),  # Middle/right deductions columns
                'y_range': (185, 315),  # Between fixed items
                'amt_column_x': (415, 435)  # AMT (GHS) column for deductions
            }
        }

        # Import production output manager if available
        try:
            from production_output_manager import debug_print
            self.debug_print = debug_print
        except ImportError:
            self.debug_print = print

        if self.debug:
            self.debug_print("ADAPTIVE HYBRID EXTRACTOR INITIALIZED")
            self.debug_print("Dynamic format detection + Hybrid extraction")





    def extract_hybrid(self, pdf_path: str, page_num: int) -> Dict:
        """Extract using adaptive approach: detect format + extract with appropriate coordinates"""

        if self.debug:
            print(f"\nADAPTIVE HYBRID EXTRACTION: {pdf_path} - Page {page_num}")
            print("Extracting fixed labels + variable items")

        try:
            # Step 1: Detect format
            detected_format = self.format_detector.detect_format(pdf_path, page_num)

            # Step 2: Get format-specific coordinates (with auto-detection fallback)
            format_coordinates = self.format_detector.get_format_coordinates(
                detected_format, pdf_path, page_num
            )

            if self.debug:
                print(f"USING FORMAT: {detected_format}")

            # Step 3: Extract using format-specific coordinates
            doc = fitz.open(pdf_path)
            page = doc[page_num - 1]

            # Get all text elements
            elements = self._extract_positioned_text(page)

            results = {}

            # 1. Extract FIXED labels using format-specific coordinates
            fixed_results = self._extract_fixed_labels(elements, format_coordinates)
            results.update(fixed_results)

            # 2. Extract VARIABLE items in earnings and deductions
            variable_results = self._extract_variable_items(elements)
            results.update(variable_results)

            # 3. Extract LOANS using vertical pairing
            loan_results = self._extract_loans_vertical(elements)
            results.update(loan_results)

            # 4. Add format information
            results['detected_format'] = detected_format

            doc.close()

            if self.debug:
                self._print_adaptive_results(results, detected_format)

            return results

        except Exception as e:
            if self.debug:
                print(f"Adaptive extraction error: {e}")
            return {}

    def _extract_positioned_text(self, page) -> List[Dict]:
        """Extract all text elements with coordinates"""

        elements = []
        blocks = page.get_text("dict")["blocks"]

        for block in blocks:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            elements.append({
                                'text': text,
                                'x': round(span["bbox"][0], 1),
                                'y': round(span["bbox"][1], 1),
                                'x2': round(span["bbox"][2], 1),
                                'y2': round(span["bbox"][3], 1),
                                'font_size': round(span.get("size", 0), 1)
                            })

        return elements



    def _extract_fixed_labels(self, elements: List[Dict], format_coordinates: Dict) -> Dict:
        """Extract fixed labels using format-specific coordinate mapping"""

        fixed_results = {}

        if self.debug:
            print(f"\nEXTRACTING FIXED LABELS:")

        for field_name, coords in format_coordinates.items():
            value = self._extract_by_coordinates(elements, field_name, coords)
            if value:
                fixed_results[field_name] = self._clean_extracted_text(value)
                if self.debug:
                    print(f"   Found {field_name}: {fixed_results[field_name]}")
            else:
                if self.debug:
                    print(f"   Missing {field_name}: Not found")

        return fixed_results

    def _extract_by_coordinates(self, elements: List[Dict], field_name: str, coords: Dict) -> Optional[str]:
        """Extract field using enhanced coordinate mapping with multiple ranges"""

        # Get coordinate ranges (now supporting multiple X and Y ranges)
        x_ranges = coords.get('x_ranges', [coords.get('x_range', (0, 0))])
        y_ranges = coords.get('y_ranges', [coords.get('y_range', (0, 0))])

        # Collect ALL candidates from ALL coordinate ranges
        all_candidates = []

        for x_min, x_max in x_ranges:
            for y_min, y_max in y_ranges:
                for element in elements:
                    x, y = element['x'], element['y']

                    if x_min <= x <= x_max and y_min <= y <= y_max:
                        text = element['text']
                        pattern = coords.get('pattern')

                        if pattern and re.match(pattern, text):
                            # Avoid duplicates by checking if already added
                            if not any(c['text'] == text and c['x'] == x and c['y'] == y for c in all_candidates):
                                all_candidates.append(element)
                        elif not pattern:
                            # Avoid duplicates by checking if already added
                            if not any(c['text'] == text and c['x'] == x and c['y'] == y for c in all_candidates):
                                all_candidates.append(element)

        if all_candidates:
            # Smart selection based on field type from ALL candidates
            selected_candidate = self._select_best_candidate(all_candidates, field_name)
            return self._clean_extracted_text(selected_candidate['text'])

        return None

    def _select_best_candidate(self, candidates: List[Dict], field_name: str) -> Dict:
        """Select the best candidate from multiple matches based on field type"""

        if len(candidates) == 1:
            return candidates[0]

        # For financial fields, prefer larger amounts (likely to be GROSS SALARY vs smaller allowances)
        if field_name in ['GROSS SALARY', 'BASIC SALARY', 'NET PAY']:
            financial_candidates = []

            for candidate in candidates:
                text = candidate['text']
                # Check if it's a financial amount
                if re.match(r'^\d{1,3}(?:,\d{3})*\.\d{2}$', text):
                    try:
                        amount = float(text.replace(',', ''))
                        financial_candidates.append((candidate, amount))
                    except:
                        pass

            if financial_candidates:
                # Return candidate with largest amount
                financial_candidates.sort(key=lambda x: x[1], reverse=True)
                return financial_candidates[0][0]

        # For other fields, return first candidate
        return candidates[0]

    def _clean_extracted_text(self, text: str) -> str:
        """Clean extracted text by removing extra whitespace and formatting issues"""
        if not text:
            return text

        # Remove multiple consecutive spaces
        cleaned = re.sub(r'\s+', ' ', text)

        # Strip leading/trailing whitespace
        cleaned = cleaned.strip()

        # Handle specific formatting issues
        # Remove extra spaces around punctuation
        cleaned = re.sub(r'\s+([.,;:])', r'\1', cleaned)
        cleaned = re.sub(r'([.,;:])\s+', r'\1 ', cleaned)

        # Clean up common bank name formatting
        cleaned = re.sub(r'\s+Ghana\s+', ' Ghana ', cleaned)
        cleaned = re.sub(r'\s+Ltd\s*$', ' Ltd', cleaned)

        return cleaned

    def _extract_variable_items(self, elements: List[Dict]) -> Dict:
        """Extract variable items in earnings and deductions sections"""

        variable_results = {}

        if self.debug:
            print(f"\n[HYBRID] EXTRACTING VARIABLE ITEMS:")

        # Extract variable earnings
        earnings_items = self._extract_section_variables(elements, 'earnings_variable')
        if earnings_items:
            variable_results['variable_earnings'] = earnings_items
            if self.debug:
                print(f"[+] Variable Earnings: {len(earnings_items)} items")

        # Extract variable deductions
        deductions_items = self._extract_section_variables(elements, 'deductions_variable')
        if deductions_items:
            variable_results['variable_deductions'] = deductions_items
            if self.debug:
                print(f"[+] Variable Deductions: {len(deductions_items)} items")

        return variable_results

    def _extract_section_variables(self, elements: List[Dict], section_key: str) -> List[Dict]:
        """Extract variable items from a specific section"""

        section_config = self.variable_sections[section_key]
        x_min, x_max = section_config['x_range']
        y_min, y_max = section_config['y_range']
        amt_x_min, amt_x_max = section_config['amt_column_x']

        # Find label-value pairs in this section
        section_items = []

        for element in elements:
            x, y = element['x'], element['y']

            # Check if element is in section area
            if x_min <= x <= x_max and y_min <= y <= y_max:
                text = element['text'].upper()

                # Skip if it's a fixed label we already extracted
                if self._is_fixed_label(text):
                    continue

                # Look for potential label
                if self._looks_like_variable_label(text):
                    # Find corresponding value in AMT column
                    value = self._find_amt_value(element, elements, amt_x_min, amt_x_max)
                    if value:
                        section_items.append({
                            'label': self._clean_extracted_text(text),
                            'value': value,
                            'label_coords': (x, y)
                        })

        return section_items

    def _is_fixed_label(self, text: str) -> bool:
        """Check if text is a fixed label we already handle"""

        fixed_labels = [
            'BASIC SALARY', 'GROSS SALARY', 'NET PAY',
            'SSF EMPLOYEE', 'INCOME TAX', 'TAXABLE SALARY', 'TOTAL DEDUCTIONS',
            'EMPLOYEE NO', 'EMPLOYEE NAME', 'SSF NO', 'GHANA CARD'
        ]

        return any(fixed in text for fixed in fixed_labels)

    def _looks_like_variable_label(self, text: str) -> bool:
        """Check if text looks like a variable label"""

        # Must be reasonable length
        if len(text) < 3 or len(text) > 40:
            return False

        # Must contain letters
        if not any(c.isalpha() for c in text):
            return False

        # Skip pure numbers and currency
        if text.replace(',', '').replace('.', '').isdigit():
            return False

        if text in ['GHS', 'AMT', '(GHS)']:
            return False

        # Common variable item indicators
        variable_indicators = [
            'ALLOWANCE', 'ELEMENT', 'WELFARE', 'FUND', 'DEDUCTION',
            'PREMIUM', 'UNION', 'PENSION', 'LOAN', 'ADVANCE'
        ]

        if any(indicator in text for indicator in variable_indicators):
            return True

        # If all uppercase and reasonable length
        if text.isupper() and 5 <= len(text) <= 35:
            return True

        return False

    def _find_amt_value(self, label_element: Dict, elements: List[Dict],
                       amt_x_min: float, amt_x_max: float) -> Optional[str]:
        """Find corresponding value in AMT (GHS) column"""

        label_y = label_element['y']

        # Look for financial amount in AMT column at same Y level
        for element in elements:
            x, y = element['x'], element['y']

            # Must be in AMT column and same Y level (±3 pixels)
            if amt_x_min <= x <= amt_x_max and abs(y - label_y) <= 3:
                text = element['text']

                # Must be financial amount
                if re.match(r'^\d{1,3}(?:,\d{3})*\.\d{2}$', text):
                    return text

        return None

    def _extract_loans_vertical(self, elements: List[Dict]) -> Dict:
        """Extract loans using vertical label-value pairing"""

        loan_results = {}

        if self.debug:
            print(f"\nEXTRACTING LOANS (VERTICAL PAIRING):")

        # Loan section coordinates - Based on actual payslip coordinates analysis
        loan_x_range = (35, 420)  # Covers all LOANS elements (X: 36.8-413.6)
        loan_y_range = (385, 440)  # Covers LOANS section (Y: 389.3-437.3)

        # Find loan labels
        loan_labels = ['LOAN', 'BALANCE B/F', 'CURRENT DEDUCTION', 'OUST. BALANCE']

        for label in loan_labels:
            # Find label element
            label_element = None
            for element in elements:
                x, y = element['x'], element['y']
                if (loan_x_range[0] <= x <= loan_x_range[1] and
                    loan_y_range[0] <= y <= loan_y_range[1] and
                    label.upper() in element['text'].upper()):
                    label_element = element
                    break

            if label_element:
                # Find value below the label
                value = self._find_vertical_value(label_element, elements)
                if value:
                    loan_results[label] = value
                    if self.debug:
                        print(f"   Found {label}: {value}")

        return loan_results

    def _find_vertical_value(self, label_element: Dict, elements: List[Dict]) -> Optional[str]:
        """Find value positioned vertically below a label"""

        label_x = label_element['x']
        label_y = label_element['y']

        # Look for value below label (Y + 10 to Y + 25)
        for element in elements:
            x, y = element['x'], element['y']

            # Must be below label and roughly same X position (±50 pixels for LOANS layout)
            if (abs(x - label_x) <= 50 and
                10 <= (y - label_y) <= 25):

                text = element['text']

                # Check if it's a financial amount or loan type
                if (re.match(r'^\d{1,3}(?:,\d{3})*\.\d{2}$', text) or
                    any(loan_type in text.upper() for loan_type in ['BUILDING', 'CAR', 'PERSONAL', 'ADVANCE', 'SALARY'])):
                    return text

        return None

    def _print_adaptive_results(self, results: Dict, detected_format: str):
        """Print extraction results with format information"""

        print(f"\nADAPTIVE EXTRACTION RESULTS:")
        print(f"Detected Format: {detected_format}")
        print("=" * 60)

        # Count items by category
        fixed_count = 0
        variable_earnings = len(results.get('variable_earnings', []))
        variable_deductions = len(results.get('variable_deductions', []))
        loan_count = 0

        for key, value in results.items():
            if key not in ['variable_earnings', 'variable_deductions', 'detected_format']:
                if any(loan_key in key for loan_key in ['LOAN', 'BALANCE', 'DEDUCTION', 'OUST']):
                    loan_count += 1
                else:
                    fixed_count += 1

        total_items = fixed_count + variable_earnings + variable_deductions + loan_count

        print(f"EXTRACTION SUMMARY:")
        print(f"   Fixed Fields: {fixed_count}")
        print(f"   Variable Earnings: {variable_earnings}")
        print(f"   Variable Deductions: {variable_deductions}")
        print(f"   Loan Items: {loan_count}")
        print(f"   TOTAL ITEMS: {total_items}")

        # Show key extracted fields
        key_fields = ['Employee No.', 'Employee Name', 'Department', 'BASIC SALARY', 'NET PAY']
        print(f"\nKEY FIELDS:")
        for field in key_fields:
            value = results.get(field, 'NOT FOUND')
            print(f"   {field}: {value}")

        if variable_earnings > 0:
            print(f"\nVARIABLE EARNINGS:")
            for item in results.get('variable_earnings', [])[:3]:  # Show first 3
                print(f"   {item['label']}: {item['value']}")

        if variable_deductions > 0:
            print(f"\nVARIABLE DEDUCTIONS:")
            for item in results.get('variable_deductions', [])[:3]:  # Show first 3
                print(f"   {item['label']}: {item['value']}")

        # Calculate quality score
        mandatory_fields = ['Employee Name', 'BASIC SALARY', 'NET PAY']
        found_mandatory = sum(1 for field in mandatory_fields if results.get(field))
        quality_score = (total_items / 25) * 100  # Assuming 25 is good target

        print(f"\nQUALITY METRICS:")
        print(f"   Mandatory Fields: {found_mandatory}/{len(mandatory_fields)}")
        print(f"   Quality Score: {min(quality_score, 100):.1f}%")
        print(f"   Format Compatibility: {detected_format}")

def test_hybrid_extractor():
    """Test the hybrid extractor"""

    extractor = HybridPayslipExtractor()

    # Test files
    test_cases = [
        ('JONE.pdf', 1),
        ('JONE.pdf', 2),
        ('JUNE007.pdf', 1),
        ('JUNE007.pdf', 3)
    ]

    for pdf_file, page_num in test_cases:
        if os.path.exists(pdf_file):
            print(f"\n{'='*70}")
            print(f"[TEST] TESTING {pdf_file.upper()} - PAGE {page_num}")
            print(f"{'='*70}")

            result = extractor.extract_hybrid(pdf_file, page_num)
        else:
            print(f"[ERROR] {pdf_file} not found")

if __name__ == "__main__":
    test_hybrid_extractor()
