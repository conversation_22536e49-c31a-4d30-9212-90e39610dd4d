#!/usr/bin/env python3
"""
DATA BUILDER API
Command-line interface for the Data Builder module
Provides spreadsheet generation and data analysis capabilities
"""

import os
import sys
import json
import argparse
import contextlib
import io
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add core directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

try:
    from core.data_builder import DataBuilder
    from core.python_database_manager import PythonDatabaseManager
    DATA_BUILDER_AVAILABLE = True
except ImportError:
    DATA_BUILDER_AVAILABLE = False

class DataBuilderAPI:
    """
    Data Builder API for command-line operations
    """

    def __init__(self, debug=False):
        self.debug = debug
        self.data_builder = None
        self.database = None

        if DATA_BUILDER_AVAILABLE:
            try:
                # Suppress stdout during initialization to prevent debug messages
                # from interfering with JSON output
                if not debug:
                    with contextlib.redirect_stdout(io.StringIO()):
                        self.data_builder = DataBuilder()
                        self.database = PythonDatabaseManager()
                else:
                    self.data_builder = DataBuilder()
                    self.database = PythonDatabaseManager()
                    print("✅ Data Builder API initialized")
            except Exception as e:
                if self.debug:
                    print(f"⚠️ Data Builder initialization failed: {e}")
        else:
            if self.debug:
                print("❌ Data Builder modules not available")

    def initialize(self) -> Dict[str, Any]:
        """Initialize Data Builder system"""
        try:
            if not DATA_BUILDER_AVAILABLE:
                return {
                    'success': False,
                    'error': 'Data Builder modules not available'
                }
            
            # Initialize database tables
            if self.database:
                self.database.ensure_tables_exist()
            
            if self.debug:
                print("🚀 Data Builder system initialized")
            
            return {
                'success': True,
                'message': 'Data Builder initialized successfully',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def build_spreadsheet(self, pdf_path: str, month_year: str, 
                         selected_items: list = None, 
                         include_analytics: bool = True,
                         include_varying: bool = True) -> Dict[str, Any]:
        """
        Build comprehensive spreadsheet from payroll data
        
        Args:
            pdf_path: Path to PDF file
            month_year: Month and year (e.g., "June 2025")
            selected_items: List of specific items to include
            include_analytics: Include analytics sheet
            include_varying: Include varying items
        
        Returns:
            Result dictionary with success status and file path
        """
        try:
            if not self.data_builder:
                return {
                    'success': False,
                    'error': 'Data Builder not available'
                }
            
            if self.debug:
                print(f"📊 Building spreadsheet for: {pdf_path}")
                print(f"   Month/Year: {month_year}")
                print(f"   Selected items: {len(selected_items) if selected_items else 'All'}")
            
            # Use Data Builder to create spreadsheet
            result = self.data_builder.build_spreadsheet(
                pdf_path, 
                month_year, 
                selected_items or [],
                include_analytics,
                include_varying
            )
            
            if self.debug:
                if result.get('success'):
                    print(f"✅ Spreadsheet created: {result.get('output_file')}")
                else:
                    print(f"❌ Spreadsheet creation failed: {result.get('error')}")
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_available_items(self, pdf_path: str) -> Dict[str, Any]:
        """
        Get list of available items from PDF for selection
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Dictionary with available items by section
        """
        try:
            if not self.data_builder:
                return {
                    'success': False,
                    'error': 'Data Builder not available'
                }
            
            # Extract sample data to get available items
            sample_result = self.data_builder._extract_payslip_data(pdf_path)
            
            if not sample_result.get('success'):
                return sample_result
            
            # Get unique items from extracted data
            available_items = {}
            sample_data = sample_result.get('data', [])
            
            for employee in sample_data[:5]:  # Sample first 5 employees
                for section, items in employee.get('sections', {}).items():
                    if section not in available_items:
                        available_items[section] = set()
                    available_items[section].update(items.keys())
            
            # Convert sets to lists for JSON serialization
            for section in available_items:
                available_items[section] = sorted(list(available_items[section]))
            
            return {
                'success': True,
                'available_items': available_items,
                'total_sections': len(available_items),
                'total_items': sum(len(items) for items in available_items.values())
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_session_info(self, session_id: str = None) -> Dict[str, Any]:
        """Get information about Data Builder sessions"""
        try:
            if not self.database:
                return {
                    'success': False,
                    'error': 'Database not available'
                }
            
            if session_id:
                # Get specific session
                session = self.database.get_session(session_id)
                return {
                    'success': True,
                    'session': session
                }
            else:
                # Get all sessions
                sessions = self.database.get_all_sessions()
                return {
                    'success': True,
                    'sessions': sessions,
                    'total_sessions': len(sessions)
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def cleanup_old_sessions(self, days_old: int = 30) -> Dict[str, Any]:
        """Clean up old Data Builder sessions"""
        try:
            if not self.database:
                return {
                    'success': False,
                    'error': 'Database not available'
                }
            
            cleaned_count = self.database.cleanup_old_sessions(days_old)
            
            return {
                'success': True,
                'cleaned_sessions': cleaned_count,
                'message': f'Cleaned up {cleaned_count} sessions older than {days_old} days'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        try:
            return {
                'success': True,
                'isProcessing': False,
                'systemStatus': {
                    'initialized': DATA_BUILDER_AVAILABLE,
                    'databaseConnected': self.database is not None and self.database.is_connected if self.database else False
                }
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_statistics(self) -> Dict[str, Any]:
        """Get system statistics"""
        try:
            if not self.database:
                return {
                    'success': True,
                    'statistics': {
                        'employees': 0,
                        'columns': 0,
                        'dataDeficits': 0
                    }
                }

            # Get basic statistics from database
            stats = {
                'employees': 0,
                'columns': 0,
                'dataDeficits': 0
            }

            return {
                'success': True,
                'statistics': stats
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

def main():
    """Command line interface for Data Builder API"""
    parser = argparse.ArgumentParser(description='Data Builder API')
    parser.add_argument('command', help='Command to execute')
    parser.add_argument('--pdf-path', help='Path to PDF file')
    parser.add_argument('--month-year', help='Month and year (e.g., "June 2025")')
    parser.add_argument('--selected-items', help='JSON string of selected items')
    parser.add_argument('--include-analytics', action='store_true', help='Include analytics')
    parser.add_argument('--include-varying', action='store_true', help='Include varying items')
    parser.add_argument('--session-id', help='Session ID for session operations')
    parser.add_argument('--days-old', type=int, default=30, help='Days old for cleanup')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    api = DataBuilderAPI(debug=args.debug)
    result = {}
    
    try:
        if args.command == 'initialize':
            result = api.initialize()
            
        elif args.command == 'build-spreadsheet':
            if not args.pdf_path or not args.month_year:
                result = {
                    'success': False,
                    'error': 'PDF path and month-year are required for build-spreadsheet'
                }
            else:
                selected_items = []
                if args.selected_items:
                    try:
                        selected_items = json.loads(args.selected_items)
                    except json.JSONDecodeError:
                        result = {
                            'success': False,
                            'error': 'Invalid JSON format for selected-items'
                        }
                        
                if not result:  # Only proceed if no error above
                    result = api.build_spreadsheet(
                        args.pdf_path,
                        args.month_year,
                        selected_items,
                        args.include_analytics,
                        args.include_varying
                    )
                    
        elif args.command == 'get-available-items':
            if not args.pdf_path:
                result = {
                    'success': False,
                    'error': 'PDF path is required for get-available-items'
                }
            else:
                result = api.get_available_items(args.pdf_path)
                
        elif args.command == 'get-session-info':
            result = api.get_session_info(args.session_id)
            
        elif args.command == 'cleanup-sessions':
            result = api.cleanup_old_sessions(args.days_old)

        elif args.command == 'status':
            result = api.get_status()

        elif args.command == 'statistics':
            result = api.get_statistics()

        else:
            result = {
                'success': False,
                'error': f'Unknown command: {args.command}',
                'available_commands': [
                    'initialize',
                    'build-spreadsheet',
                    'get-available-items',
                    'get-session-info',
                    'cleanup-sessions',
                    'status',
                    'statistics'
                ]
            }
            
    except Exception as e:
        result = {
            'success': False,
            'error': str(e)
        }
    
    # Output result as JSON
    print(json.dumps(result, indent=2, default=str))

if __name__ == "__main__":
    main()
