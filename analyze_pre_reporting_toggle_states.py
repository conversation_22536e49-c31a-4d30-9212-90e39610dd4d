#!/usr/bin/env python3
"""
Comprehensive Analysis of Pre-Reporting Toggle States vs Actual Data
Analyzes the change detection and include/exclude toggle states and compares
them with the actual data loaded in the interactive pre-reporting UI.
"""

import sqlite3
import json
from pathlib import Path

def analyze_toggle_states_vs_data():
    """Analyze toggle states and compare with pre-reporting data"""
    
    print("🔍 ANALYZING PRE-REPORTING TOGGLE STATES VS ACTUAL DATA")
    print("=" * 70)
    
    # Connect to database
    db_path = Path("payroll_audit.db")
    if not db_path.exists():
        print("❌ Database not found!")
        return
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    try:
        # 1. Check available tables and get current session
        print("\n1. 📊 CHECKING DATABASE STRUCTURE:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"   Available tables: {[t[0] for t in tables]}")

        # Try different session table names
        current_session = None
        session_tables = ['audit_sessions', 'sessions', 'comparison_sessions']

        for table_name in session_tables:
            try:
                cursor.execute(f"SELECT DISTINCT session_id FROM comparison_results ORDER BY session_id DESC LIMIT 1")
                session_result = cursor.fetchone()
                if session_result:
                    current_session = session_result[0]
                    print(f"   ✅ Found session from comparison_results: {current_session}")
                    break
            except:
                continue

        if not current_session:
            # Try to get any session from comparison_results
            try:
                cursor.execute("SELECT DISTINCT session_id FROM comparison_results LIMIT 1")
                session_result = cursor.fetchone()
                if session_result:
                    current_session = session_result[0]
                    print(f"   ✅ Using available session: {current_session}")
                else:
                    print("   ❌ No sessions found in comparison_results")
                    return
            except Exception as e:
                print(f"   ❌ Error finding sessions: {e}")
                return
        
        # 2. Analyze Change Detection Toggle States
        print("\n2. 🔧 CHANGE DETECTION TOGGLE STATES:")
        cursor.execute("""
            SELECT ds.section_name, di.item_name, 
                   di.include_new, di.include_increase, di.include_decrease,
                   di.include_removed, di.include_no_change, di.include_in_report
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            ORDER BY ds.section_name, di.item_name
        """)
        
        toggle_states = cursor.fetchall()
        
        print(f"   Total dictionary items: {len(toggle_states)}")
        
        # Group by section and analyze
        sections_analysis = {}
        for row in toggle_states:
            section, item, new, inc, dec, rem, no_change, include = row
            
            if section not in sections_analysis:
                sections_analysis[section] = {
                    'items': [],
                    'high_priority_items': 0,
                    'moderate_priority_items': 0,
                    'excluded_items': 0,
                    'change_detection_disabled': 0
                }
            
            # Determine priority based on section
            priority = 'HIGH' if section.upper() in ['PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'BANK DETAILS'] else \
                      'MODERATE' if section.upper() in ['LOANS'] else 'LOW'
            
            if priority == 'HIGH':
                sections_analysis[section]['high_priority_items'] += 1
            elif priority == 'MODERATE':
                sections_analysis[section]['moderate_priority_items'] += 1
            
            # Check if excluded from reports
            if include == 0:
                sections_analysis[section]['excluded_items'] += 1
            
            # Check if all change detection is disabled
            if not any([new, inc, dec, rem, no_change]):
                sections_analysis[section]['change_detection_disabled'] += 1
            
            sections_analysis[section]['items'].append({
                'item': item,
                'priority': priority,
                'toggles': {
                    'NEW': bool(new),
                    'INCREASE': bool(inc),
                    'DECREASE': bool(dec),
                    'REMOVED': bool(rem),
                    'NO_CHANGE': bool(no_change)
                },
                'include_in_report': bool(include)
            })
        
        # Print section analysis
        for section, analysis in sections_analysis.items():
            print(f"\n   📁 {section}:")
            print(f"      Total items: {len(analysis['items'])}")
            print(f"      High priority: {analysis['high_priority_items']}")
            print(f"      Moderate priority: {analysis['moderate_priority_items']}")
            print(f"      Excluded from reports: {analysis['excluded_items']}")
            print(f"      Change detection disabled: {analysis['change_detection_disabled']}")
        
        # 3. Check table structures first
        print("\n3. 📊 CHECKING TABLE STRUCTURES:")

        # Check comparison_results structure
        cursor.execute("PRAGMA table_info(comparison_results)")
        cr_columns = cursor.fetchall()
        print(f"   comparison_results columns: {[col[1] for col in cr_columns]}")

        # Check pre_reporting_results structure
        cursor.execute("PRAGMA table_info(pre_reporting_results)")
        pr_columns = cursor.fetchall()
        print(f"   pre_reporting_results columns: {[col[1] for col in pr_columns]}")

        # 4. Analyze Actual Pre-Reporting Data
        print("\n4. 📊 ACTUAL PRE-REPORTING DATA ANALYSIS:")

        # Get pre-reporting data (adjusted query based on actual columns)
        cursor.execute("""
            SELECT cr.id, cr.employee_id, cr.employee_name, cr.section, cr.item_label,
                   cr.previous_value, cr.current_value, cr.change_type, cr.priority_level,
                   pr.bulk_category, pr.is_selected
            FROM comparison_results cr
            LEFT JOIN pre_reporting_results pr ON cr.id = pr.change_id
            WHERE cr.session_id = ?
            ORDER BY cr.priority_level DESC, cr.section, cr.employee_id
        """, (current_session,))
        
        pre_reporting_data = cursor.fetchall()
        print(f"   Total pre-reporting records: {len(pre_reporting_data)}")
        
        # Analyze by section and priority
        data_analysis = {}
        for row in pre_reporting_data:
            change_id, emp_id, emp_name, section, item, prev_val, curr_val, change_type, priority, bulk_cat, selected = row

            # Handle None values
            section = section or 'UNKNOWN'
            item = item or 'UNKNOWN'
            priority = priority or 'UNKNOWN'
            
            if section not in data_analysis:
                data_analysis[section] = {
                    'total_changes': 0,
                    'high_priority': 0,
                    'moderate_priority': 0,
                    'low_priority': 0,
                    'change_types': {},
                    'items': set()
                }
            
            data_analysis[section]['total_changes'] += 1
            data_analysis[section]['items'].add(item)
            
            # Count by priority
            if priority and priority.upper() == 'HIGH':
                data_analysis[section]['high_priority'] += 1
            elif priority and priority.upper() in ['MODERATE', 'MEDIUM']:
                data_analysis[section]['moderate_priority'] += 1
            else:
                data_analysis[section]['low_priority'] += 1
            
            # Count by change type
            if change_type not in data_analysis[section]['change_types']:
                data_analysis[section]['change_types'][change_type] = 0
            data_analysis[section]['change_types'][change_type] += 1
        
        # Print data analysis
        for section, analysis in data_analysis.items():
            print(f"\n   📊 {section}:")
            print(f"      Total changes: {analysis['total_changes']}")
            print(f"      Unique items: {len(analysis['items'])}")
            print(f"      High priority: {analysis['high_priority']}")
            print(f"      Moderate priority: {analysis['moderate_priority']}")
            print(f"      Low priority: {analysis['low_priority']}")
            print(f"      Change types: {analysis['change_types']}")
        
        # 4. Compare Toggle States vs Actual Data
        print("\n4. ⚖️ COMPARISON: TOGGLE STATES VS ACTUAL DATA:")
        
        discrepancies = []
        
        for section in sections_analysis.keys():
            toggle_info = sections_analysis[section]
            data_info = data_analysis.get(section, {'total_changes': 0, 'high_priority': 0, 'moderate_priority': 0})
            
            print(f"\n   🔍 {section}:")
            
            # Check if high priority items are missing
            if toggle_info['high_priority_items'] > 0 and data_info['high_priority'] == 0:
                discrepancy = f"HIGH PRIORITY MISSING: {section} has {toggle_info['high_priority_items']} high priority items in dictionary but 0 in pre-reporting data"
                print(f"      ❌ {discrepancy}")
                discrepancies.append(discrepancy)
            
            # Check if excluded items are appearing
            if toggle_info['excluded_items'] > 0 and data_info['total_changes'] > 0:
                discrepancy = f"EXCLUDED ITEMS APPEARING: {section} has {toggle_info['excluded_items']} excluded items but {data_info['total_changes']} changes in pre-reporting"
                print(f"      ⚠️ {discrepancy}")
                discrepancies.append(discrepancy)
            
            # Check if change detection disabled items are appearing
            if toggle_info['change_detection_disabled'] > 0 and data_info['total_changes'] > 0:
                discrepancy = f"CHANGE DETECTION DISABLED: {section} has {toggle_info['change_detection_disabled']} items with disabled change detection but {data_info['total_changes']} changes"
                print(f"      ⚠️ {discrepancy}")
                discrepancies.append(discrepancy)
            
            # Positive checks
            if data_info['high_priority'] > 0:
                print(f"      ✅ High priority data present: {data_info['high_priority']} changes")
            if data_info['moderate_priority'] > 0:
                print(f"      ✅ Moderate priority data present: {data_info['moderate_priority']} changes")
        
        # 5. Specific Analysis for User's Issue
        print("\n5. 🎯 SPECIFIC ANALYSIS FOR USER'S ISSUE:")
        print("   User expected HIGH priority items but only saw LOANS (MODERATE priority)")
        
        # Check high priority sections
        high_priority_sections = ['PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'BANK DETAILS']
        loans_data = data_analysis.get('LOANS', {})
        
        print(f"\n   📊 LOANS section analysis:")
        print(f"      Total changes: {loans_data.get('total_changes', 0)}")
        print(f"      Moderate priority: {loans_data.get('moderate_priority', 0)}")
        print(f"      Change types: {loans_data.get('change_types', {})}")
        
        high_priority_total = 0
        for section in high_priority_sections:
            section_data = data_analysis.get(section, {})
            high_priority_count = section_data.get('high_priority', 0)
            high_priority_total += high_priority_count
            print(f"   📊 {section}: {high_priority_count} high priority changes")
        
        print(f"\n   📈 SUMMARY:")
        print(f"      Total HIGH priority changes: {high_priority_total}")
        print(f"      Total LOANS (MODERATE) changes: {loans_data.get('total_changes', 0)}")
        
        if high_priority_total == 0 and loans_data.get('total_changes', 0) > 0:
            print("   🚨 ISSUE CONFIRMED: No high priority items in pre-reporting, only moderate priority LOANS")
            discrepancies.append("PRIORITY FILTERING ISSUE: Expected high priority items missing, only moderate priority LOANS present")
        
        # 6. Final Summary
        print("\n6. 📋 FINAL SUMMARY:")
        
        if discrepancies:
            print("   ❌ DISCREPANCIES FOUND:")
            for i, discrepancy in enumerate(discrepancies, 1):
                print(f"      {i}. {discrepancy}")
        else:
            print("   ✅ No major discrepancies found between toggle states and data")
        
        # 7. Recommendations
        print("\n7. 💡 RECOMMENDATIONS:")
        
        if high_priority_total == 0:
            print("   🔧 Check if high priority sections have data in comparison_results")
            print("   🔧 Verify that priority assignment is working correctly")
            print("   🔧 Check if include_in_report filtering is too restrictive")
        
        if any("EXCLUDED ITEMS APPEARING" in d for d in discrepancies):
            print("   🔧 Pre-reporting queries may not be applying include_in_report filtering")
        
        if any("CHANGE DETECTION DISABLED" in d for d in discrepancies):
            print("   🔧 Change detection toggle states may not be enforced in pre-reporting")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

if __name__ == "__main__":
    analyze_toggle_states_vs_data()
