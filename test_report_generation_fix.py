#!/usr/bin/env python3
"""
Test Report Generation Fix
Quick test to verify the report generation fix is working
"""

import sys
import os
import sqlite3
from pathlib import Path

def test_report_generation_fix():
    """Test the fixed report generation"""
    
    print("🧪 TESTING REPORT GENERATION FIX")
    print("=" * 40)
    
    try:
        # 1. Test the fixed query directly
        print("\n1. 🔍 TESTING FIXED QUERY:")
        
        db_path = Path("payroll_audit.db")
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY session_id DESC LIMIT 1")
        session_result = cursor.fetchone()
        current_session = session_result[0]
        
        print(f"   Session: {current_session}")
        
        # Test the fixed query
        fixed_query = '''SELECT cr.id, cr.employee_id, cr.employee_name, cr.section, cr.item_label,
                                cr.previous_value, cr.current_value, cr.change_type, cr.priority_level,
                                0 as numeric_difference,
                                0 as percentage_change,
                                COALESCE(pr.bulk_category, 'INDIVIDUAL') as bulk_category,
                                COALESCE(pr.bulk_size, 1) as bulk_size
                         FROM comparison_results cr
                         JOIN pre_reporting_results pr ON cr.id = pr.change_id
                         WHERE cr.session_id = ? AND pr.selected_for_report = 1
                         ORDER BY cr.priority_level DESC, cr.section, cr.employee_id'''
        
        cursor.execute(fixed_query, (current_session,))
        results = cursor.fetchall()
        
        print(f"   Fixed query results: {len(results)} records")
        
        if results:
            print("   ✅ Query is now returning results!")
            for i, row in enumerate(results[:3]):  # Show first 3
                print(f"     {i+1}. {row[3]}.{row[4]} ({row[7]}) - {row[2]}")
        else:
            print("   ❌ Query still returning 0 results")
            return False
        
        conn.close()
        
        # 2. Test the PhasedProcessManager with the fix
        print("\n2. 🔧 TESTING PHASED PROCESS MANAGER:")
        
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        # Suppress stdout to avoid debug messages
        import contextlib
        import io
        
        with contextlib.redirect_stdout(io.StringIO()):
            manager = PhasedProcessManager(debug_mode=False)
            result = manager.generate_final_reports(current_session)
        
        print(f"   Report generation result: {result.get('success', False)}")
        
        if result.get('success'):
            print("   ✅ Report generation API working!")
            
            # Check if reports were actually generated
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM generated_reports WHERE session_id = ?", (current_session,))
            report_count = cursor.fetchone()[0]
            
            print(f"   Generated reports in database: {report_count}")
            
            if report_count > 0:
                cursor.execute("""
                    SELECT report_type, file_path, file_size 
                    FROM generated_reports 
                    WHERE session_id = ? 
                    ORDER BY id DESC
                """, (current_session,))
                
                reports = cursor.fetchall()
                print("   📄 Generated reports:")
                for report_type, file_path, file_size in reports:
                    exists = "✅" if os.path.exists(file_path) else "❌"
                    print(f"     {exists} {report_type}: {file_path} ({file_size} bytes)")
                
                conn.close()
                return True
            else:
                print("   ⚠️ API succeeded but no reports in database")
                conn.close()
                return False
        else:
            print(f"   ❌ Report generation failed: {result.get('error')}")
            return False
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    success = test_report_generation_fix()
    
    if success:
        print("\n🎉 REPORT GENERATION FIX: SUCCESSFUL!")
        print("   The Generate Report buttons should now work correctly.")
    else:
        print("\n❌ REPORT GENERATION FIX: FAILED!")
        print("   Additional debugging needed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
