#!/usr/bin/env python3
"""Reset session state to clear any stuck workflows"""

import sqlite3
import os
from datetime import datetime

def reset_session_state():
    """Reset the current session state to allow fresh workflow start"""
    print("🔄 RESETTING SESSION STATE")
    print("=" * 40)
    
    try:
        db_path = 'data/templar_payroll_auditor.db'
        if not os.path.exists(db_path):
            print("❌ Database not found")
            return False
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session info
        print("1. 📋 CHECKING CURRENT SESSION:")
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session_result = cursor.fetchone()
        
        if session_result:
            session_id = session_result[0]
            print(f"   Current session: {session_id}")
            
            # Check data counts
            cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session_id,))
            extracted_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            comparison_count = cursor.fetchone()[0]
            
            print(f"   Extracted data: {extracted_count} records")
            print(f"   Comparison data: {comparison_count} records")
            
        else:
            print("   ❌ No current session found")
            return True  # Nothing to reset
        
        # 2. Reset session status
        print("\n2. 🔄 RESETTING SESSION STATUS:")
        cursor.execute('''
            UPDATE audit_sessions 
            SET status = 'ready_for_restart' 
            WHERE session_id = ?
        ''', (session_id,))
        print("   ✅ Session status reset to 'ready_for_restart'")
        
        # 3. Reset all phase statuses to NOT_STARTED except extraction if it has data
        print("\n3. 🔄 RESETTING PHASE STATUSES:")
        
        if extracted_count > 0:
            # Keep extraction as completed since we have data
            cursor.execute('''
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = ?, data_count = ?
                WHERE session_id = ? AND phase_name = 'EXTRACTION'
            ''', (datetime.now().isoformat(), extracted_count, session_id))
            print(f"   ✅ EXTRACTION kept as COMPLETED ({extracted_count} records)")
            
            # Reset other phases
            other_phases = ['COMPARISON', 'AUTO_LEARNING', 'TRACKER_FEEDING', 'PRE_REPORTING', 'REPORT_GENERATION']
            for phase in other_phases:
                cursor.execute('''
                    UPDATE session_phases 
                    SET status = 'NOT_STARTED', started_at = NULL, completed_at = NULL, data_count = 0
                    WHERE session_id = ? AND phase_name = ?
                ''', (session_id, phase))
                print(f"   🔄 {phase} reset to NOT_STARTED")
        else:
            # Reset all phases if no extraction data
            all_phases = ['EXTRACTION', 'COMPARISON', 'AUTO_LEARNING', 'TRACKER_FEEDING', 'PRE_REPORTING', 'REPORT_GENERATION']
            for phase in all_phases:
                cursor.execute('''
                    UPDATE session_phases 
                    SET status = 'NOT_STARTED', started_at = NULL, completed_at = NULL, data_count = 0
                    WHERE session_id = ? AND phase_name = ?
                ''', (session_id, phase))
                print(f"   🔄 {phase} reset to NOT_STARTED")
        
        # 4. Clear any process locks
        print("\n4. 🔓 CLEARING PROCESS LOCKS:")
        try:
            cursor.execute('DELETE FROM session_locks WHERE session_id = ?', (session_id,))
            print("   ✅ Process locks cleared")
        except sqlite3.OperationalError:
            print("   ℹ️ No session_locks table (normal)")
        
        # 5. Clean up any lock files
        print("\n5. 🧹 CLEANING UP LOCK FILES:")
        lock_dir = 'data/.db_locks'
        if os.path.exists(lock_dir):
            lock_file = os.path.join(lock_dir, 'database.lock')
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    print("   ✅ Database lock file removed")
                except:
                    print("   ⚠️ Could not remove lock file (may be in use)")
            else:
                print("   ✅ No lock file found")
        else:
            print("   ✅ No lock directory found")
        
        # 6. Update current session status
        print("\n6. 📋 UPDATING CURRENT SESSION:")
        cursor.execute('''
            UPDATE current_session 
            SET status = 'READY', updated_at = datetime('now')
            WHERE id = 1
        ''', )
        print("   ✅ Current session marked as READY")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        print("\n🎉 SUCCESS: Session state reset completed!")
        print("📋 The system is now ready for a fresh audit workflow")
        print("💡 You can now start a new audit job from the UI")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resetting session state: {e}")
        return False

if __name__ == "__main__":
    reset_session_state()
