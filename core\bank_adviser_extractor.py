#!/usr/bin/env python3
"""
BANK ADVISER EXTRACTOR
Dedicated extractor for Bank Adviser Module based on Perfect Section-Aware Extractor
Handles FINAL ADJUSTED PAYSLIP, ALLOWANCES PAYSLIP, and AWARDS PAYSLIP processing
"""

import sys
import os
import json
import fitz  # PyMuPDF
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# Add the parent directory to the path to import the perfect extractor
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from perfect_section_aware_extractor import PerfectSectionAwareExtractor

@dataclass
class BankAdviserExtractionResult:
    """Data structure for Bank Adviser extraction results"""
    document_type: str
    employee_no: str
    employee_name: str
    department: str
    section: str = ""
    job_title: str = ""

    # Final Adjusted Payslip specific
    net_pay: float = 0.0
    bank: str = ""
    account_no: str = ""
    branch: str = ""

    # Allowances specific
    allowance_type: str = ""
    allowance_gross_amount: float = 0.0
    allowance_payable_amount: float = 0.0

    # Awards specific
    award_type: str = ""
    award_gross_amount: float = 0.0
    award_payable_amount: float = 0.0

    # Extraction metadata
    extraction_confidence: float = 1.0
    extraction_source: str = "Bank Adviser Extractor"
    extraction_timestamp: str = ""

class BankAdviserExtractor:
    """
    Bank Adviser Extractor - Specialized for Bank Adviser Module documents
    Based on Perfect Section-Aware Extractor but focused on specific data requirements
    """

    def __init__(self, debug: bool = False):
        self.debug = debug
        self.perfect_extractor = PerfectSectionAwareExtractor(debug=debug)

        # Tax calculation settings (can be overridden)
        self.allowance_tax_rate = 5.0  # 5% for allowances (especially EDUCATIONAL SUBSIDY)
        self.awards_tax_rate = 7.5     # 7.5% for all awards

        print("🏦 BANK ADVISER EXTRACTOR INITIALIZED")
        if self.debug:
            print(f"   📊 Allowance Tax Rate: {self.allowance_tax_rate}%")
            print(f"   📊 Awards Tax Rate: {self.awards_tax_rate}%")

    def extract_final_adjusted_payslip(self, pdf_path: str, page_num: int = 1) -> List[BankAdviserExtractionResult]:
        """
        Extract data from FINAL ADJUSTED PAYSLIP
        Focus: Employee No., Employee Name, NET PAY, Bank, Account No., Branch, Department, Section
        """
        print(f"🏦 [FINAL ADJUSTED] Extracting from: {pdf_path} Page {page_num}")

        try:
            # Use Perfect Section-Aware Extractor as base
            raw_data = self.perfect_extractor.extract_perfect(pdf_path, page_num)

            if 'error' in raw_data:
                print(f"❌ Perfect extractor returned error: {raw_data.get('error', 'Unknown error')}")
                return [BankAdviserExtractionResult(
                    document_type="final_adjusted",
                    employee_no="",
                    employee_name="",
                    department="",
                    extraction_confidence=0.0,
                    extraction_timestamp=datetime.now().isoformat()
                )]

            # Validate required fields
            required_fields = ['EMPLOYEE NO.', 'EMPLOYEE NAME', 'NET PAY']
            missing_fields = [field for field in required_fields if not raw_data.get(field)]

            if missing_fields:
                print(f"⚠️ Missing required fields: {missing_fields}")
                # Still proceed but with lower confidence
                confidence = 0.7
            else:
                confidence = 1.0

            # Extract required fields for Final Adjusted Payslip
            result = BankAdviserExtractionResult(
                document_type="final_adjusted",
                employee_no=raw_data.get('EMPLOYEE NO.', ''),
                employee_name=raw_data.get('EMPLOYEE NAME', ''),
                department=raw_data.get('DEPARTMENT', ''),
                section=raw_data.get('SECTION', ''),
                job_title=raw_data.get('JOB TITLE', ''),
                net_pay=self._parse_amount(raw_data.get('NET PAY', '0')),
                bank=raw_data.get('BANK', ''),
                account_no=raw_data.get('ACCOUNT NO.', ''),
                branch=raw_data.get('BRANCH', ''),
                extraction_confidence=confidence,
                extraction_source="Bank Adviser Extractor - Final Adjusted",
                extraction_timestamp=datetime.now().isoformat()
            )

            if self.debug:
                print(f"   ✅ Extracted: {result.employee_no} - {result.employee_name}")
                print(f"   💰 NET PAY: {result.net_pay}")
                print(f"   🏛️ BANK: {result.bank}")
                print(f"   📍 SECTION: {result.section}")
                print(f"   🎯 CONFIDENCE: {confidence}")

            return [result]

        except Exception as e:
            print(f"❌ Final Adjusted extraction failed: {e}")
            return [BankAdviserExtractionResult(
                document_type="final_adjusted",
                employee_no="",
                employee_name="",
                department="",
                extraction_confidence=0.0,
                extraction_timestamp=datetime.now().isoformat()
            )]

    def extract_allowances_payslip(self, pdf_path: str, page_num: int = 1, tax_rate: float = None) -> List[BankAdviserExtractionResult]:
        """
        Extract data from ALLOWANCES PAYSLIP
        Focus: Allowance type (from Earnings), Payable Amount, Employee No., Employee Name, Department
        Apply tax calculation (5% for EDUCATIONAL SUBSIDY by default)
        """
        print(f"🎁 [ALLOWANCES] Extracting from: {pdf_path} Page {page_num}")

        if tax_rate is not None:
            self.allowance_tax_rate = tax_rate

        try:
            # Use Perfect Section-Aware Extractor as base
            raw_data = self.perfect_extractor.extract_perfect(pdf_path, page_num)

            if 'error' in raw_data:
                return []

            results = []

            # Look for allowance types in EARNINGS section
            allowance_items = self._find_allowance_items(raw_data)

            for allowance_type, gross_amount in allowance_items:
                # Calculate tax and payable amount
                tax_amount = gross_amount * (self.allowance_tax_rate / 100)
                payable_amount = gross_amount - tax_amount

                result = BankAdviserExtractionResult(
                    document_type="allowances",
                    employee_no=raw_data.get('EMPLOYEE NO.', ''),
                    employee_name=raw_data.get('EMPLOYEE NAME', ''),
                    department=raw_data.get('DEPARTMENT', ''),
                    section=raw_data.get('SECTION', ''),
                    allowance_type=allowance_type,
                    allowance_gross_amount=gross_amount,
                    allowance_payable_amount=round(payable_amount, 2),
                    extraction_confidence=1.0,
                    extraction_source="Bank Adviser Extractor - Allowances",
                    extraction_timestamp=datetime.now().isoformat()
                )

                results.append(result)

                if self.debug:
                    print(f"   ✅ Allowance: {allowance_type}")
                    print(f"   💰 Gross: {gross_amount}, Tax: {tax_amount:.2f}, Payable: {payable_amount:.2f}")

            return results

        except Exception as e:
            print(f"❌ Allowances extraction failed: {e}")
            return []

    def extract_awards_payslip(self, pdf_path: str, page_num: int = 1, tax_rate: float = None) -> List[BankAdviserExtractionResult]:
        """
        Extract data from AWARDS PAYSLIP
        Focus: Award type (from Earnings), Payable Amount, Employee No., Employee Name, Department, NET PAY
        Apply tax calculation (7.5% for all awards by default)
        """
        print(f"🏆 [AWARDS] Extracting from: {pdf_path} Page {page_num}")

        if tax_rate is not None:
            self.awards_tax_rate = tax_rate

        try:
            # Use Perfect Section-Aware Extractor as base
            raw_data = self.perfect_extractor.extract_perfect(pdf_path, page_num)

            if 'error' in raw_data:
                return []

            results = []

            # Look for award types in EARNINGS section
            award_items = self._find_award_items(raw_data)

            for award_type, gross_amount in award_items:
                # Calculate tax and payable amount
                tax_amount = gross_amount * (self.awards_tax_rate / 100)
                payable_amount = gross_amount - tax_amount

                result = BankAdviserExtractionResult(
                    document_type="awards",
                    employee_no=raw_data.get('EMPLOYEE NO.', ''),
                    employee_name=raw_data.get('EMPLOYEE NAME', ''),
                    department=raw_data.get('DEPARTMENT', ''),
                    section=raw_data.get('SECTION', ''),
                    net_pay=self._parse_amount(raw_data.get('NET PAY', '0')),
                    award_type=award_type,
                    award_gross_amount=gross_amount,
                    award_payable_amount=round(payable_amount, 2),
                    extraction_confidence=1.0,
                    extraction_source="Bank Adviser Extractor - Awards",
                    extraction_timestamp=datetime.now().isoformat()
                )

                results.append(result)

                if self.debug:
                    print(f"   ✅ Award: {award_type}")
                    print(f"   💰 Gross: {gross_amount}, Tax: {tax_amount:.2f}, Payable: {payable_amount:.2f}")

            return results

        except Exception as e:
            print(f"❌ Awards extraction failed: {e}")
            return []

    def _find_allowance_items(self, raw_data: Dict) -> List[Tuple[str, float]]:
        """Find allowance items from extracted data with enhanced detection"""
        allowance_items = []

        # Enhanced allowance patterns with priority
        allowance_patterns = [
            # High priority - exact matches
            ('EDUCATIONAL SUBSIDY', 1.0),
            ('LEAVE ALLOWANCE', 1.0),
            ('LEAVE CLAIMS', 1.0),
            ('TRANSPORT ALLOWANCE', 1.0),
            ('HOUSING ALLOWANCE', 1.0),
            ('MEDICAL ALLOWANCE', 1.0),

            # Medium priority - partial matches
            ('SUBSIDY', 0.8),
            ('ALLOWANCE', 0.7),
            ('CLAIMS', 0.7),

            # Low priority - general patterns
            ('LEAVE', 0.5),
            ('TRANSPORT', 0.5),
            ('HOUSING', 0.5),
            ('MEDICAL', 0.5)
        ]

        # Check each extracted item
        for key, value in raw_data.items():
            key_upper = key.upper().strip()

            # Skip if this looks like a deduction or other non-allowance item
            if any(exclude in key_upper for exclude in ['TAX', 'DEDUCTION', 'SSF', 'LOAN', 'ADVANCE']):
                continue

            # Find best matching pattern
            best_match = None
            best_confidence = 0

            for pattern, confidence in allowance_patterns:
                if pattern in key_upper:
                    if confidence > best_confidence:
                        best_match = pattern
                        best_confidence = confidence

            if best_match and best_confidence >= 0.5:
                amount = self._parse_amount(value)
                if amount > 0:
                    allowance_items.append((key, amount))
                    if self.debug:
                        print(f"   🎁 Found allowance: {key} = {amount} (confidence: {best_confidence})")

        return allowance_items

    def _find_award_items(self, raw_data: Dict) -> List[Tuple[str, float]]:
        """Find award items from extracted data"""
        award_items = []

        # Look for award patterns in the extracted data
        award_keywords = [
            'LONG SERVICE AWARD', 'SERVICE AWARD', 'AWARD',
            'RECOGNITION', 'BONUS'
        ]

        for key, value in raw_data.items():
            key_upper = key.upper()

            # Check if this is an award item
            if any(keyword in key_upper for keyword in award_keywords):
                amount = self._parse_amount(value)
                if amount > 0:
                    award_items.append((key, amount))

        return award_items

    def _parse_amount(self, amount_str: str) -> float:
        """Parse amount string to float"""
        if not amount_str:
            return 0.0

        try:
            # Remove common currency symbols and formatting
            cleaned = str(amount_str).replace(',', '').replace('GHS', '').replace('₵', '').strip()
            return float(cleaned)
        except (ValueError, TypeError):
            return 0.0

    def set_tax_rates(self, allowance_rate: float = None, awards_rate: float = None):
        """Set custom tax rates"""
        if allowance_rate is not None:
            self.allowance_tax_rate = allowance_rate
            print(f"📊 Allowance tax rate set to: {allowance_rate}%")

        if awards_rate is not None:
            self.awards_tax_rate = awards_rate
            print(f"📊 Awards tax rate set to: {awards_rate}%")

def main():
    """Command line interface for Bank Adviser Extractor"""
    import sys
    import json

    if len(sys.argv) < 3:
        print("Usage: python bank_adviser_extractor.py <document_type> <pdf_path> [tax_rate]")
        print("Document types: final_adjusted, allowances, awards")
        sys.exit(1)

    document_type = sys.argv[1]
    pdf_path = sys.argv[2]
    tax_rate = float(sys.argv[3]) if len(sys.argv) > 3 else None

    extractor = BankAdviserExtractor(debug=False)

    try:
        if document_type == 'final_adjusted':
            results = extractor.extract_final_adjusted_payslip(pdf_path)
        elif document_type == 'allowances':
            if tax_rate:
                extractor.set_tax_rates(allowance_rate=tax_rate)
            results = extractor.extract_allowances_payslip(pdf_path)
        elif document_type == 'awards':
            if tax_rate:
                extractor.set_tax_rates(awards_rate=tax_rate)
            results = extractor.extract_awards_payslip(pdf_path)
        else:
            raise ValueError(f"Unknown document type: {document_type}")

        # Convert results to JSON-serializable format
        json_results = []
        for result in results:
            json_results.append({
                'document_type': result.document_type,
                'employee_no': result.employee_no,
                'employee_name': result.employee_name,
                'department': result.department,
                'section': result.section,
                'job_title': result.job_title,
                'net_pay': result.net_pay,
                'bank': result.bank,
                'account_no': result.account_no,
                'branch': result.branch,
                'allowance_type': result.allowance_type,
                'allowance_gross_amount': result.allowance_gross_amount,
                'allowance_payable_amount': result.allowance_payable_amount,
                'award_type': result.award_type,
                'award_gross_amount': result.award_gross_amount,
                'award_payable_amount': result.award_payable_amount,
                'extraction_confidence': result.extraction_confidence,
                'extraction_source': result.extraction_source,
                'extraction_timestamp': result.extraction_timestamp
            })

        print(json.dumps(json_results, indent=2))

    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'document_type': document_type,
            'pdf_path': pdf_path
        }
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    main()
