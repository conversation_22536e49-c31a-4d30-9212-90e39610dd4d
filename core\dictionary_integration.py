"""
Dictionary Integration Module

This module provides integration between the enhanced payslip dictionary
and the extraction engine. It replaces the old dictionary standardization
with the new comprehensive dictionary functionality.
"""

import os
import json
import re
from core.clean_payroll_dictionaries import load_dictionary

# Global dictionary cache
_dictionary_cache = None

def find_item_by_variation(section_name, item_text):
    """
    Find an item by checking variations in the dictionary.

    Args:
        section_name: The section to look in
        item_text: The item text to find

    Returns:
        The standardized item name or None if not found
    """
    dictionary = get_dictionary()

    if section_name not in dictionary:
        return None

    section_items = dictionary[section_name].get("items", {})

    # First check exact match
    if item_text in section_items:
        return item_text

    # Check variations
    for item_name, item_data in section_items.items():
        variations = item_data.get("variations", [])
        if item_text in variations:
            return item_name

    return None

def get_standardized_name(section_name, item_text):
    """
    Get the standardized name for an item.

    Args:
        section_name: The section to look in
        item_text: The item text to standardize

    Returns:
        The standardized item name or the original if no match is found
    """
    found_item = find_item_by_variation(section_name, item_text)
    return found_item if found_item else item_text

def get_dictionary():
    """
    Get the dictionary, loading it from disk if necessary.

    Returns:
        The dictionary object
    """
    global _dictionary_cache
    if _dictionary_cache is None:
        _dictionary_cache = load_dictionary()
    return _dictionary_cache

def reload_dictionary():
    """
    Force a reload of the dictionary from disk.

    Returns:
        The reloaded dictionary object
    """
    global _dictionary_cache
    _dictionary_cache = load_dictionary()
    return _dictionary_cache

def standardize_item(section_name, item_text):
    """
    Standardize an item name using the dictionary.

    Args:
        section_name: The section to look in (e.g., "EARNINGS", "DEDUCTIONS")
        item_text: The item text to standardize

    Returns:
        The standardized item name or the original if no match is found
    """
    dictionary = get_dictionary()

    # Check if the section exists in the dictionary
    if section_name not in dictionary:
        return item_text

    # Get the standardized name
    return get_standardized_name(section_name, item_text)

def standardize_earnings(earnings_dict):
    """
    Standardize earnings dictionary keys using the dictionary.

    Args:
        earnings_dict: Dictionary of earnings items

    Returns:
        Dictionary with standardized keys
    """
    standardized = {}

    for key, value in earnings_dict.items():
        std_key = standardize_item("EARNINGS", key)
        standardized[std_key] = value

    return standardized

def standardize_deductions(deductions_dict):
    """
    Standardize deductions dictionary keys using the dictionary.

    Args:
        deductions_dict: Dictionary of deduction items

    Returns:
        Dictionary with standardized keys
    """
    standardized = {}

    for key, value in deductions_dict.items():
        std_key = standardize_item("DEDUCTIONS", key)
        standardized[std_key] = value

    return standardized

def standardize_loans(loans_dict):
    """
    Standardize loans dictionary keys using the dictionary.

    Args:
        loans_dict: Dictionary of loan items

    Returns:
        Dictionary with standardized keys
    """
    standardized = {}

    for key, value in loans_dict.items():
        std_key = standardize_item("LOANS", key)
        standardized[std_key] = value

    return standardized

def should_include_in_report(section_name, item_name):
    """
    Check if an item should be included in reports.

    Args:
        section_name: The section to look in (e.g., "EARNINGS", "DEDUCTIONS")
        item_name: The item name to check

    Returns:
        True if the item should be included, False otherwise
    """
    dictionary = get_dictionary()

    # Check if the section exists in the dictionary
    if section_name not in dictionary:
        return True  # Default to including if section not found

    # Find the item
    found_item_name = find_item_by_variation(section_name, item_name)

    # If item not found, default to including
    if not found_item_name:
        return True

    # Get the item data
    section_items = dictionary[section_name].get("items", {})
    item_data = section_items.get(found_item_name, {})

    # Return the include_in_report flag
    return item_data.get("include_in_report", True)

def get_value_format(section_name, item_name):
    """
    Get the value format for an item.

    Args:
        section_name: The section to look in (e.g., "EARNINGS", "DEDUCTIONS")
        item_name: The item name to check

    Returns:
        The value format string or None if not found
    """
    dictionary = get_dictionary()

    # Check if the section exists in the dictionary
    if section_name not in dictionary:
        return None

    # Find the item
    found_item_name = find_item_by_variation(section_name, item_name)

    # If item not found, return None
    if not found_item_name:
        return None

    # Get the item data
    section_items = dictionary[section_name].get("items", {})
    item_data = section_items.get(found_item_name, {})

    # Return the value format
    return item_data.get("value_format")
