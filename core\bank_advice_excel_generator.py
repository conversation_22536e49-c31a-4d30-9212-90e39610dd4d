#!/usr/bin/env python3
"""
BANK ADVICE EXCEL GENERATOR
Professional Excel generation for Church of Pentecost Bank Advice
Handles Final Adjusted, Allowances, and Awards with proper formatting
"""

import os
import sys
from datetime import datetime
from typing import List, Dict, Any
import json

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
    from openpyxl.utils import get_column_letter
    from openpyxl.worksheet.worksheet import Worksheet
except ImportError:
    print("❌ openpyxl not installed. Please install: pip install openpyxl")
    sys.exit(1)

class BankAdviceExcelGenerator:
    """
    Professional Excel generator for Church of Pentecost Bank Advice
    """

    def __init__(self):
        self.church_name = "THE CHURCH OF PENTECOST"
        self.header_font = Font(name='Arial', size=14, bold=True)
        self.subheader_font = Font(name='Arial', size=12, bold=True)
        self.normal_font = Font(name='Arial', size=10)
        self.small_font = Font(name='Arial', size=9)

        # Colors
        self.header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")  # Church blue
        self.pre_audited_fill = PatternFill(start_color="D5E8D4", end_color="D5E8D4", fill_type="solid")  # Light green
        self.total_fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")  # Light gray

        # Borders
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        print("[BANK ADVICE] Excel Generator Initialized")

    def generate_final_adjusted_bank_advice(self, session_data: Dict, output_path: str,
                                          selected_sections: List[str] = None,
                                          generation_type: str = "composite") -> Dict:
        """
        Generate Final Adjusted Payslip Bank Advice Excel
        """
        print(f"[BANK ADVICE] Generating Final Adjusted Bank Advice: {generation_type}")

        try:
            # Get data from database (simulated for now)
            employees_data = self._get_final_adjusted_data(session_data, selected_sections)

            if not employees_data:
                return {"success": False, "error": "No employee data found"}

            # Group by bank for bank advice format
            bank_groups = self._group_by_bank(employees_data)

            if generation_type == "composite":
                # Single Excel file with all banks
                result = self._create_composite_bank_advice(bank_groups, output_path, "Final Adjusted")
            else:
                # Separate Excel files per bank
                result = self._create_separate_bank_advice(bank_groups, output_path, "Final Adjusted")

            return result

        except Exception as e:
            print(f"❌ Final Adjusted Excel generation failed: {e}")
            return {"success": False, "error": str(e)}

    def generate_allowances_bank_advice(self, session_data: Dict, output_path: str,
                                      generation_type: str = "composite") -> Dict:
        """
        Generate Allowances Bank Advice Excel
        """
        print(f"[BANK ADVICE] Generating Allowances Bank Advice: {generation_type}")

        try:
            # Get allowances data from database
            allowances_data = self._get_allowances_data(session_data)

            if not allowances_data:
                return {"success": False, "error": "No allowances data found"}

            # Group by bank and allowance type
            bank_groups = self._group_allowances_by_bank(allowances_data)

            if generation_type == "composite":
                result = self._create_composite_allowances_advice(bank_groups, output_path)
            else:
                result = self._create_separate_allowances_advice(bank_groups, output_path)

            return result

        except Exception as e:
            print(f"❌ Allowances Excel generation failed: {e}")
            return {"success": False, "error": str(e)}

    def generate_awards_bank_advice(self, session_data: Dict, output_path: str,
                                  generation_type: str = "composite") -> Dict:
        """
        Generate Awards Bank Advice Excel
        """
        print(f"[BANK ADVICE] Generating Awards Bank Advice: {generation_type}")

        try:
            # Get awards data from database
            awards_data = self._get_awards_data(session_data)

            if not awards_data:
                return {"success": False, "error": "No awards data found"}

            # Group by bank and award type
            bank_groups = self._group_awards_by_bank(awards_data)

            if generation_type == "composite":
                result = self._create_composite_awards_advice(bank_groups, output_path)
            else:
                result = self._create_separate_awards_advice(bank_groups, output_path)

            return result

        except Exception as e:
            print(f"❌ Awards Excel generation failed: {e}")
            return {"success": False, "error": str(e)}

    def _create_composite_bank_advice(self, bank_groups: Dict, output_path: str, advice_type: str) -> Dict:
        """Create single Excel file with all banks"""
        wb = Workbook()
        wb.remove(wb.active)  # Remove default sheet

        total_employees = 0
        total_amount = 0.0

        for bank_name, employees in bank_groups.items():
            # Create sheet for each bank
            ws = wb.create_sheet(title=self._sanitize_sheet_name(bank_name))

            # Add Church of Pentecost header
            self._add_church_header(ws, advice_type, bank_name)

            # Add employee data table
            table_result = self._add_employee_table(ws, employees, advice_type)

            total_employees += len(employees)
            total_amount += table_result['total_amount']

            # Add PRE-AUDITED remark
            self._add_pre_audited_remark(ws, table_result['last_row'])

        # Save the workbook
        wb.save(output_path)

        return {
            "success": True,
            "file_path": output_path,
            "banks_count": len(bank_groups),
            "total_employees": total_employees,
            "total_amount": total_amount,
            "generation_type": "composite"
        }

    def _add_church_header(self, ws: Worksheet, advice_type: str, bank_name: str):
        """Add Church of Pentecost header to worksheet"""
        # Church name
        ws['A1'] = self.church_name
        ws['A1'].font = self.header_font
        ws['A1'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A1:F1')

        # Bank advice title
        ws['A3'] = f"{advice_type.upper()} BANK ADVICE"
        ws['A3'].font = self.subheader_font
        ws['A3'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A3:F3')

        # Bank name
        ws['A4'] = f"BANK: {bank_name}"
        ws['A4'].font = self.subheader_font

        # Date
        ws['E4'] = f"DATE: {datetime.now().strftime('%d/%m/%Y')}"
        ws['E4'].font = self.normal_font
        ws['E4'].alignment = Alignment(horizontal='right')

    def _add_employee_table(self, ws: Worksheet, employees: List[Dict], advice_type: str) -> Dict:
        """Add employee data table"""
        start_row = 6

        # Headers
        headers = ['S/N', 'EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT', 'ACCOUNT NO.', 'AMOUNT (GHS)']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.font = self.subheader_font
            cell.fill = self.header_fill
            cell.border = self.thin_border
            cell.alignment = Alignment(horizontal='center')

        # Employee data
        current_row = start_row + 1
        total_amount = 0.0

        for i, employee in enumerate(employees, 1):
            ws.cell(row=current_row, column=1, value=i).border = self.thin_border
            ws.cell(row=current_row, column=2, value=employee.get('employee_no', '')).border = self.thin_border
            ws.cell(row=current_row, column=3, value=employee.get('employee_name', '')).border = self.thin_border
            ws.cell(row=current_row, column=4, value=employee.get('department', '')).border = self.thin_border
            ws.cell(row=current_row, column=5, value=employee.get('account_no', '')).border = self.thin_border

            # Amount based on advice type
            if advice_type == "Final Adjusted":
                amount = employee.get('net_pay', 0.0)
            elif advice_type == "Allowances":
                amount = employee.get('payable_amount', 0.0)
            else:  # Awards
                amount = employee.get('payable_amount', 0.0)

            amount_cell = ws.cell(row=current_row, column=6, value=amount)
            amount_cell.border = self.thin_border
            amount_cell.number_format = '#,##0.00'

            total_amount += amount
            current_row += 1

        # Total row
        ws.cell(row=current_row, column=5, value="TOTAL:").font = self.subheader_font
        ws.cell(row=current_row, column=5).border = self.thin_border
        ws.cell(row=current_row, column=5).fill = self.total_fill

        total_cell = ws.cell(row=current_row, column=6, value=total_amount)
        total_cell.font = self.subheader_font
        total_cell.border = self.thin_border
        total_cell.fill = self.total_fill
        total_cell.number_format = '#,##0.00'

        # Auto-adjust column widths
        self._auto_adjust_columns(ws)

        return {"last_row": current_row, "total_amount": total_amount}

    def _add_pre_audited_remark(self, ws: Worksheet, last_row: int):
        """Add PRE-AUDITED remark with light green highlighting"""
        remark_row = last_row + 3

        ws.cell(row=remark_row, column=1, value="REMARK: PRE-AUDITED")
        ws.cell(row=remark_row, column=1).font = self.subheader_font
        ws.cell(row=remark_row, column=1).fill = self.pre_audited_fill
        ws.merge_cells(f'A{remark_row}:F{remark_row}')

    def _auto_adjust_columns(self, ws: Worksheet):
        """Auto-adjust column widths"""
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = min(max_length + 2, 50)  # Cap at 50
            ws.column_dimensions[column_letter].width = adjusted_width

    def _sanitize_sheet_name(self, name: str) -> str:
        """Sanitize sheet name for Excel compatibility"""
        # Remove invalid characters and limit length
        invalid_chars = ['\\', '/', '*', '[', ']', ':', '?']
        for char in invalid_chars:
            name = name.replace(char, '_')

        return name[:31]  # Excel sheet name limit

    def _group_by_bank(self, employees_data: List[Dict]) -> Dict:
        """Group employees by bank"""
        bank_groups = {}

        for employee in employees_data:
            bank = employee.get('bank', 'UNKNOWN BANK')
            if bank == '' or bank is None:
                bank = 'NO BANK SPECIFIED'

            if bank not in bank_groups:
                bank_groups[bank] = []

            bank_groups[bank].append(employee)

        return bank_groups

    def _get_final_adjusted_data(self, session_data: Dict, selected_sections: List[str] = None) -> List[Dict]:
        """Get Final Adjusted data from database"""
        try:
            import sqlite3
            import os

            # Connect to database
            db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'templar_payroll_auditor.db')
            if not os.path.exists(db_path):
                print(f"[ERROR] Database not found: {db_path}")
                return []

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            cursor = conn.cursor()

            # Get session ID from session_data
            session_id = session_data.get('session_id')
            if not session_id:
                print("[ERROR] No session_id provided")
                return []

            # Build query with optional section filtering
            base_query = """
                SELECT employee_no, employee_name, department, section, job_title,
                       net_pay, gross_salary, taxable_salary, total_deductions,
                       bank, account_no, branch, extraction_confidence
                FROM final_adjusted_data
                WHERE session_id = ?
            """

            params = [session_id]

            if selected_sections:
                placeholders = ','.join(['?' for _ in selected_sections])
                base_query += f" AND section IN ({placeholders})"
                params.extend(selected_sections)

            base_query += " ORDER BY employee_no"

            cursor.execute(base_query, params)
            rows = cursor.fetchall()

            # Convert to list of dictionaries
            data = []
            for row in rows:
                data.append({
                    'employee_no': row['employee_no'],
                    'employee_name': row['employee_name'],
                    'department': row['department'],
                    'section': row['section'],
                    'job_title': row['job_title'],
                    'net_pay': row['net_pay'] or 0.0,
                    'gross_salary': row['gross_salary'] or 0.0,
                    'taxable_salary': row['taxable_salary'] or 0.0,
                    'total_deductions': row['total_deductions'] or 0.0,
                    'bank': row['bank'] or 'NO BANK SPECIFIED',
                    'account_no': row['account_no'] or 'N/A',
                    'branch': row['branch'] or '',
                    'extraction_confidence': row['extraction_confidence'] or 1.0
                })

            conn.close()

            print(f"[DATABASE] Retrieved {len(data)} Final Adjusted records from database")
            if selected_sections:
                print(f"[DATABASE] Filtered by sections: {selected_sections}")

            return data

        except Exception as e:
            print(f"[ERROR] Error retrieving Final Adjusted data: {e}")
            return []

    def _get_allowances_data(self, session_data: Dict) -> List[Dict]:
        """Get Allowances data from database"""
        try:
            import sqlite3
            import os

            # Connect to database
            db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'templar_payroll_auditor.db')
            if not os.path.exists(db_path):
                print(f"[ERROR] Database not found: {db_path}")
                return []

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get session ID from session_data
            session_id = session_data.get('session_id')
            if not session_id:
                print("[ERROR] No session_id provided")
                return []

            # Query allowances data with bank information from final_adjusted_data
            query = """
                SELECT a.employee_no, a.employee_name, a.department,
                       a.allowance_type, a.gross_amount, a.tax_amount, a.payable_amount,
                       f.bank, f.account_no, f.branch
                FROM allowances_data a
                LEFT JOIN final_adjusted_data f ON a.employee_no = f.employee_no AND a.session_id = f.session_id
                WHERE a.session_id = ?
                ORDER BY a.employee_no, a.allowance_type
            """

            cursor.execute(query, [session_id])
            rows = cursor.fetchall()

            # Convert to list of dictionaries
            data = []
            for row in rows:
                data.append({
                    'employee_no': row['employee_no'],
                    'employee_name': row['employee_name'],
                    'department': row['department'],
                    'allowance_type': row['allowance_type'],
                    'gross_amount': row['gross_amount'] or 0.0,
                    'tax_amount': row['tax_amount'] or 0.0,
                    'payable_amount': row['payable_amount'] or 0.0,
                    'bank': row['bank'] or 'NO BANK SPECIFIED',
                    'account_no': row['account_no'] or 'N/A',
                    'branch': row['branch'] or ''
                })

            conn.close()

            print(f"[DATABASE] Retrieved {len(data)} Allowances records from database")

            return data

        except Exception as e:
            print(f"[ERROR] Error retrieving Allowances data: {e}")
            return []

    def _get_awards_data(self, session_data: Dict) -> List[Dict]:
        """Get Awards data from database"""
        try:
            import sqlite3
            import os

            # Connect to database
            db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'templar_payroll_auditor.db')
            if not os.path.exists(db_path):
                print(f"[ERROR] Database not found: {db_path}")
                return []

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get session ID from session_data
            session_id = session_data.get('session_id')
            if not session_id:
                print("[ERROR] No session_id provided")
                return []

            # Query awards data with bank information from final_adjusted_data
            query = """
                SELECT a.employee_no, a.employee_name, a.department,
                       a.award_type, a.gross_amount, a.tax_amount, a.payable_amount, a.net_pay,
                       f.bank, f.account_no, f.branch
                FROM awards_data a
                LEFT JOIN final_adjusted_data f ON a.employee_no = f.employee_no AND a.session_id = f.session_id
                WHERE a.session_id = ?
                ORDER BY a.employee_no, a.award_type
            """

            cursor.execute(query, [session_id])
            rows = cursor.fetchall()

            # Convert to list of dictionaries
            data = []
            for row in rows:
                data.append({
                    'employee_no': row['employee_no'],
                    'employee_name': row['employee_name'],
                    'department': row['department'],
                    'award_type': row['award_type'],
                    'gross_amount': row['gross_amount'] or 0.0,
                    'tax_amount': row['tax_amount'] or 0.0,
                    'payable_amount': row['payable_amount'] or 0.0,
                    'net_pay': row['net_pay'] or 0.0,
                    'bank': row['bank'] or 'NO BANK SPECIFIED',
                    'account_no': row['account_no'] or 'N/A',
                    'branch': row['branch'] or ''
                })

            conn.close()

            print(f"[DATABASE] Retrieved {len(data)} Awards records from database")

            return data

        except Exception as e:
            print(f"[ERROR] Error retrieving Awards data: {e}")
            return []

    def _group_allowances_by_bank(self, allowances_data: List[Dict]) -> Dict:
        """Group allowances by bank and allowance type"""
        bank_groups = {}

        for allowance in allowances_data:
            bank = allowance.get('bank', 'UNKNOWN BANK')
            if bank == '' or bank is None:
                bank = 'NO BANK SPECIFIED'

            if bank not in bank_groups:
                bank_groups[bank] = {}

            allowance_type = allowance.get('allowance_type', 'UNKNOWN ALLOWANCE')
            if allowance_type not in bank_groups[bank]:
                bank_groups[bank][allowance_type] = []

            bank_groups[bank][allowance_type].append(allowance)

        return bank_groups

    def _group_awards_by_bank(self, awards_data: List[Dict]) -> Dict:
        """Group awards by bank and award type"""
        bank_groups = {}

        for award in awards_data:
            bank = award.get('bank', 'UNKNOWN BANK')
            if bank == '' or bank is None:
                bank = 'NO BANK SPECIFIED'

            if bank not in bank_groups:
                bank_groups[bank] = {}

            award_type = award.get('award_type', 'UNKNOWN AWARD')
            if award_type not in bank_groups[bank]:
                bank_groups[bank][award_type] = []

            bank_groups[bank][award_type].append(award)

        return bank_groups

    def _create_composite_allowances_advice(self, bank_groups: Dict, output_path: str) -> Dict:
        """Create composite allowances bank advice"""
        wb = Workbook()
        wb.remove(wb.active)

        total_employees = 0
        total_amount = 0.0

        for bank_name, allowance_types in bank_groups.items():
            for allowance_type, allowances in allowance_types.items():
                # Create sheet for each bank-allowance combination
                sheet_name = f"{self._sanitize_sheet_name(bank_name)}_{self._sanitize_sheet_name(allowance_type)}"
                ws = wb.create_sheet(title=sheet_name[:31])

                # Add header with allowance type
                self._add_allowance_header(ws, allowance_type, bank_name)

                # Add allowance data table
                table_result = self._add_allowance_table(ws, allowances)

                total_employees += len(allowances)
                total_amount += table_result['total_amount']

                # Add PRE-AUDITED remark
                self._add_pre_audited_remark(ws, table_result['last_row'])

        wb.save(output_path)

        return {
            "success": True,
            "file_path": output_path,
            "banks_count": len(bank_groups),
            "total_employees": total_employees,
            "total_amount": total_amount,
            "generation_type": "composite"
        }

    def _create_composite_awards_advice(self, bank_groups: Dict, output_path: str) -> Dict:
        """Create composite awards bank advice"""
        wb = Workbook()
        wb.remove(wb.active)

        total_employees = 0
        total_amount = 0.0

        for bank_name, award_types in bank_groups.items():
            for award_type, awards in award_types.items():
                # Create sheet for each bank-award combination
                sheet_name = f"{self._sanitize_sheet_name(bank_name)}_{self._sanitize_sheet_name(award_type)}"
                ws = wb.create_sheet(title=sheet_name[:31])

                # Add header with award type
                self._add_award_header(ws, award_type, bank_name)

                # Add award data table
                table_result = self._add_award_table(ws, awards)

                total_employees += len(awards)
                total_amount += table_result['total_amount']

                # Add PRE-AUDITED remark
                self._add_pre_audited_remark(ws, table_result['last_row'])

        wb.save(output_path)

        return {
            "success": True,
            "file_path": output_path,
            "banks_count": len(bank_groups),
            "total_employees": total_employees,
            "total_amount": total_amount,
            "generation_type": "composite"
        }

    def _add_allowance_header(self, ws: Worksheet, allowance_type: str, bank_name: str):
        """Add allowance-specific header"""
        # Church name
        ws['A1'] = self.church_name
        ws['A1'].font = self.header_font
        ws['A1'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A1:G1')

        # Allowance type title
        ws['A3'] = f"{allowance_type.upper()} BANK ADVICE"
        ws['A3'].font = self.subheader_font
        ws['A3'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A3:G3')

        # Bank name
        ws['A4'] = f"BANK: {bank_name}"
        ws['A4'].font = self.subheader_font

        # Date
        ws['F4'] = f"DATE: {datetime.now().strftime('%d/%m/%Y')}"
        ws['F4'].font = self.normal_font
        ws['F4'].alignment = Alignment(horizontal='right')

    def _add_allowance_table(self, ws: Worksheet, allowances: List[Dict]) -> Dict:
        """Add allowance data table with tax information"""
        start_row = 6

        # Headers for allowances
        headers = ['S/N', 'EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT', 'ACCOUNT NO.', 'GROSS (GHS)', 'TAX (GHS)', 'PAYABLE (GHS)']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.font = self.subheader_font
            cell.fill = self.header_fill
            cell.border = self.thin_border
            cell.alignment = Alignment(horizontal='center')

        # Allowance data
        current_row = start_row + 1
        total_gross = 0.0
        total_tax = 0.0
        total_payable = 0.0

        for i, allowance in enumerate(allowances, 1):
            ws.cell(row=current_row, column=1, value=i).border = self.thin_border
            ws.cell(row=current_row, column=2, value=allowance.get('employee_no', '')).border = self.thin_border
            ws.cell(row=current_row, column=3, value=allowance.get('employee_name', '')).border = self.thin_border
            ws.cell(row=current_row, column=4, value=allowance.get('department', '')).border = self.thin_border
            ws.cell(row=current_row, column=5, value=allowance.get('account_no', '')).border = self.thin_border

            gross = allowance.get('gross_amount', 0.0)
            tax = allowance.get('tax_amount', 0.0)
            payable = allowance.get('payable_amount', 0.0)

            ws.cell(row=current_row, column=6, value=gross).border = self.thin_border
            ws.cell(row=current_row, column=6).number_format = '#,##0.00'

            ws.cell(row=current_row, column=7, value=tax).border = self.thin_border
            ws.cell(row=current_row, column=7).number_format = '#,##0.00'

            ws.cell(row=current_row, column=8, value=payable).border = self.thin_border
            ws.cell(row=current_row, column=8).number_format = '#,##0.00'

            total_gross += gross
            total_tax += tax
            total_payable += payable
            current_row += 1

        # Total row
        ws.cell(row=current_row, column=5, value="TOTAL:").font = self.subheader_font
        ws.cell(row=current_row, column=5).border = self.thin_border
        ws.cell(row=current_row, column=5).fill = self.total_fill

        for col, total_val in enumerate([total_gross, total_tax, total_payable], 6):
            total_cell = ws.cell(row=current_row, column=col, value=total_val)
            total_cell.font = self.subheader_font
            total_cell.border = self.thin_border
            total_cell.fill = self.total_fill
            total_cell.number_format = '#,##0.00'

        self._auto_adjust_columns(ws)

        return {"last_row": current_row, "total_amount": total_payable}

    def _add_award_header(self, ws: Worksheet, award_type: str, bank_name: str):
        """Add award-specific header"""
        # Church name
        ws['A1'] = self.church_name
        ws['A1'].font = self.header_font
        ws['A1'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A1:G1')

        # Award type title
        ws['A3'] = f"{award_type.upper()} BANK ADVICE"
        ws['A3'].font = self.subheader_font
        ws['A3'].alignment = Alignment(horizontal='center')
        ws.merge_cells('A3:G3')

        # Bank name
        ws['A4'] = f"BANK: {bank_name}"
        ws['A4'].font = self.subheader_font

        # Date
        ws['F4'] = f"DATE: {datetime.now().strftime('%d/%m/%Y')}"
        ws['F4'].font = self.normal_font
        ws['F4'].alignment = Alignment(horizontal='right')

    def _add_award_table(self, ws: Worksheet, awards: List[Dict]) -> Dict:
        """Add award data table with tax information"""
        start_row = 6

        # Headers for awards
        headers = ['S/N', 'EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT', 'ACCOUNT NO.', 'GROSS (GHS)', 'TAX (GHS)', 'PAYABLE (GHS)']

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.font = self.subheader_font
            cell.fill = self.header_fill
            cell.border = self.thin_border
            cell.alignment = Alignment(horizontal='center')

        # Award data
        current_row = start_row + 1
        total_gross = 0.0
        total_tax = 0.0
        total_payable = 0.0

        for i, award in enumerate(awards, 1):
            ws.cell(row=current_row, column=1, value=i).border = self.thin_border
            ws.cell(row=current_row, column=2, value=award.get('employee_no', '')).border = self.thin_border
            ws.cell(row=current_row, column=3, value=award.get('employee_name', '')).border = self.thin_border
            ws.cell(row=current_row, column=4, value=award.get('department', '')).border = self.thin_border
            ws.cell(row=current_row, column=5, value=award.get('account_no', '')).border = self.thin_border

            gross = award.get('gross_amount', 0.0)
            tax = award.get('tax_amount', 0.0)
            payable = award.get('payable_amount', 0.0)

            ws.cell(row=current_row, column=6, value=gross).border = self.thin_border
            ws.cell(row=current_row, column=6).number_format = '#,##0.00'

            ws.cell(row=current_row, column=7, value=tax).border = self.thin_border
            ws.cell(row=current_row, column=7).number_format = '#,##0.00'

            ws.cell(row=current_row, column=8, value=payable).border = self.thin_border
            ws.cell(row=current_row, column=8).number_format = '#,##0.00'

            total_gross += gross
            total_tax += tax
            total_payable += payable
            current_row += 1

        # Total row
        ws.cell(row=current_row, column=5, value="TOTAL:").font = self.subheader_font
        ws.cell(row=current_row, column=5).border = self.thin_border
        ws.cell(row=current_row, column=5).fill = self.total_fill

        for col, total_val in enumerate([total_gross, total_tax, total_payable], 6):
            total_cell = ws.cell(row=current_row, column=col, value=total_val)
            total_cell.font = self.subheader_font
            total_cell.border = self.thin_border
            total_cell.fill = self.total_fill
            total_cell.number_format = '#,##0.00'

        self._auto_adjust_columns(ws)

        return {"last_row": current_row, "total_amount": total_payable}

    def _create_separate_bank_advice(self, bank_groups: Dict, output_path: str, advice_type: str) -> Dict:
        """Create separate Excel files for each bank"""
        results = []
        total_employees = 0
        total_amount = 0.0

        base_path = os.path.splitext(output_path)[0]

        for bank_name, employees in bank_groups.items():
            # Create separate file for each bank
            bank_file_path = f"{base_path}_{self._sanitize_sheet_name(bank_name)}.xlsx"

            wb = Workbook()
            ws = wb.active
            ws.title = self._sanitize_sheet_name(bank_name)

            # Add Church of Pentecost header
            self._add_church_header(ws, advice_type, bank_name)

            # Add employee data table
            table_result = self._add_employee_table(ws, employees, advice_type)

            # Add PRE-AUDITED remark
            self._add_pre_audited_remark(ws, table_result['last_row'])

            wb.save(bank_file_path)

            results.append({
                "bank": bank_name,
                "file_path": bank_file_path,
                "employees": len(employees),
                "amount": table_result['total_amount']
            })

            total_employees += len(employees)
            total_amount += table_result['total_amount']

        return {
            "success": True,
            "files": results,
            "banks_count": len(bank_groups),
            "total_employees": total_employees,
            "total_amount": total_amount,
            "generation_type": "separate"
        }

    def _create_separate_allowances_advice(self, bank_groups: Dict, output_path: str) -> Dict:
        """Create separate allowances files"""
        # Similar to _create_separate_bank_advice but for allowances
        # Implementation would be similar with allowance-specific formatting
        return {"success": True, "message": "Separate allowances advice generation not yet implemented"}

    def _create_separate_awards_advice(self, bank_groups: Dict, output_path: str) -> Dict:
        """Create separate awards files"""
        # Similar to _create_separate_bank_advice but for awards
        # Implementation would be similar with award-specific formatting
        return {"success": True, "message": "Separate awards advice generation not yet implemented"}


def main():
    """Command line interface for Bank Advice Excel Generator"""
    if len(sys.argv) < 4:
        print("Usage: python bank_advice_excel_generator.py <advice_type> <output_path> <generation_type> [session_data_file]")
        print("Advice types: final_adjusted, allowances, awards")
        print("Generation types: composite, separate")
        sys.exit(1)

    advice_type = sys.argv[1]
    output_path = sys.argv[2]
    generation_type = sys.argv[3]
    session_data_file = sys.argv[4] if len(sys.argv) > 4 else None

    generator = BankAdviceExcelGenerator()

    # Load session data from file or use default
    if session_data_file and os.path.exists(session_data_file):
        try:
            with open(session_data_file, 'r') as f:
                session_data = json.load(f)
            print(f"[SESSION] Loaded session data from: {session_data_file}")
            print(f"[SESSION] Session ID: {session_data.get('session_id', 'Unknown')}")
        except Exception as e:
            print(f"[ERROR] Failed to load session data: {e}")
            session_data = {"session_id": "fallback_session"}
    else:
        # Fallback to sample data for testing
        session_data = {"session_id": "test_session"}
        print("[WARNING] Using sample session data (no session file provided)")

    try:
        if advice_type == 'final_adjusted':
            selected_sections = session_data.get('selected_sections', [])
            result = generator.generate_final_adjusted_bank_advice(
                session_data, output_path, selected_sections, generation_type
            )
        elif advice_type == 'allowances':
            result = generator.generate_allowances_bank_advice(session_data, output_path, generation_type)
        elif advice_type == 'awards':
            result = generator.generate_awards_bank_advice(session_data, output_path, generation_type)
        else:
            raise ValueError(f"Unknown advice type: {advice_type}")

        print(json.dumps(result, indent=2))

    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'advice_type': advice_type,
            'output_path': output_path,
            'session_data': session_data.get('session_id', 'Unknown') if session_data else 'None'
        }
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    main()