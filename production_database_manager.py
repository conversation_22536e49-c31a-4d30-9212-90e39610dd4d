#!/usr/bin/env python3
"""
Production Database Manager
Ensures unified database access and prevents future synchronization issues.
"""

import sqlite3
import os
import threading
import time
from pathlib import Path
from typing import Optional, Dict, Any
import logging

class ProductionDatabaseManager:
    """Production-level database manager with unified access and automatic failover"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.setup_logging()
        
        # Database configuration
        self.primary_db_path = Path("data/templar_payroll_auditor.db")
        self.secondary_db_path = Path("payroll_audit.db")
        self.current_db_path = None
        
        # Connection management
        self.connection_pool = {}
        self.pool_lock = threading.Lock()
        self.max_connections = 10
        
        # Health monitoring
        self.health_check_interval = 60  # seconds
        self.monitoring_active = False
        
        # Initialize database selection
        self.select_active_database()
        self.start_health_monitoring()
    
    def setup_logging(self):
        """Setup logging for database operations"""
        self.logger = logging.getLogger('ProductionDB')
        if not self.logger.handlers:
            handler = logging.FileHandler('production_database.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def select_active_database(self):
        """Select the most appropriate database to use"""
        primary_valid = self.validate_database(self.primary_db_path)
        secondary_valid = self.validate_database(self.secondary_db_path)
        
        if primary_valid and secondary_valid:
            # Both valid, prefer primary
            self.current_db_path = self.primary_db_path
            self.logger.info(f"Using primary database: {self.primary_db_path}")
        elif primary_valid:
            self.current_db_path = self.primary_db_path
            self.logger.info(f"Using primary database: {self.primary_db_path}")
        elif secondary_valid:
            self.current_db_path = self.secondary_db_path
            self.logger.warning(f"Primary database unavailable, using secondary: {self.secondary_db_path}")
        else:
            # Neither valid, create new database
            self.current_db_path = self.primary_db_path
            self.ensure_database_structure(self.current_db_path)
            self.logger.warning(f"Created new database: {self.current_db_path}")
    
    def validate_database(self, db_path: Path) -> bool:
        """Validate database integrity"""
        try:
            if not db_path.exists():
                return False
            
            conn = sqlite3.connect(str(db_path), timeout=5)
            cursor = conn.cursor()
            
            # Check for required tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = {row[0] for row in cursor.fetchall()}
            
            required_tables = {
                'dictionary_sections', 'dictionary_items', 
                'comparison_results', 'pre_reporting_results'
            }
            
            if not required_tables.issubset(tables):
                conn.close()
                return False
            
            # Check dictionary data
            cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
            sections_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dictionary_items")
            items_count = cursor.fetchone()[0]
            
            conn.close()
            
            # Database is valid if it has dictionary data
            return sections_count > 0 and items_count > 0
            
        except Exception as e:
            self.logger.error(f"Database validation failed for {db_path}: {e}")
            return False
    
    def ensure_database_structure(self, db_path: Path):
        """Ensure database has all required tables"""
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Create all required tables
        tables_sql = [
            """CREATE TABLE IF NOT EXISTS dictionary_sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT NOT NULL UNIQUE
            )""",
            
            """CREATE TABLE IF NOT EXISTS dictionary_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_id INTEGER NOT NULL,
                item_name TEXT NOT NULL,
                include_in_report BOOLEAN DEFAULT 1,
                include_new BOOLEAN DEFAULT 1,
                include_increase BOOLEAN DEFAULT 1,
                include_decrease BOOLEAN DEFAULT 1,
                include_removed BOOLEAN DEFAULT 1,
                include_no_change BOOLEAN DEFAULT 0,
                FOREIGN KEY (section_id) REFERENCES dictionary_sections (id),
                UNIQUE(section_id, item_name)
            )""",
            
            """CREATE TABLE IF NOT EXISTS comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section TEXT,
                item_label TEXT,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT,
                priority_level TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
            
            """CREATE TABLE IF NOT EXISTS pre_reporting_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                change_id INTEGER,
                section TEXT,
                item_label TEXT,
                change_type TEXT,
                priority_level TEXT,
                bulk_category TEXT,
                is_selected BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (change_id) REFERENCES comparison_results (id)
            )"""
        ]
        
        for sql in tables_sql:
            cursor.execute(sql)
        
        conn.commit()
        conn.close()
    
    def get_connection(self) -> sqlite3.Connection:
        """Get a database connection with automatic failover"""
        thread_id = threading.get_ident()
        
        with self.pool_lock:
            # Check if we have a connection for this thread
            if thread_id in self.connection_pool:
                conn = self.connection_pool[thread_id]
                try:
                    # Test connection
                    conn.execute("SELECT 1")
                    return conn
                except:
                    # Connection is dead, remove it
                    del self.connection_pool[thread_id]
            
            # Create new connection
            try:
                conn = sqlite3.connect(str(self.current_db_path), timeout=10)
                conn.row_factory = sqlite3.Row  # Enable column access by name
                self.connection_pool[thread_id] = conn
                return conn
            except Exception as e:
                self.logger.error(f"Failed to connect to {self.current_db_path}: {e}")
                
                # Try failover
                if self.current_db_path == self.primary_db_path:
                    self.logger.info("Attempting failover to secondary database")
                    self.current_db_path = self.secondary_db_path
                else:
                    self.logger.info("Attempting failover to primary database")
                    self.current_db_path = self.primary_db_path
                
                try:
                    conn = sqlite3.connect(str(self.current_db_path), timeout=10)
                    conn.row_factory = sqlite3.Row
                    self.connection_pool[thread_id] = conn
                    self.logger.info(f"Failover successful to {self.current_db_path}")
                    return conn
                except Exception as e2:
                    self.logger.error(f"Failover also failed: {e2}")
                    raise Exception("All database connections failed")
    
    def execute_query(self, query: str, params: tuple = (), fetch: str = None):
        """Execute query with automatic retry and failover"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                conn = self.get_connection()
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                if fetch == 'one':
                    result = cursor.fetchone()
                elif fetch == 'all':
                    result = cursor.fetchall()
                else:
                    result = cursor.rowcount
                
                conn.commit()
                return result
                
            except Exception as e:
                self.logger.error(f"Query execution failed (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(0.1 * (attempt + 1))  # Exponential backoff
    
    def get_dictionary_items(self) -> Dict[str, Any]:
        """Get all dictionary items with their toggle states"""
        query = """
            SELECT ds.section_name, di.item_name, 
                   di.include_in_report, di.include_new, di.include_increase,
                   di.include_decrease, di.include_removed, di.include_no_change
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            ORDER BY ds.section_name, di.item_name
        """
        
        rows = self.execute_query(query, fetch='all')
        
        dictionary = {}
        for row in rows:
            section_name = row['section_name']
            if section_name not in dictionary:
                dictionary[section_name] = {'items': {}}
            
            dictionary[section_name]['items'][row['item_name']] = {
                'include_in_report': bool(row['include_in_report']),
                'include_new': bool(row['include_new']),
                'include_increase': bool(row['include_increase']),
                'include_decrease': bool(row['include_decrease']),
                'include_removed': bool(row['include_removed']),
                'include_no_change': bool(row['include_no_change'])
            }
        
        return dictionary
    
    def start_health_monitoring(self):
        """Start continuous health monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        def monitor_loop():
            while self.monitoring_active:
                try:
                    # Check database health
                    primary_valid = self.validate_database(self.primary_db_path)
                    secondary_valid = self.validate_database(self.secondary_db_path)
                    
                    # Switch to better database if needed
                    if not primary_valid and not secondary_valid:
                        self.logger.error("Both databases are invalid!")
                    elif primary_valid and self.current_db_path != self.primary_db_path:
                        self.logger.info("Primary database recovered, switching back")
                        self.current_db_path = self.primary_db_path
                        # Clear connection pool to force new connections
                        with self.pool_lock:
                            self.connection_pool.clear()
                    
                    time.sleep(self.health_check_interval)
                    
                except Exception as e:
                    self.logger.error(f"Health monitoring error: {e}")
                    time.sleep(5)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        self.logger.info("Database health monitoring started")
    
    def stop_health_monitoring(self):
        """Stop health monitoring"""
        self.monitoring_active = False
        self.logger.info("Database health monitoring stopped")
    
    def close_all_connections(self):
        """Close all database connections"""
        with self.pool_lock:
            for conn in self.connection_pool.values():
                try:
                    conn.close()
                except:
                    pass
            self.connection_pool.clear()

# Global instance
db_manager = ProductionDatabaseManager()

def get_database_manager() -> ProductionDatabaseManager:
    """Get the global database manager instance"""
    return db_manager
