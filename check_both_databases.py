#!/usr/bin/env python3
"""Check both databases to see which has the data"""

import sqlite3
import os

def check_databases():
    print("🔍 CHECKING BOTH DATABASES")
    print("=" * 40)
    
    # Check payroll_audit.db
    print("1. payroll_audit.db:")
    if os.path.exists('payroll_audit.db'):
        conn1 = sqlite3.connect('payroll_audit.db')
        cursor1 = conn1.cursor()
        
        try:
            cursor1.execute('SELECT COUNT(*) FROM pre_reporting_results')
            count1 = cursor1.fetchone()[0]
            print(f"   pre_reporting_results: {count1} records")
            
            cursor1.execute('SELECT COUNT(*) FROM comparison_results')
            count1_cr = cursor1.fetchone()[0]
            print(f"   comparison_results: {count1_cr} records")
        except Exception as e:
            print(f"   Error: {e}")
        
        conn1.close()
    else:
        print("   File not found")
    
    # Check data/templar_payroll_auditor.db
    print("\n2. data/templar_payroll_auditor.db:")
    if os.path.exists('data/templar_payroll_auditor.db'):
        conn2 = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor2 = conn2.cursor()
        
        try:
            cursor2.execute('SELECT COUNT(*) FROM pre_reporting_results')
            count2 = cursor2.fetchone()[0]
            print(f"   pre_reporting_results: {count2} records")
            
            cursor2.execute('SELECT COUNT(*) FROM comparison_results')
            count2_cr = cursor2.fetchone()[0]
            print(f"   comparison_results: {count2_cr} records")
        except Exception as e:
            print(f"   Error: {e}")
        
        conn2.close()
    else:
        print("   File not found")

if __name__ == "__main__":
    check_databases()
