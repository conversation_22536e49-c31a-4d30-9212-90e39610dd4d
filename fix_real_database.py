#!/usr/bin/env python3
"""Fix the real database for report generation"""

import sqlite3
import os

def fix_real_database():
    print("🔧 FIXING REAL DATABASE")
    print("=" * 40)
    
    db_path = 'data/templar_payroll_auditor.db'
    
    if not os.path.exists(db_path):
        print("❌ Real database not found")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Check current schema
        print("1. Checking current schema...")
        cursor.execute('PRAGMA table_info(pre_reporting_results)')
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        print(f"   Current columns: {column_names}")
        
        # 2. Add selected_for_report column if missing
        if 'selected_for_report' not in column_names:
            print("2. Adding selected_for_report column...")
            cursor.execute('ALTER TABLE pre_reporting_results ADD COLUMN selected_for_report INTEGER DEFAULT 1')
            print("   ✅ Column added")
        else:
            print("2. selected_for_report column already exists")
        
        # 3. Update selected_for_report based on is_selected if it exists
        if 'is_selected' in column_names:
            print("3. Updating selected_for_report from is_selected...")
            cursor.execute('UPDATE pre_reporting_results SET selected_for_report = is_selected WHERE is_selected IS NOT NULL')
            updated = cursor.rowcount
            print(f"   ✅ Updated {updated} records")
        else:
            print("3. is_selected column not found, setting all to selected...")
            cursor.execute('UPDATE pre_reporting_results SET selected_for_report = 1')
            updated = cursor.rowcount
            print(f"   ✅ Updated {updated} records")
        
        # 4. Create missing report tables
        print("4. Creating report tables...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS generated_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                report_type TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER DEFAULT 0,
                report_metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("   ✅ generated_reports table ready")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_id TEXT UNIQUE NOT NULL,
                report_type TEXT NOT NULL,
                report_category TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                file_paths TEXT,
                metadata TEXT,
                file_size INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("   ✅ reports table ready")
        
        # 5. Check data counts
        print("5. Checking data...")
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE selected_for_report = 1')
        selected_count = cursor.fetchone()[0]
        print(f"   Selected pre-reporting results: {selected_count}")
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results')
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        # 6. Get a recent session for testing
        cursor.execute('SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY created_at DESC LIMIT 1')
        session_result = cursor.fetchone()
        
        if session_result:
            session = session_result[0]
            print(f"   Latest session: {session}")
            
            # Check if this session has comparison results
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session,))
            session_comparison_count = cursor.fetchone()[0]
            print(f"   Session comparison results: {session_comparison_count}")
            
            if session_comparison_count == 0:
                print("   ⚠️ Latest session has no comparison results")
                # Find a session that has both
                cursor.execute('''
                    SELECT pr.session_id, COUNT(DISTINCT pr.id) as pr_count, COUNT(DISTINCT cr.id) as cr_count
                    FROM pre_reporting_results pr
                    LEFT JOIN comparison_results cr ON pr.session_id = cr.session_id
                    GROUP BY pr.session_id
                    HAVING cr_count > 0
                    ORDER BY pr.session_id DESC
                    LIMIT 1
                ''')
                
                good_session = cursor.fetchone()
                if good_session:
                    print(f"   Found good session: {good_session[0]} (PR: {good_session[1]}, CR: {good_session[2]})")
                else:
                    print("   ❌ No session found with both pre-reporting and comparison results")
        
        conn.commit()
        print("\n✅ Real database fixed successfully!")
        return True
    
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        conn.close()

if __name__ == "__main__":
    success = fix_real_database()
    if success:
        print("\n🎉 Database is ready for report generation!")
    else:
        print("\n❌ Database fix failed")
