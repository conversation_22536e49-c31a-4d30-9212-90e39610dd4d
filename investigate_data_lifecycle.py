#!/usr/bin/env python3
"""
INVESTIGATE DATA LIFECYCLE
Determine when and how comparison_results gets cleared
"""

import sqlite3
import sys
import os

def investigate_data_lifecycle():
    print("🔍 INVESTIGATING DATA LIFECYCLE & CLEANUP POLICIES")
    print("=" * 60)
    
    db_path = 'data/templar_payroll_auditor.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Check current session data
        print("1. 📊 CURRENT SESSION DATA STATE:")
        cursor.execute('SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY created_at DESC LIMIT 1')
        current_session = cursor.fetchone()[0]
        print(f"   Current session: {current_session}")
        
        # Check all data for this session
        tables_to_check = [
            'extracted_data', 'comparison_results', 'pre_reporting_results',
            'generated_reports', 'in_house_loans', 'external_loans', 
            'motor_vehicle_maintenance'
        ]
        
        session_data = {}
        for table in tables_to_check:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM {table} WHERE session_id = ? OR source_session = ?', 
                             (current_session, current_session))
                count = cursor.fetchone()[0]
                session_data[table] = count
                print(f"   {table}: {count} records")
            except sqlite3.OperationalError:
                session_data[table] = 0
                print(f"   {table}: table doesn't exist or no session_id column")
        
        # 2. Check session phases and completion status
        print("\n2. 📋 SESSION PHASES & COMPLETION STATUS:")
        cursor.execute('SELECT * FROM session_phases WHERE session_id = ?', (current_session,))
        phases = cursor.fetchall()
        
        if phases:
            for phase in phases:
                phase_name, status = phase[1], phase[2]
                print(f"   {phase_name}: {status}")
        else:
            print("   ⚠️ No phase tracking found")
        
        # 3. Check for cleanup triggers in session metadata
        print("\n3. 🔍 CHECKING CLEANUP TRIGGERS:")
        
        # Check if there are any cleanup logs
        try:
            cursor.execute('SELECT * FROM session_guidance_log ORDER BY timestamp DESC LIMIT 5')
            cleanup_logs = cursor.fetchall()
            if cleanup_logs:
                print("   Recent session guidance logs:")
                for log in cleanup_logs:
                    print(f"     {log}")
            else:
                print("   No session guidance logs found")
        except sqlite3.OperationalError:
            print("   No session guidance log table")
        
        # 4. Check for automatic cleanup policies
        print("\n4. 🧹 AUTOMATIC CLEANUP POLICIES:")
        
        # Check if there are old sessions that might trigger cleanup
        cursor.execute('''
            SELECT session_id, created_at,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = s.session_id) as comparison_count,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = s.session_id) as pre_reporting_count
            FROM audit_sessions s
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        all_sessions = cursor.fetchall()
        print("   Recent sessions and their data:")
        for session_info in all_sessions:
            session_id, created_at, comp_count, pre_count = session_info
            print(f"     {session_id}: created {created_at}, comparison: {comp_count}, pre_reporting: {pre_count}")
        
        # 5. Analyze when data gets cleared
        print("\n5. 🎯 DATA CLEARING ANALYSIS:")
        
        # Check if comparison_results gets cleared after report generation
        cursor.execute('''
            SELECT COUNT(*) FROM generated_reports 
            WHERE session_id = ?
        ''', (current_session,))
        
        reports_generated = cursor.fetchone()[0]
        print(f"   Reports generated for current session: {reports_generated}")
        
        if reports_generated > 0 and session_data['comparison_results'] == 0:
            print("   🚨 PATTERN DETECTED: comparison_results cleared AFTER report generation")
            print("   📋 EVIDENCE: Reports exist but comparison_results is empty")
        elif reports_generated == 0 and session_data['comparison_results'] > 0:
            print("   ✅ NORMAL STATE: comparison_results exists, no reports generated yet")
        elif reports_generated > 0 and session_data['comparison_results'] > 0:
            print("   ✅ IDEAL STATE: Both reports and comparison_results exist")
        else:
            print("   ⚠️ UNCLEAR STATE: No reports and no comparison_results")
        
        # 6. Check for cleanup triggers in the codebase patterns
        print("\n6. 🔍 CLEANUP TRIGGER ANALYSIS:")
        
        # Based on the codebase analysis, identify potential cleanup points
        cleanup_triggers = [
            "New session creation",
            "Report generation completion", 
            "Session reuse/switching",
            "Automatic maintenance (30+ days old)",
            "Manual cleanup commands",
            "Error recovery procedures"
        ]
        
        print("   Potential cleanup triggers identified:")
        for trigger in cleanup_triggers:
            print(f"     • {trigger}")
        
        # 7. Determine the most likely cleanup point
        print("\n7. 🎯 MOST LIKELY CLEANUP POINT:")
        
        if session_data['comparison_results'] == 0 and session_data['pre_reporting_results'] > 0:
            if reports_generated > 0:
                print("   🚨 CONCLUSION: comparison_results cleared AFTER report generation")
                print("   📋 REASON: Reports exist, pre_reporting_results exist, but comparison_results empty")
                print("   💡 SOLUTION: Modify cleanup policy to preserve comparison_results")
                return "AFTER_REPORT_GENERATION"
            else:
                print("   🚨 CONCLUSION: comparison_results cleared during session management")
                print("   📋 REASON: No reports yet, but comparison_results already empty")
                print("   💡 SOLUTION: Fix session initialization/switching logic")
                return "DURING_SESSION_MANAGEMENT"
        else:
            print("   ✅ CONCLUSION: Data state is normal")
            return "NORMAL"
    
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return "ERROR"
    
    finally:
        conn.close()

def recommend_data_retention_policy(cleanup_point):
    print("\n" + "=" * 60)
    print("📋 RECOMMENDED DATA RETENTION POLICY")
    print("=" * 60)
    
    if cleanup_point == "AFTER_REPORT_GENERATION":
        print("🎯 ISSUE: comparison_results cleared after report generation")
        print("\n📋 RECOMMENDED POLICY:")
        print("   ✅ KEEP comparison_results until:")
        print("     • Next audit session starts")
        print("     • 30+ days old (automatic maintenance)")
        print("     • Manual cleanup command")
        print("   ❌ DO NOT clear comparison_results after:")
        print("     • Report generation")
        print("     • Pre-reporting completion")
        print("     • User interface operations")
        
    elif cleanup_point == "DURING_SESSION_MANAGEMENT":
        print("🎯 ISSUE: comparison_results cleared during session operations")
        print("\n📋 RECOMMENDED POLICY:")
        print("   ✅ PRESERVE comparison_results during:")
        print("     • Session switching")
        print("     • Session reuse")
        print("     • Phase transitions")
        print("   ❌ ONLY clear comparison_results when:")
        print("     • Starting completely new audit")
        print("     • Explicit user request")
        print("     • Data corruption recovery")
        
    else:
        print("✅ Current data retention appears normal")
        print("\n📋 RECOMMENDED POLICY:")
        print("   ✅ MAINTAIN current approach:")
        print("     • Keep comparison_results throughout session lifecycle")
        print("     • Clear only during new audit initialization")
        print("     • Automatic cleanup after 30+ days")
    
    print("\n🔧 IMPLEMENTATION STEPS:")
    print("   1. Identify exact cleanup trigger in code")
    print("   2. Modify cleanup logic to preserve comparison_results")
    print("   3. Add data retention configuration")
    print("   4. Implement proper lifecycle management")
    print("   5. Add monitoring to prevent future data loss")

if __name__ == "__main__":
    cleanup_point = investigate_data_lifecycle()
    recommend_data_retention_policy(cleanup_point)
    
    print(f"\n🎯 CLEANUP TRIGGER: {cleanup_point}")
    print("🔧 Use this information to implement proper data retention policy")
