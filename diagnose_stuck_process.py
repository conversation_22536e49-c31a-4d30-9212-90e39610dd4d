#!/usr/bin/env python3
"""
DIAGNOSE STUCK PROCESS
Find out why the process is stuck after extraction phase
"""

import sqlite3
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

def diagnose_stuck_process():
    print("🔍 DIAGNOSING STUCK PROCESS AFTER EXTRACTION")
    print("=" * 50)
    
    db_path = 'data/templar_payroll_auditor.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Get the most recent session (likely the stuck one)
        print("1. 📊 IDENTIFYING CURRENT SESSION:")
        
        # Check current_session table
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_result = cursor.fetchone()
        if current_result:
            current_session = current_result[0]
            print(f"   Current session: {current_session}")
        else:
            print("   ⚠️ No current session found")
            # Try to find most recent session
            cursor.execute('SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1')
            recent_result = cursor.fetchone()
            if recent_result:
                current_session = recent_result[0]
                print(f"   Using most recent session: {current_session}")
            else:
                print("   ❌ No sessions found at all")
                return False
        
        # 2. Check session phases status
        print("\n2. 📋 SESSION PHASES STATUS:")
        cursor.execute('SELECT * FROM session_phases WHERE session_id = ? ORDER BY phase_name', (current_session,))
        phases = cursor.fetchall()
        
        if phases:
            stuck_phase = None
            completed_phases = []
            
            for phase in phases:
                phase_name = phase[1]
                status = phase[2]
                started_at = phase[3]
                completed_at = phase[4]
                data_count = phase[5] if len(phase) > 5 else 0
                
                print(f"   {phase_name}: {status}")
                if started_at:
                    print(f"     Started: {started_at}")
                if completed_at:
                    print(f"     Completed: {completed_at}")
                if data_count:
                    print(f"     Data count: {data_count}")
                
                if status == 'COMPLETED':
                    completed_phases.append(phase_name)
                elif status in ['IN_PROGRESS', 'STARTED']:
                    stuck_phase = phase_name
                    print(f"     🚨 STUCK IN: {phase_name}")
            
            print(f"   Completed phases: {completed_phases}")
            if stuck_phase:
                print(f"   Stuck phase: {stuck_phase}")
        else:
            print("   ⚠️ No phase tracking found")
        
        # 3. Check data counts for each phase
        print("\n3. 📊 DATA VERIFICATION:")
        
        # Extraction data
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
        extracted_count = cursor.fetchone()[0]
        print(f"   Extracted data: {extracted_count} records")
        
        # Comparison data
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count} records")
        
        # Pre-reporting data
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (current_session,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   Pre-reporting results: {pre_reporting_count} records")
        
        # 4. Check for process locks or hanging operations
        print("\n4. 🔒 CHECKING FOR LOCKS:")
        
        try:
            cursor.execute('SELECT * FROM session_locks WHERE lock_type = ?', ('processing',))
            locks = cursor.fetchall()
            if locks:
                print("   🚨 PROCESSING LOCKS FOUND:")
                for lock in locks:
                    print(f"     {lock}")
            else:
                print("   ✅ No processing locks found")
        except sqlite3.OperationalError:
            print("   ℹ️ No session_locks table")
        
        # 5. Check recent activity
        print("\n5. ⏰ RECENT ACTIVITY CHECK:")
        
        # Check when extraction completed
        if 'EXTRACTION' in [p[1] for p in phases if p[2] == 'COMPLETED']:
            cursor.execute('''
                SELECT completed_at FROM session_phases 
                WHERE session_id = ? AND phase_name = 'EXTRACTION'
            ''', (current_session,))
            extraction_completed = cursor.fetchone()
            if extraction_completed:
                completed_time = extraction_completed[0]
                print(f"   Extraction completed: {completed_time}")
                
                # Calculate time since completion
                try:
                    completed_dt = datetime.fromisoformat(completed_time.replace('Z', '+00:00'))
                    now = datetime.now()
                    time_stuck = now - completed_dt
                    print(f"   Time stuck: {time_stuck}")
                    
                    if time_stuck > timedelta(minutes=5):
                        print("   🚨 PROCESS APPEARS STUCK (>5 minutes since extraction)")
                    else:
                        print("   ℹ️ Process may still be transitioning")
                except:
                    print("   ⚠️ Could not calculate stuck time")
        
        # 6. Determine the issue
        print("\n6. 🎯 DIAGNOSIS:")
        
        if extracted_count == 0:
            print("   🚨 ISSUE: No extracted data found")
            print("   💡 SOLUTION: Extraction phase may have failed silently")
            return "NO_EXTRACTED_DATA"
        
        elif extracted_count > 0 and comparison_count == 0:
            print("   🚨 ISSUE: Extraction completed but comparison phase not started")
            print("   💡 SOLUTION: Comparison phase trigger failed")
            return "COMPARISON_NOT_TRIGGERED"
        
        elif comparison_count > 0 and pre_reporting_count == 0:
            print("   🚨 ISSUE: Comparison completed but pre-reporting not started")
            print("   💡 SOLUTION: Pre-reporting phase trigger failed")
            return "PRE_REPORTING_NOT_TRIGGERED"
        
        elif stuck_phase:
            print(f"   🚨 ISSUE: Process stuck in {stuck_phase} phase")
            print("   💡 SOLUTION: Phase execution hanging")
            return f"STUCK_IN_{stuck_phase}"
        
        else:
            print("   ⚠️ ISSUE: Unknown state - process appears stuck")
            return "UNKNOWN_STUCK_STATE"
    
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        import traceback
        traceback.print_exc()
        return "DIAGNOSIS_ERROR"
    
    finally:
        conn.close()

def provide_solution(issue_type):
    print("\n" + "=" * 50)
    print("🔧 SOLUTION RECOMMENDATIONS")
    print("=" * 50)
    
    if issue_type == "NO_EXTRACTED_DATA":
        print("📋 SOLUTION: Re-run extraction phase")
        print("   1. Check PDF files are accessible")
        print("   2. Verify extraction configuration")
        print("   3. Re-run the audit process")
    
    elif issue_type == "COMPARISON_NOT_TRIGGERED":
        print("📋 SOLUTION: Manually trigger comparison phase")
        print("   1. Force comparison phase to start")
        print("   2. Check phase transition logic")
        print("   3. Verify extracted data format")
    
    elif issue_type == "PRE_REPORTING_NOT_TRIGGERED":
        print("📋 SOLUTION: Manually trigger pre-reporting phase")
        print("   1. Force pre-reporting phase to start")
        print("   2. Check comparison results validity")
        print("   3. Verify phase dependencies")
    
    elif issue_type.startswith("STUCK_IN_"):
        phase = issue_type.replace("STUCK_IN_", "")
        print(f"📋 SOLUTION: Unstick {phase} phase")
        print("   1. Kill any hanging processes")
        print("   2. Clear processing locks")
        print("   3. Restart the phase")
    
    else:
        print("📋 SOLUTION: General troubleshooting")
        print("   1. Check system resources")
        print("   2. Review process logs")
        print("   3. Restart the audit process")
    
    print("\n🚀 IMMEDIATE ACTION:")
    print("   Run the unstuck script to force progression")

def create_unstuck_script():
    print("\n🔧 CREATING UNSTUCK SCRIPT...")
    
    unstuck_script = '''#!/usr/bin/env python3
"""Force unstuck the audit process"""

import sqlite3
import sys
from datetime import datetime

def force_unstuck():
    print("🚀 FORCING PROCESS TO CONTINUE...")
    
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    try:
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session = cursor.fetchone()[0]
        print(f"Session: {session}")
        
        # Clear any locks
        try:
            cursor.execute('DELETE FROM session_locks')
            print("✅ Cleared processing locks")
        except:
            pass
        
        # Force next phase to start
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (session,))
        extracted = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session,))
        comparison = cursor.fetchone()[0]
        
        if extracted > 0 and comparison == 0:
            print("🔄 Forcing COMPARISON phase...")
            cursor.execute('''
                UPDATE session_phases 
                SET status = 'READY', started_at = NULL 
                WHERE session_id = ? AND phase_name = 'COMPARISON'
            ''', (session,))
        
        elif comparison > 0:
            print("🔄 Forcing PRE_REPORTING phase...")
            cursor.execute('''
                UPDATE session_phases 
                SET status = 'READY', started_at = NULL 
                WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
            ''', (session,))
        
        conn.commit()
        print("✅ Process should continue now")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    force_unstuck()
'''
    
    with open('force_unstuck.py', 'w') as f:
        f.write(unstuck_script)
    
    print("✅ Created force_unstuck.py")
    print("🚀 Run: python force_unstuck.py")

if __name__ == "__main__":
    issue = diagnose_stuck_process()
    provide_solution(issue)
    create_unstuck_script()
    
    print(f"\n🎯 ISSUE IDENTIFIED: {issue}")
    print("🔧 Use the solutions above to resolve the stuck process")
