/**
 * BANK ADVISER DATABASE MANAGER
 * Dedicated database schema and operations for Bank Adviser Module
 * Handles both Core Bank Adviser and Loan & Allowance Tracker functionality
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class BankAdviserDatabase {
    constructor(dbPath = null) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'templar_payroll_auditor.db');
        this.db = null;
        this.isConnected = false;
        this.isInitialized = false;

        console.log('🏦 BANK ADVISER DATABASE INITIALIZING...');
        this.initializeDatabase();
    }

    /**
     * Initialize Bank Adviser database tables
     */
    async initializeDatabase() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('❌ Bank Adviser Database connection failed:', err);
                    reject(err);
                    return;
                }

                console.log('🔗 Bank Adviser Database connected');
                this.isConnected = true;

                // Enable performance optimizations
                this.db.serialize(() => {
                    this.db.run('PRAGMA journal_mode = WAL');
                    this.db.run('PRAGMA synchronous = NORMAL');
                    this.db.run('PRAGMA cache_size = 20000');
                    this.db.run('PRAGMA temp_store = MEMORY');

                    this.createBankAdviserTables()
                        .then(() => {
                            this.isInitialized = true;
                            console.log('✅ Bank Adviser Database ready');
                            resolve();
                        })
                        .catch(reject);
                });
            });
        });
    }

    /**
     * Create all Bank Adviser related tables
     */
    async createBankAdviserTables() {
        const tables = [
            // ========== CORE BANK ADVISER TABLES ==========

            // Bank Adviser Sessions
            `CREATE TABLE IF NOT EXISTS bank_adviser_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                session_name TEXT,
                current_month TEXT NOT NULL,
                current_year INTEGER NOT NULL,
                selected_sections TEXT, -- JSON array of selected sections
                generation_type TEXT DEFAULT 'composite', -- 'composite' or 'separate'
                processing_status TEXT DEFAULT 'pending',
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Document Processing Records
            `CREATE TABLE IF NOT EXISTS document_processing (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                document_type TEXT NOT NULL, -- 'final_adjusted', 'allowances', 'awards'
                file_path TEXT NOT NULL,
                file_name TEXT,
                total_employees INTEGER,
                processing_status TEXT DEFAULT 'pending',
                extraction_method TEXT DEFAULT 'bank_adviser_extractor',
                tax_percentage REAL, -- For allowances and awards
                processed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES bank_adviser_sessions(session_id)
            )`,

            // Extracted Employee Data (Final Adjusted Payslip)
            `CREATE TABLE IF NOT EXISTS final_adjusted_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                document_id INTEGER,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                section TEXT,
                job_title TEXT,
                net_pay REAL,
                bank TEXT,
                account_no TEXT,
                branch TEXT,
                extraction_confidence REAL DEFAULT 1.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES bank_adviser_sessions(session_id),
                FOREIGN KEY (document_id) REFERENCES document_processing(id)
            )`,

            // Extracted Allowances Data
            `CREATE TABLE IF NOT EXISTS allowances_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                document_id INTEGER,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                allowance_type TEXT NOT NULL,
                gross_amount REAL,
                tax_percentage REAL,
                tax_amount REAL,
                payable_amount REAL, -- After tax calculation
                extraction_confidence REAL DEFAULT 1.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES bank_adviser_sessions(session_id),
                FOREIGN KEY (document_id) REFERENCES document_processing(id)
            )`,

            // Extracted Awards Data
            `CREATE TABLE IF NOT EXISTS awards_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                document_id INTEGER,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                award_type TEXT NOT NULL,
                gross_amount REAL,
                tax_percentage REAL,
                tax_amount REAL,
                payable_amount REAL, -- After tax calculation
                net_pay REAL,
                extraction_confidence REAL DEFAULT 1.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES bank_adviser_sessions(session_id),
                FOREIGN KEY (document_id) REFERENCES document_processing(id)
            )`,

            // Generated Bank Advice Records
            `CREATE TABLE IF NOT EXISTS bank_advice_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                net_salary REAL,
                leave_allowance REAL DEFAULT 0,
                awards REAL DEFAULT 0,
                total_amount REAL,
                bank TEXT,
                account_no TEXT,
                branch TEXT,
                section TEXT,
                remarks TEXT DEFAULT 'PRE-AUDITED',
                row_color TEXT DEFAULT 'light_green',
                sort_order INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES bank_adviser_sessions(session_id)
            )`,

            // ========== LOAN & ALLOWANCE TRACKER TABLES ==========

            // In-House Loans
            `CREATE TABLE IF NOT EXISTS in_house_loans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                loan_type TEXT NOT NULL,
                loan_amount REAL,
                period_month TEXT,
                period_year INTEGER,
                period_acquired TEXT, -- "Month Year" format
                remarks TEXT DEFAULT '',
                is_duplicate BOOLEAN DEFAULT 0,
                source_session TEXT, -- From Payroll Audit
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // External Loans
            `CREATE TABLE IF NOT EXISTS external_loans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                loan_type TEXT NOT NULL,
                loan_amount REAL,
                period_month TEXT,
                period_year INTEGER,
                period_acquired TEXT, -- "Month Year" format
                source_session TEXT, -- From Payroll Audit
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Leave Claims (Remarks: Flagging duplicate rules apply)
            `CREATE TABLE IF NOT EXISTS leave_claims (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                allowance_type TEXT NOT NULL,
                payable_amount REAL,
                period_month TEXT,
                period_year INTEGER,
                period_acquired TEXT, -- "Month Year" format
                remarks TEXT DEFAULT '', -- Flagging duplicate rules apply
                is_duplicate BOOLEAN DEFAULT 0,
                source_session TEXT, -- From Bank Adviser
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Educational Subsidy (Remarks: Flagging duplicate rules apply)
            `CREATE TABLE IF NOT EXISTS educational_subsidy (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                allowance_type TEXT NOT NULL,
                payable_amount REAL,
                period_month TEXT,
                period_year INTEGER,
                period_acquired TEXT, -- "Month Year" format
                remarks TEXT DEFAULT '', -- Flagging duplicate rules apply
                is_duplicate BOOLEAN DEFAULT 0,
                source_session TEXT, -- From Bank Adviser
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Long Service Awards (Remarks: Flagging duplicate rules apply)
            `CREATE TABLE IF NOT EXISTS long_service_awards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                award_type TEXT NOT NULL,
                payable_amount REAL,
                period_month TEXT,
                period_year INTEGER,
                period_acquired TEXT, -- "Month Year" format
                remarks TEXT DEFAULT '',
                is_duplicate BOOLEAN DEFAULT 0,
                source_session TEXT, -- From Bank Adviser
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Motor Vehicle Maintenance (Remarks: Flagging duplicate rules apply)
            `CREATE TABLE IF NOT EXISTS motor_vehicle_maintenance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_no TEXT NOT NULL,
                employee_name TEXT,
                department TEXT,
                allowance_type TEXT NOT NULL,
                payable_amount REAL,
                period_month TEXT,
                period_year INTEGER,
                period_acquired TEXT, -- "Month Year" format
                remarks TEXT DEFAULT '',
                is_duplicate BOOLEAN DEFAULT 0,
                source_session TEXT, -- From Payroll Audit
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Bank Adviser Settings
            `CREATE TABLE IF NOT EXISTS bank_adviser_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                module_section TEXT DEFAULT 'general',
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Bank Adviser Reports
            `CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_id TEXT UNIQUE NOT NULL,
                report_type TEXT NOT NULL,
                category TEXT, -- Bank Adviser category
                title TEXT NOT NULL,
                description TEXT,
                file_paths TEXT, -- JSON string of file paths
                metadata TEXT, -- JSON string of report metadata
                session_id TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                file_size INTEGER,
                is_archived BOOLEAN DEFAULT 0,
                FOREIGN KEY (session_id) REFERENCES bank_adviser_sessions(session_id)
            )`
        ];

        // Execute table creation
        for (const tableSQL of tables) {
            await this.runQuery(tableSQL);
        }

        // Create indexes for performance
        await this.createIndexes();

        // Run database migrations for existing tables
        await this.runMigrations();

        // Insert default settings
        await this.insertDefaultSettings();

        console.log('✅ Bank Adviser database tables created successfully');
    }

    /**
     * Run database migrations for existing tables
     */
    async runMigrations() {
        try {
            // Check if tax_percentage column exists in allowances_data table
            const allowancesColumns = await this.getQuery("PRAGMA table_info(allowances_data)");
            const hasTaxPercentageAllowances = allowancesColumns.some(col => col.name === 'tax_percentage');

            if (!hasTaxPercentageAllowances) {
                console.log('🔄 Adding tax_percentage column to allowances_data table...');
                await this.runQuery('ALTER TABLE allowances_data ADD COLUMN tax_percentage REAL');
            }

            // Check if tax_percentage column exists in awards_data table
            const awardsColumns = await this.getQuery("PRAGMA table_info(awards_data)");
            const hasTaxPercentageAwards = awardsColumns.some(col => col.name === 'tax_percentage');

            if (!hasTaxPercentageAwards) {
                console.log('🔄 Adding tax_percentage column to awards_data table...');
                await this.runQuery('ALTER TABLE awards_data ADD COLUMN tax_percentage REAL');
            }

            // Silent migration completion to avoid encoding issues
        } catch (error) {
            console.error('❌ Database migration failed:', error);
            // Don't throw error to prevent app from crashing
        }
    }

    /**
     * Create database indexes for performance
     */
    async createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_final_adjusted_employee ON final_adjusted_data(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_allowances_employee ON allowances_data(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_awards_employee ON awards_data(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_bank_advice_employee ON bank_advice_records(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_bank_advice_bank ON bank_advice_records(bank)',
            'CREATE INDEX IF NOT EXISTS idx_in_house_loans_employee ON in_house_loans(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_external_loans_employee ON external_loans(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_leave_claims_employee ON leave_claims(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_educational_subsidy_employee ON educational_subsidy(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_long_service_awards_employee ON long_service_awards(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_motor_vehicle_employee ON motor_vehicle_maintenance(employee_no)',
            'CREATE INDEX IF NOT EXISTS idx_period_tracking ON in_house_loans(period_year, period_month)',
            'CREATE INDEX IF NOT EXISTS idx_duplicate_tracking ON leave_claims(employee_no, allowance_type, period_year)'
        ];

        for (const indexSQL of indexes) {
            await this.runQuery(indexSQL);
        }
    }

    /**
     * Insert default Bank Adviser settings
     */
    async insertDefaultSettings() {
        const defaultSettings = [
            ['default_allowance_tax_rate', '5.0', 'number', 'tax_settings', 'Default tax rate for allowances (%)'],
            ['default_awards_tax_rate', '7.5', 'number', 'tax_settings', 'Default tax rate for awards (%)'],
            ['educational_subsidy_tax_rate', '5.0', 'number', 'tax_settings', 'Tax rate for educational subsidy (%)'],
            ['duplicate_detection_enabled', 'true', 'boolean', 'tracking', 'Enable duplicate detection for tracker'],
            ['auto_generate_remarks', 'true', 'boolean', 'generation', 'Auto-generate PRE-AUDITED remarks'],
            ['default_sections', '["HEADQUARTERS", "INTERNATIONAL MISSIONS", "PENTMEDIA CENTER"]', 'json', 'sections', 'Default available sections'],
            ['bank_advice_sort_primary', 'bank', 'string', 'sorting', 'Primary sort field for bank advice'],
            ['bank_advice_sort_secondary', 'employee_no', 'string', 'sorting', 'Secondary sort field for bank advice']
        ];

        for (const [key, value, type, section, description] of defaultSettings) {
            await this.runQuery(
                `INSERT OR IGNORE INTO bank_adviser_settings
                 (setting_key, setting_value, setting_type, module_section, description)
                 VALUES (?, ?, ?, ?, ?)`,
                [key, value, type, section, description]
            );
        }
    }

    /**
     * Execute a database query
     */
    async runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('❌ Bank Adviser Database query failed:', err);
                    reject(err);
                } else {
                    resolve({ lastID: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * Execute a SELECT query
     */
    async getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('❌ Bank Adviser Database select failed:', err);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Get a single row
     */
    async getOne(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('❌ Bank Adviser Database get one failed:', err);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Close database connection
     */
    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('❌ Error closing Bank Adviser database:', err);
                    } else {
                        console.log('🗄️ Bank Adviser Database closed');
                    }
                    this.isConnected = false;
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = BankAdviserDatabase;
