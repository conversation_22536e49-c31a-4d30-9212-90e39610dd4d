#!/usr/bin/env python3
"""Simple test of the fixed query"""

import sqlite3

def test_query():
    conn = sqlite3.connect('payroll_audit.db')
    cursor = conn.cursor()
    
    try:
        # Get session
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY session_id DESC LIMIT 1")
        session = cursor.fetchone()[0]
        print(f"Testing session: {session}")
        
        # Test the fixed query
        query = '''SELECT cr.id, cr.employee_id, cr.employee_name, cr.section, cr.item_label,
                          cr.previous_value, cr.current_value, cr.change_type, cr.priority_level
                   FROM comparison_results cr
                   JOIN pre_reporting_results pr ON cr.id = pr.change_id
                   WHERE cr.session_id = ? AND pr.selected_for_report = 1'''
        
        cursor.execute(query, (session,))
        results = cursor.fetchall()
        
        print(f"Query results: {len(results)} records")
        
        if results:
            print("✅ SUCCESS! Query is working")
            for i, row in enumerate(results[:3]):
                print(f"  {i+1}. {row[3]}.{row[4]} ({row[7]}) - {row[2]}")
            return True
        else:
            print("❌ FAILED! Query returned 0 results")
            return False
    
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False
    
    finally:
        conn.close()

if __name__ == "__main__":
    test_query()
