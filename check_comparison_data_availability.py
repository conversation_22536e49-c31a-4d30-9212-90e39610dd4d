#!/usr/bin/env python3
"""Check if comparison data is readily available in the secondary source"""

import sqlite3
import os

def check_comparison_data():
    print("🔍 CHECKING COMPARISON DATA AVAILABILITY")
    print("=" * 50)
    
    db_path = 'data/templar_payroll_auditor.db'
    
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Check if comparison_results table exists
        print("1. 📊 CHECKING COMPARISON_RESULTS TABLE:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("   ✅ comparison_results table exists")
            
            # Check total records
            cursor.execute("SELECT COUNT(*) FROM comparison_results")
            total_count = cursor.fetchone()[0]
            print(f"   Total records: {total_count}")
            
            if total_count == 0:
                print("   ❌ NO DATA in comparison_results table")
            else:
                print(f"   ✅ {total_count} records available")
                
                # Check recent sessions
                cursor.execute("SELECT DISTINCT session_id FROM comparison_results ORDER BY session_id DESC LIMIT 5")
                sessions = cursor.fetchall()
                print(f"   Recent sessions: {[s[0] for s in sessions]}")
                
                # Check sample data
                cursor.execute("SELECT * FROM comparison_results LIMIT 3")
                samples = cursor.fetchall()
                print("   Sample records:")
                for i, sample in enumerate(samples):
                    print(f"     {i+1}. {sample}")
        else:
            print("   ❌ comparison_results table does not exist")
        
        # 2. Check for alternative comparison data sources
        print("\n2. 🔍 CHECKING ALTERNATIVE COMPARISON DATA SOURCES:")
        
        # Check all tables that might contain comparison data
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%comparison%'")
        comparison_tables = cursor.fetchall()
        
        if comparison_tables:
            print("   Found comparison-related tables:")
            for table in comparison_tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"     - {table_name}: {count} records")
        else:
            print("   ❌ No comparison-related tables found")
        
        # 3. Check for audit results or processed data
        print("\n3. 📋 CHECKING AUDIT RESULTS/PROCESSED DATA:")
        
        audit_tables = ['audit_results', 'processed_results', 'analysis_results', 'payroll_changes']
        for table_name in audit_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if cursor.fetchone():
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   - {table_name}: {count} records")
        
        # 4. Check what data is actually in pre_reporting_results
        print("\n4. 📊 ANALYZING PRE_REPORTING_RESULTS STRUCTURE:")
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results")
        pr_count = cursor.fetchone()[0]
        print(f"   Total pre_reporting_results: {pr_count}")
        
        # Check if pre_reporting_results contains comparison-like data
        cursor.execute("PRAGMA table_info(pre_reporting_results)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f"   Columns: {column_names}")
        
        # Check for comparison-like fields
        comparison_fields = ['previous_value', 'current_value', 'change_type', 'difference']
        found_fields = [field for field in comparison_fields if field in column_names]
        print(f"   Comparison-like fields: {found_fields}")
        
        if found_fields:
            print("   ✅ pre_reporting_results contains comparison data!")
            
            # Sample the data
            cursor.execute("SELECT * FROM pre_reporting_results LIMIT 3")
            samples = cursor.fetchall()
            print("   Sample records:")
            for i, sample in enumerate(samples):
                print(f"     {i+1}. {sample}")
        
        # 5. Check current session data
        print("\n5. 🎯 CHECKING CURRENT SESSION DATA:")
        
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY created_at DESC LIMIT 1")
        current_session = cursor.fetchone()
        
        if current_session:
            session_id = current_session[0]
            print(f"   Current session: {session_id}")
            
            # Check pre_reporting_results for this session
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_id,))
            pr_session_count = cursor.fetchone()[0]
            print(f"   Pre-reporting records: {pr_session_count}")
            
            # Check comparison_results for this session
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            cr_session_count = cursor.fetchone()[0]
            print(f"   Comparison records: {cr_session_count}")
            
            if cr_session_count == 0 and pr_session_count > 0:
                print("   ⚠️ ISSUE: No comparison data for current session")
                print("   💡 SOLUTION: Use pre_reporting_results as both primary AND secondary source")
            elif cr_session_count > 0:
                print("   ✅ Both data sources available for current session")
            else:
                print("   ❌ No data available for current session")
        
        # 6. Final assessment
        print("\n6. 📋 FINAL ASSESSMENT:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        total_comparison = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results")
        total_pre_reporting = cursor.fetchone()[0]
        
        print(f"   📊 SECONDARY SOURCE (comparison_results): {total_comparison} records")
        print(f"   📊 PRIMARY SOURCE (pre_reporting_results): {total_pre_reporting} records")
        
        if total_comparison == 0:
            print("   ❌ NO SECONDARY DATA READILY AVAILABLE")
            print("   💡 RECOMMENDATION: Use pre_reporting_results for both primary and secondary analysis")
            print("   🔧 ALTERNATIVE: Generate comparison data from pre_reporting_results")
            return False
        else:
            print("   ✅ SECONDARY DATA IS READILY AVAILABLE")
            return True
    
    except Exception as e:
        print(f"❌ Error checking data: {e}")
        return False
    
    finally:
        conn.close()

if __name__ == "__main__":
    has_secondary_data = check_comparison_data()
    
    if has_secondary_data:
        print("\n🎉 CONCLUSION: Secondary comparison data is readily available!")
    else:
        print("\n⚠️ CONCLUSION: No secondary comparison data readily available")
        print("   The hybrid approach needs to be adjusted to work with available data")
