#!/usr/bin/env python3
"""
THE PAYROLL AUDITOR - PDF SORTER INTEGRATION MODULE
Integrates the enterprise PDF sorter with the main application.
Handles IPC communication, progress reporting, and error management.
"""

import os
import sys
import json
import time
import traceback
from typing import Dict, Any, Optional, Callable

# Add core directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

from pdf_sorter import EnterprisePDFSorter, sort_pdf_file, get_pdf_info

class PDFSorterIntegration:
    """Integration layer for PDF Sorter with the main application"""

    def __init__(self, progress_callback: Optional[Callable] = None, debug: bool = False):
        """Initialize the PDF sorter integration"""
        self.progress_callback = progress_callback
        self.current_sorter = None
        self.debug = debug

        if self.debug:
            print("[PDF-SORTER] PDF SORTER INTEGRATION INITIALIZED")

    def sort_pdf(self, pdf_path: str, sort_config: Dict) -> Dict:
        """
        Sort PDF with comprehensive error handling and progress reporting

        Args:
            pdf_path: Path to PDF file
            sort_config: Sorting configuration dictionary

        Returns:
            Dictionary with results or error information
        """
        try:
            if self.debug:
                print(f"[PDF-SORTER] STARTING PDF SORTING OPERATION")
                print(f"   PDF: {pdf_path}")
                print(f"   Config: {sort_config}")

            # Validate inputs
            validation_result = self._validate_inputs(pdf_path, sort_config)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error'],
                    'error_type': 'validation'
                }

            # Create progress wrapper
            def progress_wrapper(data):
                self._handle_progress(data)

            # Initialize sorter
            self.current_sorter = EnterprisePDFSorter(
                progress_callback=progress_wrapper,
                debug=self.debug
            )

            # Perform sorting
            result = self.current_sorter.sort_pdf(pdf_path, sort_config)

            # Add integration metadata
            if result.get('success', False):
                result['integration_version'] = '1.0.0'
                result['sorted_at'] = time.strftime('%Y-%m-%d %H:%M:%S')

                # Send final completion progress update
                final_progress = {
                    'percentage': 100,
                    'message': 'PDF sorting completed successfully!',
                    'status': 'Completed',
                    'processed_pages': result.get('stats', {}).get('processed_pages', 0),
                    'extracted_payslips': result.get('total_payslips', 0),
                    'current_stage': 'Complete',
                    'stats': result.get('stats', {}),
                    'final_result': True
                }
                self._handle_progress(final_progress)

                # Save sorting report
                self._save_sorting_report(result)

            return result

        except Exception as e:
            error_msg = f"PDF sorting integration failed: {str(e)}"
            if self.debug:
                print(f"[ERROR] {error_msg}")
                traceback.print_exc()

            return {
                'success': False,
                'error': error_msg,
                'error_type': 'integration',
                'traceback': traceback.format_exc() if self.debug else None
            }
        finally:
            self.current_sorter = None

    def get_pdf_info(self, pdf_path: str) -> Dict:
        """Get PDF information with enhanced metadata"""
        try:
            if self.debug:
                print(f"[PDF-SORTER] GETTING PDF INFO: {pdf_path}")

            # Get basic PDF info
            info = get_pdf_info(pdf_path)

            if info.get('success', False):
                # Add enhanced metadata
                info['can_sort'] = info['total_pages'] > 0
                info['estimated_processing_time'] = self._estimate_processing_time(info['total_pages'])
                info['memory_requirement'] = self._estimate_memory_requirement(info['file_size_mb'])
                info['recommended_batch_size'] = self._get_recommended_batch_size(info['total_pages'])

                if self.debug:
                    print(f"[SUCCESS] PDF Info retrieved successfully")
                    print(f"   Pages: {info['total_pages']}")
                    print(f"   Size: {info['file_size_mb']} MB")
                    print(f"   Est. time: {info['estimated_processing_time']}s")

            return info

        except Exception as e:
            error_msg = f"Failed to get PDF info: {str(e)}"
            if self.debug:
                print(f"[ERROR] {error_msg}")

            return {
                'success': False,
                'error': error_msg
            }

    def stop_sorting(self) -> Dict:
        """Stop current sorting operation"""
        try:
            if self.current_sorter:
                self.current_sorter.stop_processing()
                if self.debug:
                    print("[STOP] Sorting operation stopped")
                return {'success': True, 'message': 'Sorting stopped'}
            else:
                return {'success': False, 'error': 'No active sorting operation'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _validate_inputs(self, pdf_path: str, sort_config: Dict) -> Dict:
        """Validate inputs for PDF sorting"""
        try:
            # Check if PDF file exists
            if not os.path.exists(pdf_path):
                return {'valid': False, 'error': f'PDF file not found: {pdf_path}'}

            # Check file extension
            if not pdf_path.lower().endswith('.pdf'):
                return {'valid': False, 'error': 'File must be a PDF'}

            # Check file size (max 500MB for safety)
            file_size = os.path.getsize(pdf_path)
            max_size = 500 * 1024 * 1024  # 500MB
            if file_size > max_size:
                return {'valid': False, 'error': f'PDF file too large (max 500MB)'}

            # Validate sort configuration
            required_fields = ['primary_sort', 'sort_order']
            for field in required_fields:
                if field not in sort_config:
                    return {'valid': False, 'error': f'Missing required field: {field}'}

            # Validate sort fields
            valid_sort_fields = ['employee_no', 'employee_name', 'section', 'department', 'job_title']
            if sort_config['primary_sort'] not in valid_sort_fields:
                return {'valid': False, 'error': f'Invalid primary sort field: {sort_config["primary_sort"]}'}

            # Validate sort order
            if sort_config['sort_order'] not in ['ascending', 'descending']:
                return {'valid': False, 'error': f'Invalid sort order: {sort_config["sort_order"]}'}

            # Validate secondary and tertiary sorts if present
            for sort_type in ['secondary_sort', 'tertiary_sort']:
                if sort_type in sort_config and sort_config[sort_type]:
                    if sort_config[sort_type] not in valid_sort_fields:
                        return {'valid': False, 'error': f'Invalid {sort_type}: {sort_config[sort_type]}'}

            return {'valid': True}

        except Exception as e:
            return {'valid': False, 'error': f'Validation error: {str(e)}'}

    def _handle_progress(self, data: Dict):
        """Handle progress updates from the sorter"""
        try:
            # Add timestamp
            data['timestamp'] = time.time()

            # Forward to main application callback
            if self.progress_callback:
                self.progress_callback(data)

            # Log progress if debug enabled
            if self.debug and data.get('percentage', 0) % 20 == 0:
                print(f"[PROGRESS] Progress: {data.get('percentage', 0)}% - {data.get('status', 'Processing...')}")

        except Exception as e:
            if self.debug:
                print(f"[WARNING] Progress handling error: {e}")

    def _estimate_processing_time(self, total_pages: int) -> int:
        """Estimate processing time based on page count"""
        # Base time estimates (in seconds)
        base_time_per_page = 0.5  # 0.5 seconds per page for extraction
        sorting_overhead = 2  # 2 seconds for sorting
        pdf_generation_per_page = 0.1  # 0.1 seconds per page for PDF generation

        estimated_time = (total_pages * base_time_per_page) + sorting_overhead + (total_pages * pdf_generation_per_page)

        # Add buffer for large files
        if total_pages > 1000:
            estimated_time *= 1.5
        elif total_pages > 500:
            estimated_time *= 1.2

        return int(estimated_time)

    def _estimate_memory_requirement(self, file_size_mb: float) -> str:
        """Estimate memory requirement for processing"""
        # Rough estimate: 3x file size for processing
        estimated_mb = file_size_mb * 3

        if estimated_mb < 100:
            return "Low (< 100MB)"
        elif estimated_mb < 500:
            return f"Medium (~{int(estimated_mb)}MB)"
        elif estimated_mb < 1000:
            return f"High (~{int(estimated_mb)}MB)"
        else:
            return f"Very High (~{int(estimated_mb)}MB)"

    def _get_recommended_batch_size(self, total_pages: int) -> int:
        """Get recommended batch size for processing"""
        if total_pages < 100:
            return total_pages  # Process all at once
        elif total_pages < 500:
            return 50
        elif total_pages < 1000:
            return 100
        else:
            return 150  # Larger batches for very large files

    def _save_sorting_report(self, result: Dict):
        """Save sorting report to database and file system"""
        try:
            # Save to file system (legacy support)
            reports_dir = os.path.join(os.path.dirname(__file__), '..', 'data', 'reports', 'PDF_Sorter_Reports')
            os.makedirs(reports_dir, exist_ok=True)

            # Generate report filename
            timestamp = int(time.time())
            report_filename = f"sorting_report_{timestamp}.json"
            report_path = os.path.join(reports_dir, report_filename)

            # Prepare report data
            report_data = {
                'report_type': 'PDF_Sorting_Report',
                'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'source_tab': 'pdf_sorter',
                'result': result.copy()
            }

            # Remove large data to keep report manageable
            if 'preview_items' in report_data['result'] and len(report_data['result']['preview_items']) > 50:
                report_data['result']['preview_items'] = report_data['result']['preview_items'][:50]

            # Save to file system
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            # Also save to database if available
            try:
                # Import the correct database manager
                from core.python_database_manager import PythonDatabaseManager

                # Get database instance
                db = PythonDatabaseManager()
                if db and db.is_connected:
                    # Generate report ID
                    report_id = f"pdf_sorter_{timestamp}_{hash(str(result)) % 10000}"

                    # Prepare file paths
                    file_paths = {}
                    if result.get('output_path'):
                        file_paths['pdf'] = result['output_path']
                    file_paths['json'] = report_path

                    # Calculate file size
                    file_size = 0
                    if result.get('output_path') and os.path.exists(result['output_path']):
                        file_size = os.path.getsize(result['output_path'])

                    # Create title
                    sort_config = result.get('sort_config', {})
                    primary_sort = sort_config.get('primary_sort', 'unknown')
                    title = f"PDF Sorter Report - Sorted by {primary_sort.replace('_', ' ').title()}"

                    # Save to database using the correct method
                    db.save_report(
                        report_id=report_id,
                        report_name=title,
                        report_type='PDF Sorter Report',
                        module_name='PDF Sorter',
                        file_path=result.get('output_path', ''),
                        metadata={
                            'sort_config': sort_config,
                            'total_payslips': result.get('total_payslips', 0),
                            'processing_time': result.get('processing_time', 0),
                            'original_filename': os.path.basename(result.get('input_path', '')),
                            'file_paths': file_paths,
                            'file_size': file_size,
                            'json_report_path': report_path
                        }
                    )

                    if self.debug:
                        print(f"[DATABASE] Report saved to database: {report_id}")

            except Exception as db_error:
                if self.debug:
                    print(f"[WARNING] Failed to save to database: {db_error}")

            if self.debug:
                print(f"[REPORT] Sorting report saved: {report_filename}")

        except Exception as e:
            if self.debug:
                print(f"[WARNING] Failed to save sorting report: {e}")


# Command-line interface for testing
def main():
    """Command-line interface for testing PDF sorter integration"""
    import argparse

    parser = argparse.ArgumentParser(description='PDF Sorter Integration Test')
    parser.add_argument('pdf_path', help='Path to PDF file')
    parser.add_argument('--sort-by', default='employee_no',
                       choices=['employee_no', 'employee_name', 'section', 'department', 'job_title'],
                       help='Primary sort field')
    parser.add_argument('--secondary-sort', default='',
                       choices=['', 'employee_no', 'employee_name', 'section', 'department', 'job_title'],
                       help='Secondary sort field')
    parser.add_argument('--tertiary-sort', default='',
                       choices=['', 'employee_no', 'employee_name', 'section', 'department', 'job_title'],
                       help='Tertiary sort field')
    parser.add_argument('--order', default='ascending', choices=['ascending', 'descending'],
                       help='Sort order')
    parser.add_argument('--info-only', action='store_true', help='Only get PDF info')

    args = parser.parse_args()

    # Create integration instance
    def progress_callback(data):
        print(f"Progress: {data['percentage']}% - {data['status']}")

    integration = PDFSorterIntegration(progress_callback=progress_callback)

    if args.info_only:
        # Get PDF info only
        info = integration.get_pdf_info(args.pdf_path)
        print(json.dumps(info, indent=2))
    else:
        # Perform sorting
        sort_config = {
            'primary_sort': args.sort_by,
            'secondary_sort': args.secondary_sort,
            'tertiary_sort': args.tertiary_sort,
            'sort_order': args.order
        }

        result = integration.sort_pdf(args.pdf_path, sort_config)
        print(json.dumps(result, indent=2))


# Direct execution for IPC integration
if len(sys.argv) > 1 and sys.argv[1] == '--info-only':
    # Handle info-only request
    if len(sys.argv) < 3:
        print(json.dumps({'success': False, 'error': 'PDF path required'}))
        sys.exit(1)

    pdf_path = sys.argv[2]

    # Create integration with debug disabled for clean JSON output
    class QuietPDFSorterIntegration(PDFSorterIntegration):
        def __init__(self):
            self.progress_callback = None
            self.current_sorter = None
            self.debug = False  # Disable debug from the start

    integration = QuietPDFSorterIntegration()
    info = integration.get_pdf_info(pdf_path)
    print(json.dumps(info, indent=2))
    sys.exit(0)

elif len(sys.argv) >= 2:
    # Handle sorting request
    pdf_path = sys.argv[1]

    # Parse arguments
    sort_by = 'employee_no'
    order = 'ascending'
    secondary_sort = ''
    tertiary_sort = ''

    for i, arg in enumerate(sys.argv):
        if arg == '--sort-by' and i + 1 < len(sys.argv):
            sort_by = sys.argv[i + 1]
        elif arg == '--order' and i + 1 < len(sys.argv):
            order = sys.argv[i + 1]
        elif arg == '--secondary-sort' and i + 1 < len(sys.argv):
            secondary_sort = sys.argv[i + 1]
        elif arg == '--tertiary-sort' and i + 1 < len(sys.argv):
            tertiary_sort = sys.argv[i + 1]

    sort_config = {
        'primary_sort': sort_by,
        'secondary_sort': secondary_sort,
        'tertiary_sort': tertiary_sort,
        'sort_order': order
    }

    def progress_callback(data):
        # Output progress for IPC in the format expected by frontend
        progress_data = {
            'percentage': data.get('percentage', 0),
            'message': data.get('message', data.get('status', 'Processing...')),
            'status': data.get('status', data.get('message', 'Processing...')),
            'processed_pages': data.get('processed_pages', 0),
            'extracted_payslips': data.get('extracted_payslips', 0),
            'current_stage': data.get('current_stage', ''),
            'stats': data.get('stats', {})
        }
        print(f"PROGRESS: {json.dumps(progress_data)}", flush=True)

    integration = PDFSorterIntegration(progress_callback=progress_callback)
    result = integration.sort_pdf(pdf_path, sort_config)
    print(json.dumps(result, indent=2))
    sys.exit(0)


if __name__ == "__main__":
    main()
