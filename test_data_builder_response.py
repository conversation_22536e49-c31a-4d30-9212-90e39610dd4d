#!/usr/bin/env python3
"""
Test Data Builder Response Format
Tests the exact response format that's causing parsing issues
"""

import subprocess
import json
import sys

def test_data_builder_response():
    """Test the Data Builder API response format"""
    
    print("🧪 TESTING DATA BUILDER API RESPONSE FORMAT")
    print("=" * 50)
    
    try:
        # 1. Test direct Python call
        print("\n1. 📞 DIRECT PYTHON API CALL:")
        
        result = subprocess.run(
            [sys.executable, 'data_builder_api.py', 'initialize'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print(f"   Return code: {result.returncode}")
        print(f"   Stdout length: {len(result.stdout)} characters")
        print(f"   Stderr length: {len(result.stderr)} characters")
        
        if result.stderr:
            print("   ⚠️ Stderr content:")
            print(f"   {result.stderr[:200]}...")
        
        # 2. Analyze stdout content
        print("\n2. 📄 STDOUT CONTENT ANALYSIS:")
        
        stdout = result.stdout
        print(f"   Raw stdout (first 100 chars): '{stdout[:100]}'")
        print(f"   Raw stdout (last 100 chars): '{stdout[-100:]}'")
        
        # Check for non-printable characters
        non_printable = [c for c in stdout if ord(c) < 32 and c not in '\n\r\t']
        if non_printable:
            print(f"   ⚠️ Non-printable characters found: {[ord(c) for c in non_printable]}")
        else:
            print("   ✅ No problematic non-printable characters")
        
        # 3. Test JSON parsing
        print("\n3. 🔍 JSON PARSING TEST:")
        
        # Test direct parsing
        try:
            parsed_direct = json.loads(stdout)
            print("   ✅ Direct JSON parsing successful")
            print(f"   📋 Parsed keys: {list(parsed_direct.keys())}")
        except json.JSONDecodeError as e:
            print(f"   ❌ Direct JSON parsing failed: {e}")
            print(f"   📍 Error at position: {e.pos}")
            if e.pos < len(stdout):
                print(f"   📄 Character at error: '{stdout[e.pos]}' (ord: {ord(stdout[e.pos])})")
        
        # 4. Test Electron-style cleaning
        print("\n4. 🧹 ELECTRON-STYLE CLEANING TEST:")
        
        # Simulate the cleaning logic from main.js
        clean_result = stdout.strip()
        print(f"   After strip: {len(clean_result)} characters")
        
        # Find JSON start
        json_start = clean_result.find('{')
        if json_start > 0:
            clean_result = clean_result[json_start:]
            print(f"   After finding JSON start: {len(clean_result)} characters")
        
        # Find JSON end
        json_end = clean_result.rfind('}')
        if json_end != -1 and json_end < len(clean_result) - 1:
            clean_result = clean_result[:json_end + 1]
            print(f"   After finding JSON end: {len(clean_result)} characters")
        
        # Test parsing cleaned result
        try:
            parsed_cleaned = json.loads(clean_result)
            print("   ✅ Cleaned JSON parsing successful")
            print(f"   📋 Cleaned parsed keys: {list(parsed_cleaned.keys())}")
        except json.JSONDecodeError as e:
            print(f"   ❌ Cleaned JSON parsing failed: {e}")
            print(f"   📍 Error at position: {e.pos}")
            if e.pos < len(clean_result):
                print(f"   📄 Character at error: '{clean_result[e.pos]}' (ord: {ord(clean_result[e.pos])})")
        
        # 5. Character-by-character analysis around JSON
        print("\n5. 🔬 CHARACTER ANALYSIS:")
        
        if json_start >= 0:
            # Show characters around JSON start
            start_context = max(0, json_start - 10)
            end_context = min(len(stdout), json_start + 50)
            context = stdout[start_context:end_context]
            
            print("   📍 Context around JSON start:")
            for i, char in enumerate(context):
                pos = start_context + i
                if pos == json_start:
                    print(f"   >>> {pos:3d}: '{char}' (ord: {ord(char)}) <<<< JSON START")
                else:
                    print(f"       {pos:3d}: '{char}' (ord: {ord(char)})")
        
        # 6. Test with different parsing strategies
        print("\n6. 🎯 ALTERNATIVE PARSING STRATEGIES:")
        
        # Strategy 1: Remove all whitespace before first {
        try:
            first_brace = stdout.find('{')
            if first_brace >= 0:
                strategy1 = stdout[first_brace:]
                parsed_s1 = json.loads(strategy1)
                print("   ✅ Strategy 1 (from first {) successful")
            else:
                print("   ❌ Strategy 1: No opening brace found")
        except Exception as e:
            print(f"   ❌ Strategy 1 failed: {e}")
        
        # Strategy 2: Split by lines and find JSON lines
        try:
            lines = stdout.split('\n')
            json_lines = [line for line in lines if line.strip().startswith('{') or line.strip().startswith('"')]
            if json_lines:
                strategy2 = '\n'.join(json_lines)
                parsed_s2 = json.loads(strategy2)
                print("   ✅ Strategy 2 (JSON lines only) successful")
            else:
                print("   ❌ Strategy 2: No JSON lines found")
        except Exception as e:
            print(f"   ❌ Strategy 2 failed: {e}")
        
        # 7. Final recommendation
        print("\n7. 💡 RECOMMENDATION:")
        
        if result.returncode == 0:
            if json_start >= 0:
                print("   ✅ Python API is working correctly")
                print("   🔧 Issue is likely in Electron's JSON parsing logic")
                print("   📋 Suggested fixes:")
                print("     1. Use more robust JSON extraction in main.js")
                print("     2. Add better error logging in main.js")
                print("     3. Consider using regex to extract JSON")
            else:
                print("   ❌ Python API is not returning JSON")
                print("   🔧 Issue is in the Python API response format")
        else:
            print("   ❌ Python API call failed")
            print("   🔧 Issue is in the Python API execution")
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_builder_response()
