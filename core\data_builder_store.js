/**
 * DATA BUILDER STORE
 * Comprehensive data management system for processing 2900+ employees
 * Solves UI freezing, data deficits, and large-scale processing issues
 */

class DataBuilderStore {
    constructor() {
        this.state = {
            // Processing State Management
            isProcessing: false,
            currentProgress: 0,
            totalEmployees: 0,
            processedEmployees: 0,
            currentBatch: 0,
            totalBatches: 0,

            // Data Management
            extractedData: new Map(), // employee_id -> complete_extracted_data
            columnDefinitions: new Map(), // column_name -> {section, format, type, etc}
            sectionPrefixes: new Map(), // item_name -> [sections] for duplicates
            mandatoryFields: new Set(['NET PAY', 'GROSS SALARY', 'TAXABLE SALARY', 'TOTAL DEDUCTIONS', 'DEPARTMENT']),

            // Performance Optimization
            batchSize: 50, // Process 50 employees at a time to prevent UI freezing
            processedBatches: [],
            memoryOptimization: true,

            // Excel Output Management
            excelColumns: [], // Final column structure for Excel
            populatedRows: [], // All employee data rows
            columnMapping: new Map(), // extracted_field -> excel_column

            // Error Handling & Quality Assurance
            processingErrors: [],
            dataDeficits: [], // Track missing data for quality control
            validationResults: {},

            // Real-time Updates
            lastUpdate: null,
            updateCallbacks: [],

            // Settings
            enableSectionPrefixes: true,
            enableRealTimeUpdates: true,
            enableDataValidation: true
        };

        this.initializeStore();
    }

    /**
     * Initialize the Data Builder Store
     */
    initializeStore() {
        console.log('🏗️ DATA BUILDER STORE INITIALIZED');
        console.log('✅ Ready for large-scale processing (2900+ employees)');
        console.log('✅ UI freezing prevention enabled');
        console.log('✅ Zero data loss architecture active');

        this.setupMandatoryFields();
        this.setupEventListeners();
    }

    /**
     * Setup mandatory fields that must be present in every payslip
     */
    setupMandatoryFields() {
        const mandatoryFieldsConfig = {
            'NET PAY': { section: 'EARNINGS', format: 'currency', required: true },
            'GROSS SALARY': { section: 'EARNINGS', format: 'currency', required: true },
            'TAXABLE SALARY': { section: 'DEDUCTIONS', format: 'currency', required: true },
            'TOTAL DEDUCTIONS': { section: 'DEDUCTIONS', format: 'currency', required: true },
            'DEPARTMENT': { section: 'PERSONAL DETAILS', format: 'text', required: true },
            'EMPLOYEE NO.': { section: 'PERSONAL DETAILS', format: 'alphanumeric', required: true },
            'EMPLOYEE NAME': { section: 'PERSONAL DETAILS', format: 'text', required: true }
        };

        for (const [field, config] of Object.entries(mandatoryFieldsConfig)) {
            this.state.columnDefinitions.set(field, config);
        }
    }

    /**
     * Setup event listeners for real-time updates
     */
    setupEventListeners() {
        // Real-time progress updates
        this.onProgressUpdate = this.onProgressUpdate.bind(this);
        this.onDataUpdate = this.onDataUpdate.bind(this);
        this.onError = this.onError.bind(this);
    }

    /**
     * Start processing employees with Perfect Section-Aware Extractor
     * @param {Array} employeeFiles - Array of employee payslip files
     * @param {Object} options - Processing options
     */
    async startProcessing(employeeFiles, options = {}) {
        try {
            console.log(`🚀 STARTING DATA BUILDER PROCESSING: ${employeeFiles.length} employees`);

            // Reset state
            this.resetProcessingState();

            // Configure processing
            this.state.totalEmployees = employeeFiles.length;
            this.state.totalBatches = Math.ceil(employeeFiles.length / this.state.batchSize);
            this.state.isProcessing = true;

            // Notify UI
            this.notifyUpdate('processing_started', {
                totalEmployees: this.state.totalEmployees,
                totalBatches: this.state.totalBatches,
                batchSize: this.state.batchSize
            });

            // Process in batches to prevent UI freezing
            for (let i = 0; i < employeeFiles.length; i += this.state.batchSize) {
                const batch = employeeFiles.slice(i, i + this.state.batchSize);
                await this.processBatch(batch, Math.floor(i / this.state.batchSize) + 1);

                // Allow UI to update between batches
                await this.sleep(100);
            }

            // Generate final Excel structure
            await this.generateExcelStructure();

            // Complete processing
            this.completeProcessing();

            return {
                success: true,
                totalProcessed: this.state.processedEmployees,
                totalColumns: this.state.excelColumns.length,
                dataDeficits: this.state.dataDeficits.length,
                processingTime: Date.now() - this.state.lastUpdate
            };

        } catch (error) {
            console.error('❌ DATA BUILDER PROCESSING ERROR:', error);
            this.handleError(error);
            throw error;
        }
    }

    /**
     * Process a batch of employees
     * @param {Array} batch - Batch of employee files
     * @param {number} batchNumber - Current batch number
     */
    async processBatch(batch, batchNumber) {
        console.log(`📦 PROCESSING BATCH ${batchNumber}/${this.state.totalBatches} (${batch.length} employees)`);

        this.state.currentBatch = batchNumber;

        for (const employeeFile of batch) {
            try {
                // Extract data using Perfect Section-Aware Extractor
                const extractedData = await this.extractEmployeeData(employeeFile);

                // Process and store the data
                await this.processEmployeeData(extractedData);

                // Update progress
                this.state.processedEmployees++;
                this.state.currentProgress = (this.state.processedEmployees / this.state.totalEmployees) * 100;

                // Real-time UI update
                this.notifyUpdate('progress', {
                    processed: this.state.processedEmployees,
                    total: this.state.totalEmployees,
                    progress: this.state.currentProgress,
                    currentBatch: this.state.currentBatch
                });

            } catch (error) {
                console.error(`❌ Error processing employee ${employeeFile}:`, error);
                this.state.processingErrors.push({
                    file: employeeFile,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        // Mark batch as completed
        this.state.processedBatches.push({
            batchNumber,
            employeeCount: batch.length,
            completedAt: new Date().toISOString()
        });
    }

    /**
     * Extract employee data using Perfect Section-Aware Extractor
     * @param {string} employeeFile - Path to employee payslip file
     */
    async extractEmployeeData(employeeFile) {
        // This will integrate with the Perfect Section-Aware Extractor
        // For now, return a placeholder structure
        return {
            employeeId: 'COP0001', // Will be extracted
            personalDetails: {},
            earnings: {},
            deductions: {},
            loans: {},
            employersContribution: {},
            bankDetails: {},
            extractedItems: [], // All items extracted by Perfect Section-Aware Extractor
            metadata: {
                extractedAt: new Date().toISOString(),
                source: 'Perfect Section-Aware Extractor',
                confidence: 1.0
            }
        };
    }

    /**
     * Process individual employee data
     * @param {Object} extractedData - Data extracted from payslip
     */
    async processEmployeeData(extractedData) {
        const employeeId = extractedData.employeeId;

        // Store complete extracted data
        this.state.extractedData.set(employeeId, extractedData);

        // Process all extracted items for column creation
        for (const item of extractedData.extractedItems) {
            await this.processExtractedItem(item, employeeId);
        }

        // Validate mandatory fields
        this.validateMandatoryFields(extractedData);
    }

    /**
     * Process individual extracted item for column creation
     * @param {Object} item - Extracted item from Perfect Section-Aware Extractor
     * @param {string} employeeId - Employee identifier
     */
    async processExtractedItem(item, employeeId) {
        const { label, value, section, confidence } = item;

        // Handle section prefixes for duplicate items
        const columnName = this.resolveColumnName(label, section);

        // Create or update column definition
        if (!this.state.columnDefinitions.has(columnName)) {
            this.state.columnDefinitions.set(columnName, {
                originalLabel: label,
                section: section,
                format: this.detectFormat(value),
                valueType: this.detectValueType(value),
                includeInReport: true,
                firstSeenIn: employeeId,
                occurrenceCount: 1
            });
        } else {
            // Update occurrence count
            const existing = this.state.columnDefinitions.get(columnName);
            existing.occurrenceCount++;
        }

        // Track section duplicates
        if (this.state.sectionPrefixes.has(label)) {
            const sections = this.state.sectionPrefixes.get(label);
            if (!sections.includes(section)) {
                sections.push(section);
            }
        } else {
            this.state.sectionPrefixes.set(label, [section]);
        }
    }

    /**
     * Resolve column name with section prefixes for duplicates
     * @param {string} label - Original item label
     * @param {string} section - Section where item was found
     */
    resolveColumnName(label, section) {
        // Check if this item appears in multiple sections
        const sections = this.state.sectionPrefixes.get(label) || [section];

        if (sections.length > 1 && this.state.enableSectionPrefixes) {
            // Use section prefix for duplicates
            return `${section}.${label}`;
        }

        return label;
    }

    /**
     * Detect format of extracted value
     * @param {string} value - Extracted value
     */
    detectFormat(value) {
        if (/^\d+(\.\d{2})?$/.test(value.replace(/,/g, ''))) {
            return 'currency';
        } else if (/^[A-Z0-9]+$/.test(value)) {
            return 'alphanumeric';
        } else if (/^\d+$/.test(value)) {
            return 'numeric';
        } else {
            return 'text';
        }
    }

    /**
     * Detect value type for Excel formatting
     * @param {string} value - Extracted value
     */
    detectValueType(value) {
        if (/^\d+(\.\d+)?$/.test(value.replace(/,/g, ''))) {
            return 'number';
        } else {
            return 'string';
        }
    }

    /**
     * Validate mandatory fields for an employee
     * @param {Object} extractedData - Employee's extracted data
     */
    validateMandatoryFields(extractedData) {
        const missingFields = [];

        for (const mandatoryField of this.state.mandatoryFields) {
            const found = extractedData.extractedItems.some(item =>
                item.label.toUpperCase() === mandatoryField.toUpperCase()
            );

            if (!found) {
                missingFields.push(mandatoryField);
            }
        }

        if (missingFields.length > 0) {
            this.state.dataDeficits.push({
                employeeId: extractedData.employeeId,
                missingFields: missingFields,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Sleep function for UI update intervals
     * @param {number} ms - Milliseconds to sleep
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Reset processing state
     */
    resetProcessingState() {
        this.state.isProcessing = false;
        this.state.currentProgress = 0;
        this.state.processedEmployees = 0;
        this.state.currentBatch = 0;
        this.state.extractedData.clear();
        this.state.processingErrors = [];
        this.state.dataDeficits = [];
        this.state.processedBatches = [];
        this.state.lastUpdate = Date.now();
    }

    /**
     * Notify UI of updates
     * @param {string} type - Update type
     * @param {Object} data - Update data
     */
    notifyUpdate(type, data) {
        const update = {
            type,
            data,
            timestamp: new Date().toISOString()
        };

        // Call all registered callbacks
        this.state.updateCallbacks.forEach(callback => {
            try {
                callback(update);
            } catch (error) {
                console.error('Error in update callback:', error);
            }
        });
    }

    /**
     * Register update callback
     * @param {Function} callback - Callback function
     */
    onUpdate(callback) {
        this.state.updateCallbacks.push(callback);
    }

    /**
     * Handle processing errors
     * @param {Error} error - Error object
     */
    handleError(error) {
        this.state.isProcessing = false;
        this.notifyUpdate('error', {
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Progress update handler
     * @param {Object} progress - Progress data
     */
    onProgressUpdate(progress) {
        this.state.currentProgress = progress.percentage;
        this.notifyUpdate('progress', progress);
    }

    /**
     * Data update handler
     * @param {Object} data - Updated data
     */
    onDataUpdate(data) {
        this.notifyUpdate('data_update', data);
    }

    /**
     * Error handler
     * @param {Error} error - Error object
     */
    onError(error) {
        this.state.processingErrors.push({
            error: error.message,
            timestamp: new Date().toISOString()
        });
        this.notifyUpdate('error', error);
    }

    /**
     * Generate Excel structure with all columns and data
     */
    async generateExcelStructure() {
        console.log('📊 GENERATING EXCEL STRUCTURE...');

        // Create column headers from all discovered items
        this.createExcelColumns();

        // Populate all employee rows
        await this.populateEmployeeRows();

        // Apply Excel formatting
        this.applyExcelFormatting();

        console.log(`✅ EXCEL STRUCTURE COMPLETE: ${this.state.excelColumns.length} columns, ${this.state.populatedRows.length} rows`);
    }

    /**
     * Create Excel columns from all discovered items
     */
    createExcelColumns() {
        const columns = [];

        // Start with mandatory fields in specific order
        const mandatoryOrder = [
            'EMPLOYEE NO.', 'EMPLOYEE NAME', 'DEPARTMENT', 'SECTION', 'JOB TITLE',
            'GROSS SALARY', 'NET PAY', 'TAXABLE SALARY', 'TOTAL DEDUCTIONS'
        ];

        // Add mandatory columns first
        for (const mandatoryField of mandatoryOrder) {
            if (this.state.columnDefinitions.has(mandatoryField)) {
                const definition = this.state.columnDefinitions.get(mandatoryField);
                columns.push(this.createColumnDefinition(mandatoryField, definition));
            }
        }

        // Add all other discovered columns
        for (const [columnName, definition] of this.state.columnDefinitions) {
            if (!mandatoryOrder.includes(columnName)) {
                columns.push(this.createColumnDefinition(columnName, definition));
            }
        }

        this.state.excelColumns = columns;

        // Create column mapping for data population
        this.state.columnMapping.clear();
        columns.forEach((col, index) => {
            this.state.columnMapping.set(col.key, index);
        });
    }

    /**
     * Create individual column definition for Excel
     * @param {string} columnName - Column name
     * @param {Object} definition - Column definition
     */
    createColumnDefinition(columnName, definition) {
        return {
            key: columnName,
            header: columnName,
            width: this.calculateColumnWidth(columnName, definition),
            style: this.getColumnStyle(definition),
            format: definition.format,
            section: definition.section,
            occurrences: definition.occurrenceCount || 1
        };
    }

    /**
     * Calculate optimal column width
     * @param {string} columnName - Column name
     * @param {Object} definition - Column definition
     */
    calculateColumnWidth(columnName, definition) {
        const baseWidth = Math.max(columnName.length, 10);

        if (definition.format === 'currency') {
            return Math.max(baseWidth, 15);
        } else if (definition.format === 'alphanumeric') {
            return Math.max(baseWidth, 12);
        } else {
            return Math.max(baseWidth, 20);
        }
    }

    /**
     * Get Excel style for column based on format
     * @param {Object} definition - Column definition
     */
    getColumnStyle(definition) {
        const baseStyle = {
            alignment: { horizontal: 'left', vertical: 'middle' },
            border: {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            }
        };

        if (definition.format === 'currency') {
            baseStyle.numFmt = '#,##0.00';
            baseStyle.alignment.horizontal = 'right';
        } else if (definition.format === 'numeric') {
            baseStyle.numFmt = '#,##0';
            baseStyle.alignment.horizontal = 'right';
        }

        return baseStyle;
    }

    /**
     * Populate all employee rows with extracted data
     */
    async populateEmployeeRows() {
        console.log('📝 POPULATING EMPLOYEE ROWS...');

        const rows = [];

        for (const [employeeId, extractedData] of this.state.extractedData) {
            const row = this.createEmployeeRow(employeeId, extractedData);
            rows.push(row);

            // Progress update for large datasets
            if (rows.length % 100 === 0) {
                this.notifyUpdate('excel_generation_progress', {
                    processed: rows.length,
                    total: this.state.extractedData.size
                });
                await this.sleep(10); // Prevent UI blocking
            }
        }

        this.state.populatedRows = rows;
    }

    /**
     * Create individual employee row
     * @param {string} employeeId - Employee ID
     * @param {Object} extractedData - Employee's extracted data
     */
    createEmployeeRow(employeeId, extractedData) {
        const row = {};

        // Initialize all columns with empty values
        for (const column of this.state.excelColumns) {
            row[column.key] = '';
        }

        // Populate with extracted data
        for (const item of extractedData.extractedItems) {
            const columnName = this.resolveColumnName(item.label, item.section);

            if (this.state.columnMapping.has(columnName)) {
                row[columnName] = this.formatCellValue(item.value, item.format);
            }
        }

        // Ensure employee ID is set
        row['EMPLOYEE NO.'] = employeeId;

        return row;
    }

    /**
     * Format cell value based on detected format
     * @param {string} value - Raw value
     * @param {string} format - Detected format
     */
    formatCellValue(value, format) {
        if (!value || value === 'N/A' || value === 'Not Applicable') {
            return '';
        }

        if (format === 'currency' || format === 'numeric') {
            // Remove commas and convert to number
            const numericValue = parseFloat(value.replace(/,/g, ''));
            return isNaN(numericValue) ? value : numericValue;
        }

        return value;
    }

    /**
     * Apply Excel formatting and freeze panes
     */
    applyExcelFormatting() {
        // Header row formatting
        const headerStyle = {
            font: { bold: true, color: { argb: 'FFFFFF' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '366092' } },
            alignment: { horizontal: 'center', vertical: 'middle' },
            border: {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            }
        };

        // Store formatting configuration
        this.state.excelFormatting = {
            headerStyle,
            freezePane: 'A2', // Freeze header row
            autoFilter: true,
            columnWidths: this.state.excelColumns.map(col => col.width)
        };
    }

    /**
     * Complete processing and generate final report
     */
    completeProcessing() {
        this.state.isProcessing = false;
        this.state.currentProgress = 100;

        const summary = {
            totalEmployees: this.state.totalEmployees,
            processedEmployees: this.state.processedEmployees,
            totalColumns: this.state.excelColumns.length,
            dataDeficits: this.state.dataDeficits.length,
            processingErrors: this.state.processingErrors.length,
            uniqueItems: this.state.columnDefinitions.size,
            sectionDuplicates: Array.from(this.state.sectionPrefixes.entries())
                .filter(([_, sections]) => sections.length > 1).length,
            completedAt: new Date().toISOString()
        };

        console.log('🎉 DATA BUILDER PROCESSING COMPLETE!');
        console.log('📊 SUMMARY:', summary);

        this.notifyUpdate('processing_complete', summary);

        return summary;
    }

    /**
     * Get processing summary
     */
    getProcessingSummary() {
        return {
            isProcessing: this.state.isProcessing,
            progress: this.state.currentProgress,
            totalEmployees: this.state.totalEmployees,
            processedEmployees: this.state.processedEmployees,
            totalColumns: this.state.excelColumns.length,
            dataDeficits: this.state.dataDeficits.length,
            errors: this.state.processingErrors.length
        };
    }

    /**
     * Get Excel data for export
     */
    getExcelData() {
        return {
            columns: this.state.excelColumns,
            rows: this.state.populatedRows,
            formatting: this.state.excelFormatting,
            summary: this.getProcessingSummary()
        };
    }

    /**
     * Get data deficits report
     */
    getDataDeficitsReport() {
        return {
            totalDeficits: this.state.dataDeficits.length,
            deficitsByField: this.groupDeficitsByField(),
            deficitsByEmployee: this.state.dataDeficits,
            recommendations: this.generateDeficitRecommendations()
        };
    }

    /**
     * Group data deficits by missing field
     */
    groupDeficitsByField() {
        const grouped = {};

        for (const deficit of this.state.dataDeficits) {
            for (const field of deficit.missingFields) {
                if (!grouped[field]) {
                    grouped[field] = [];
                }
                grouped[field].push(deficit.employeeId);
            }
        }

        return grouped;
    }

    /**
     * Generate recommendations for data deficits
     */
    generateDeficitRecommendations() {
        const recommendations = [];
        const deficitsByField = this.groupDeficitsByField();

        for (const [field, employees] of Object.entries(deficitsByField)) {
            const percentage = (employees.length / this.state.totalEmployees) * 100;

            if (percentage > 50) {
                recommendations.push({
                    type: 'critical',
                    field: field,
                    affectedEmployees: employees.length,
                    percentage: percentage.toFixed(1),
                    recommendation: `Critical: ${field} is missing in ${percentage.toFixed(1)}% of payslips. Check extraction rules.`
                });
            } else if (percentage > 10) {
                recommendations.push({
                    type: 'warning',
                    field: field,
                    affectedEmployees: employees.length,
                    percentage: percentage.toFixed(1),
                    recommendation: `Warning: ${field} is missing in ${percentage.toFixed(1)}% of payslips. Review payslip variations.`
                });
            }
        }

        return recommendations;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataBuilderStore;
} else if (typeof window !== 'undefined') {
    window.DataBuilderStore = DataBuilderStore;
}
