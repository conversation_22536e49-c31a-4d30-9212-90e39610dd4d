#!/usr/bin/env python3
"""
Database-Driven Auto-Learning Section Change API
Handles section changes for items stored in the database
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def get_current_session_id():
    """Get current session ID"""
    try:
        sys.path.append(os.path.dirname(__file__))
        from core.session_manager import get_current_session_id
        return get_current_session_id()
    except:
        # Fallback to latest session
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None

def update_item_section(item_id, new_section):
    """Update an item's section in the database"""
    try:
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # First check if item exists (without session filter)
        cursor.execute("""
            SELECT id, session_id, section_name, item_label
            FROM auto_learning_results
            WHERE id = ?
        """, (item_id,))

        item = cursor.fetchone()
        if not item:
            return {"success": False, "error": f"Item with ID {item_id} not found"}

        item_session = item[1]
        old_section = item[2]
        item_label = item[3]

        # Update the section (use the item's actual session)
        cursor.execute("""
            UPDATE auto_learning_results
            SET section_name = ?, confidence_score = 0.95
            WHERE id = ?
        """, (new_section, item_id))

        conn.commit()
        conn.close()

        return {
            "success": True,
            "message": f"Updated {item_label} from {old_section} to {new_section}",
            "item_id": item_id,
            "old_section": old_section,
            "new_section": new_section,
            "session_id": item_session
        }

    except Exception as e:
        return {"success": False, "error": str(e)}

def get_pending_auto_learning_items():
    """Get pending items from database - get from all sessions for UI display"""
    try:
        db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get pending items from all sessions (for UI display)
        cursor.execute("""
            SELECT id, session_id, section_name, item_label, confidence_score, auto_approved
            FROM auto_learning_results
            WHERE auto_approved = 0
            ORDER BY session_id DESC, section_name, item_label
        """)

        items = []
        for row in cursor.fetchall():
            items.append({
                "id": row[0],
                "session_id": row[1],
                "section": row[2],
                "item_name": row[3],
                "confidence": row[4],
                "status": "pending"
            })

        conn.close()

        return {"success": True, "items": items, "count": len(items)}

    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'update-section' and len(sys.argv) > 3:
            item_id = sys.argv[2]
            new_section = sys.argv[3]
            result = update_item_section(item_id, new_section)
            print(json.dumps(result))
            
        elif command == 'get-pending':
            result = get_pending_auto_learning_items()
            print(json.dumps(result))
            
        else:
            print(json.dumps({"success": False, "error": "Invalid command"}))
