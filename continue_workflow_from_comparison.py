#!/usr/bin/env python3
"""Continue the workflow from comparison phase"""

import sys
import os
import sqlite3
import json

def continue_workflow_from_comparison():
    """Continue the audit workflow from comparison phase"""
    print("🔄 CONTINUING WORKFLOW FROM COMPARISON PHASE")
    print("=" * 50)
    
    try:
        # Get current session
        conn = sqlite3.connect('data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session_result = cursor.fetchone()
        if not session_result:
            print("❌ No current session found")
            return False
            
        session_id = session_result[0]
        print(f"📋 Current session: {session_id}")
        
        # Check extraction status
        cursor.execute('''
            SELECT status, data_count FROM session_phases 
            WHERE session_id = ? AND phase_name = ?
        ''', (session_id, 'EXTRACTION'))
        extraction_result = cursor.fetchone()
        
        if not extraction_result or extraction_result[0] != 'COMPLETED':
            print("❌ Extraction phase not completed")
            return False
            
        print(f"✅ Extraction completed with {extraction_result[1]} records")
        
        # Check comparison status
        cursor.execute('''
            SELECT status FROM session_phases 
            WHERE session_id = ? AND phase_name = ?
        ''', (session_id, 'COMPARISON'))
        comparison_result = cursor.fetchone()
        
        if comparison_result and comparison_result[0] == 'COMPLETED':
            print("✅ Comparison already completed")
            return True
            
        print("🔄 Starting comparison phase...")
        
        # Get session data
        cursor.execute('''
            SELECT current_pdf_path, previous_pdf_path 
            FROM audit_sessions 
            WHERE session_id = ?
        ''', (session_id,))
        session_data = cursor.fetchone()
        
        if not session_data:
            print("❌ Session data not found")
            return False
            
        current_pdf, previous_pdf = session_data
        print(f"📄 Current PDF: {current_pdf}")
        print(f"📄 Previous PDF: {previous_pdf}")
        
        conn.close()
        
        # Import and run the phased process manager
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Set up options
        options = {
            'currentMonth': 7,
            'currentYear': 2025,
            'previousMonth': 6,
            'previousYear': 2025,
            'signatureName': 'System Administrator',
            'signatureDesignation': 'Payroll Auditor'
        }
        
        print("\n🔄 Running comparison phase...")
        
        # Update comparison status to IN_PROGRESS
        manager.db_manager.execute_update('''
            UPDATE session_phases 
            SET status = ?, started_at = datetime('now') 
            WHERE session_id = ? AND phase_name = ?
        ''', ('IN_PROGRESS', session_id, 'COMPARISON'))
        
        # Run comparison phase
        success = manager._phase_comparison(options)
        
        if success:
            print("✅ Comparison phase completed successfully")
            
            # Continue with remaining phases
            remaining_phases = [
                ('AUTO_LEARNING', manager._phase_auto_learning),
                ('TRACKER_FEEDING', manager._phase_tracker_feeding),
                ('PRE_REPORTING', manager._phase_pre_reporting)
            ]
            
            for phase_name, phase_function in remaining_phases:
                print(f"\n🔄 Running {phase_name} phase...")
                
                # Update phase status to IN_PROGRESS
                manager.db_manager.execute_update('''
                    UPDATE session_phases 
                    SET status = ?, started_at = datetime('now') 
                    WHERE session_id = ? AND phase_name = ?
                ''', ('IN_PROGRESS', session_id, phase_name))
                
                # Run the phase
                phase_success = phase_function(options)
                
                if phase_success:
                    print(f"✅ {phase_name} phase completed successfully")
                    
                    # Update phase status to COMPLETED
                    data_count = manager._get_phase_data_count(phase_name)
                    manager.db_manager.execute_update('''
                        UPDATE session_phases 
                        SET status = ?, completed_at = datetime('now'), data_count = ? 
                        WHERE session_id = ? AND phase_name = ?
                    ''', ('COMPLETED', data_count, session_id, phase_name))
                else:
                    print(f"❌ {phase_name} phase failed")
                    return False
            
            # Update session status
            manager.db_manager.execute_update('''
                UPDATE audit_sessions 
                SET status = ? 
                WHERE session_id = ?
            ''', ('pre_reporting_ready', session_id))
            
            print("\n🎉 SUCCESS: Workflow completed successfully!")
            print("📋 Session is now ready for pre-reporting review")
            return True
            
        else:
            print("❌ Comparison phase failed")
            return False
            
    except Exception as e:
        print(f"❌ Error continuing workflow: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    continue_workflow_from_comparison()
