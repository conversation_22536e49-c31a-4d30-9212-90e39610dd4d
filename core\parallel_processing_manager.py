#!/usr/bin/env python3
"""
Parallel Processing Manager
Implements parallel processing for large datasets with intelligent chunking
"""

import time
import threading
from typing import List, Dict, Any, Callable, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from dataclasses import dataclass
import multiprocessing
import math

@dataclass
class ProcessingResult:
    """Result of parallel processing operation"""
    success: bool
    processed_count: int
    failed_count: int
    execution_time: float
    results: List[Any] = None
    errors: List[str] = None
    chunk_stats: Dict[str, Any] = None

class ParallelProcessingManager:
    """
    Manages parallel processing with intelligent chunking and load balancing
    """
    
    def __init__(self, max_workers: Optional[int] = None, use_processes: bool = False):
        self.max_workers = max_workers or min(8, multiprocessing.cpu_count())
        self.use_processes = use_processes
        self.processing_stats = {}
        
    def process_employees_parallel(self, employees: List[Dict], 
                                 processing_function: Callable,
                                 chunk_size: Optional[int] = None,
                                 **kwargs) -> ProcessingResult:
        """
        Process employees in parallel chunks
        
        Args:
            employees: List of employee data
            processing_function: Function to process each chunk
            chunk_size: Size of each chunk (auto-calculated if None)
            **kwargs: Additional arguments for processing function
            
        Returns:
            ProcessingResult with operation statistics
        """
        start_time = time.time()
        
        if not employees:
            return ProcessingResult(True, 0, 0, 0.0, [], [])
        
        # Calculate optimal chunk size
        if chunk_size is None:
            chunk_size = self._calculate_optimal_chunk_size(len(employees))
        
        # Create chunks
        chunks = self._create_chunks(employees, chunk_size)
        
        print(f"🔄 Processing {len(employees)} employees in {len(chunks)} chunks")
        print(f"   Workers: {self.max_workers}, Chunk size: {chunk_size}")
        
        # Process chunks in parallel
        results = []
        errors = []
        processed_count = 0
        failed_count = 0
        chunk_stats = {}
        
        executor_class = ProcessPoolExecutor if self.use_processes else ThreadPoolExecutor
        
        try:
            with executor_class(max_workers=self.max_workers) as executor:
                # Submit all chunks
                future_to_chunk = {
                    executor.submit(self._process_chunk_wrapper, 
                                  chunk, processing_function, i, **kwargs): i
                    for i, chunk in enumerate(chunks)
                }
                
                # Collect results
                for future in as_completed(future_to_chunk):
                    chunk_index = future_to_chunk[future]
                    
                    try:
                        chunk_result = future.result()
                        
                        if chunk_result['success']:
                            results.extend(chunk_result['results'])
                            processed_count += chunk_result['processed_count']
                        else:
                            failed_count += chunk_result['failed_count']
                            errors.extend(chunk_result['errors'])
                        
                        chunk_stats[f'chunk_{chunk_index}'] = {
                            'size': len(chunks[chunk_index]),
                            'execution_time': chunk_result['execution_time'],
                            'success': chunk_result['success']
                        }
                        
                        print(f"   ✅ Chunk {chunk_index + 1}/{len(chunks)} completed "
                              f"({chunk_result['processed_count']} items, "
                              f"{chunk_result['execution_time']:.2f}s)")
                        
                    except Exception as e:
                        failed_count += len(chunks[chunk_index])
                        errors.append(f"Chunk {chunk_index} failed: {str(e)}")
                        print(f"   ❌ Chunk {chunk_index + 1}/{len(chunks)} failed: {e}")
        
        except Exception as e:
            return ProcessingResult(
                success=False,
                processed_count=0,
                failed_count=len(employees),
                execution_time=time.time() - start_time,
                results=[],
                errors=[f"Parallel processing failed: {str(e)}"]
            )
        
        execution_time = time.time() - start_time
        
        return ProcessingResult(
            success=failed_count == 0,
            processed_count=processed_count,
            failed_count=failed_count,
            execution_time=execution_time,
            results=results,
            errors=errors,
            chunk_stats=chunk_stats
        )
    
    def _process_chunk_wrapper(self, chunk: List[Dict], processing_function: Callable,
                              chunk_index: int, **kwargs) -> Dict[str, Any]:
        """
        Wrapper for processing a single chunk
        """
        start_time = time.time()
        
        try:
            # Process the chunk
            chunk_results = processing_function(chunk, **kwargs)
            
            return {
                'success': True,
                'processed_count': len(chunk),
                'failed_count': 0,
                'results': chunk_results if isinstance(chunk_results, list) else [chunk_results],
                'errors': [],
                'execution_time': time.time() - start_time
            }
            
        except Exception as e:
            return {
                'success': False,
                'processed_count': 0,
                'failed_count': len(chunk),
                'results': [],
                'errors': [f"Chunk processing error: {str(e)}"],
                'execution_time': time.time() - start_time
            }
    
    def _calculate_optimal_chunk_size(self, total_items: int) -> int:
        """
        Calculate optimal chunk size based on total items and available workers
        """
        # Base chunk size calculation
        base_chunk_size = max(1, total_items // (self.max_workers * 2))
        
        # Adjust based on total items
        if total_items < 100:
            return max(1, total_items // self.max_workers)
        elif total_items < 1000:
            return min(50, base_chunk_size)
        else:
            return min(100, base_chunk_size)
    
    def _create_chunks(self, items: List[Any], chunk_size: int) -> List[List[Any]]:
        """
        Create chunks from list of items
        """
        chunks = []
        for i in range(0, len(items), chunk_size):
            chunks.append(items[i:i + chunk_size])
        return chunks
    
    def process_comparison_parallel(self, current_data: List[Dict], previous_data: List[Dict],
                                  comparison_function: Callable, **kwargs) -> ProcessingResult:
        """
        Process payroll comparison in parallel
        """
        start_time = time.time()
        
        # Create lookup for previous data for faster access
        previous_lookup = {emp.get('employee_id', ''): emp for emp in previous_data}
        
        # Process current employees in parallel
        def compare_chunk(chunk: List[Dict]) -> List[Dict]:
            chunk_results = []
            for employee in chunk:
                emp_id = employee.get('employee_id', '')
                previous_employee = previous_lookup.get(emp_id, {})
                
                # Perform comparison
                comparison_result = comparison_function(employee, previous_employee, **kwargs)
                if comparison_result:
                    chunk_results.extend(comparison_result if isinstance(comparison_result, list) else [comparison_result])
            
            return chunk_results
        
        return self.process_employees_parallel(current_data, compare_chunk)
    
    def process_tracker_feeding_parallel(self, new_loans: List[Dict], new_vehicles: List[Dict],
                                       database_manager) -> ProcessingResult:
        """
        Process tracker feeding in parallel batches
        """
        start_time = time.time()
        
        # Process loans and vehicles in parallel
        def process_loans_batch(loans_chunk: List[Dict]) -> Dict[str, Any]:
            in_house_loans = [loan for loan in loans_chunk if loan.get('classification') == 'IN-HOUSE']
            external_loans = [loan for loan in loans_chunk if loan.get('classification') == 'EXTERNAL']
            
            results = {}
            
            if in_house_loans:
                result = database_manager.batch_insert_in_house_loans(in_house_loans)
                results['in_house'] = result
            
            if external_loans:
                result = database_manager.batch_insert_external_loans(external_loans)
                results['external'] = result
            
            return results
        
        def process_vehicles_batch(vehicles_chunk: List[Dict]) -> Dict[str, Any]:
            result = database_manager.batch_insert_motor_vehicles(vehicles_chunk)
            return {'vehicles': result}
        
        # Process in parallel
        all_results = []
        errors = []
        
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                
                # Submit loan processing
                if new_loans:
                    loan_chunks = self._create_chunks(new_loans, 50)  # Smaller chunks for DB operations
                    for chunk in loan_chunks:
                        futures.append(executor.submit(process_loans_batch, chunk))
                
                # Submit vehicle processing
                if new_vehicles:
                    vehicle_chunks = self._create_chunks(new_vehicles, 50)
                    for chunk in vehicle_chunks:
                        futures.append(executor.submit(process_vehicles_batch, chunk))
                
                # Collect results
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        all_results.append(result)
                    except Exception as e:
                        errors.append(f"Batch processing error: {str(e)}")
        
        except Exception as e:
            errors.append(f"Parallel tracker feeding failed: {str(e)}")
        
        execution_time = time.time() - start_time
        
        return ProcessingResult(
            success=len(errors) == 0,
            processed_count=len(new_loans) + len(new_vehicles),
            failed_count=len(errors),
            execution_time=execution_time,
            results=all_results,
            errors=errors
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get parallel processing performance statistics
        """
        return {
            'max_workers': self.max_workers,
            'use_processes': self.use_processes,
            'cpu_count': multiprocessing.cpu_count(),
            'processing_stats': self.processing_stats
        }

# Parallel processing functions for specific operations
def process_new_loan_detection_parallel(current_data: List[Dict], previous_data: List[Dict],
                                       current_month: str, current_year: str,
                                       max_workers: int = 4) -> List[Dict]:
    """
    Process NEW loan detection in parallel
    """
    manager = ParallelProcessingManager(max_workers=max_workers)
    
    # Create previous data lookup
    previous_lookup = {emp.get('employee_id', ''): emp for emp in previous_data}
    
    def detect_loans_chunk(chunk: List[Dict]) -> List[Dict]:
        from core.enhanced_tracker_feeder import EnhancedTrackerFeeder
        
        feeder = EnhancedTrackerFeeder()
        chunk_loans = []
        
        for employee in chunk:
            emp_id = employee.get('employee_id', '')
            previous_employee = previous_lookup.get(emp_id, {})
            
            # Detect new loans for this employee
            current_loans = employee.get('LOANS', {})
            previous_loans = previous_employee.get('LOANS', {})
            
            for loan_name, loan_data in current_loans.items():
                if loan_name not in previous_loans:
                    # NEW LOAN detected
                    amount = feeder._extract_loan_amount(loan_data)
                    classification = feeder._classify_loan_type(loan_name)
                    
                    chunk_loans.append({
                        'employee_no': emp_id,
                        'employee_name': employee.get('employee_name', 'Unknown'),
                        'department': employee.get('department', 'Unknown'),
                        'loan_type': loan_name,
                        'loan_amount': amount,
                        'classification': classification,
                        'period_month': current_month,
                        'period_year': current_year,
                        'period_acquired': f"{current_year}-{current_month}",
                        'source_session': f"parallel_enhanced_{current_year}_{current_month}"
                    })
        
        return chunk_loans
    
    result = manager.process_employees_parallel(current_data, detect_loans_chunk)
    
    if result.success:
        return result.results
    else:
        print(f"❌ Parallel loan detection failed: {result.errors}")
        return []

def process_new_vehicle_detection_parallel(current_data: List[Dict], previous_data: List[Dict],
                                         current_month: str, current_year: str,
                                         max_workers: int = 4) -> List[Dict]:
    """
    Process NEW motor vehicle detection in parallel
    """
    manager = ParallelProcessingManager(max_workers=max_workers)
    
    # Create previous data lookup
    previous_lookup = {emp.get('employee_id', ''): emp for emp in previous_data}
    
    def detect_vehicles_chunk(chunk: List[Dict]) -> List[Dict]:
        from core.enhanced_tracker_feeder import EnhancedTrackerFeeder
        
        feeder = EnhancedTrackerFeeder()
        chunk_vehicles = []
        motor_vehicle_terms = ['MOTOR VEH. MAINTENAN', 'MOTOR VEHICLE MAINTENANCE']
        
        for employee in chunk:
            emp_id = employee.get('employee_id', '')
            previous_employee = previous_lookup.get(emp_id, {})
            
            # Check for motor vehicle terms
            current_earnings = employee.get('EARNINGS', {})
            previous_earnings = previous_employee.get('EARNINGS', {})
            
            for item_name, item_value in current_earnings.items():
                if item_name in motor_vehicle_terms:
                    # Check if NOT in previous earnings
                    motor_vehicle_in_previous = any(
                        prev_item in motor_vehicle_terms 
                        for prev_item in previous_earnings.keys()
                    )
                    
                    if not motor_vehicle_in_previous:
                        # NEW MOTOR VEHICLE detected
                        amount = feeder._extract_amount(item_value)
                        
                        chunk_vehicles.append({
                            'employee_no': emp_id,
                            'employee_name': employee.get('employee_name', 'Unknown'),
                            'department': employee.get('department', 'Unknown'),
                            'allowance_type': item_name,
                            'payable_amount': amount,
                            'period_month': current_month,
                            'period_year': current_year,
                            'period_acquired': f"{current_year}-{current_month}",
                            'source_session': f"parallel_enhanced_{current_year}_{current_month}"
                        })
                        break  # Only one motor vehicle per employee
        
        return chunk_vehicles
    
    result = manager.process_employees_parallel(current_data, detect_vehicles_chunk)
    
    if result.success:
        return result.results
    else:
        print(f"❌ Parallel vehicle detection failed: {result.errors}")
        return []
