#!/usr/bin/env python3
"""Test actual report generation"""

import sys
import os
import sqlite3
import contextlib
import io

def test_report_generation():
    print("🧪 TESTING ACTUAL REPORT GENERATION")
    print("=" * 40)
    
    try:
        # Get session
        conn = sqlite3.connect('payroll_audit.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY session_id DESC LIMIT 1")
        session = cursor.fetchone()[0]
        print(f"Session: {session}")
        
        # Clear any existing reports for this session
        cursor.execute("DELETE FROM generated_reports WHERE session_id = ?", (session,))
        conn.commit()
        conn.close()
        
        # Test report generation
        sys.path.append('core')
        from phased_process_manager import PhasedProcessManager
        
        print("\nGenerating reports...")

        # Show debug output to see what's happening
        manager = PhasedProcessManager(debug_mode=True)
        result = manager.generate_final_reports(session)

        print(f"Result: {result.get('success', False)}")
        print(f"Error: {result.get('error', 'None')}")
        
        if result.get('success'):
            # Check database for generated reports
            conn = sqlite3.connect('payroll_audit.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM generated_reports WHERE session_id = ?", (session,))
            count = cursor.fetchone()[0]
            
            print(f"Reports in database: {count}")
            
            if count > 0:
                cursor.execute("""
                    SELECT report_type, file_path, file_size 
                    FROM generated_reports 
                    WHERE session_id = ?
                """, (session,))
                
                reports = cursor.fetchall()
                print("Generated reports:")
                for report_type, file_path, file_size in reports:
                    exists = "✅" if os.path.exists(file_path) else "❌"
                    print(f"  {exists} {report_type}: {file_path} ({file_size} bytes)")
                
                print("\n🎉 SUCCESS! Reports generated successfully!")
                return True
            else:
                print("❌ No reports found in database")
                return False
        else:
            print(f"❌ Report generation failed: {result.get('error')}")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_report_generation()
