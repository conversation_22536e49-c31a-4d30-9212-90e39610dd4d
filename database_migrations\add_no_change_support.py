#!/usr/bin/env python3
"""
DATABASE MIGRATION: Add NO_CHANGE Support to comparison_results table
Updates the change_type CHECK constraint to include 'NO_CHANGE' as a valid value.
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_database_path():
    """Get the database path"""
    # Check for common database names in current directory
    db_names = ['payroll_audit.db', 'payroll_auditor.db']
    
    for db_name in db_names:
        if os.path.exists(db_name):
            return db_name
    
    # Check in data directory
    data_dir = os.path.join(os.getcwd(), 'data')
    if os.path.exists(data_dir):
        for db_name in db_names:
            db_path = os.path.join(data_dir, db_name)
            if os.path.exists(db_path):
                return db_path
    
    # Default path
    return 'payroll_audit.db'

def backup_database(db_path):
    """Create a backup of the database before migration"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_no_change_{timestamp}"
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {e}")
        return None

def check_table_exists(cursor):
    """Check if comparison_results table exists"""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='comparison_results'")
    return cursor.fetchone() is not None

def get_current_schema(cursor):
    """Get the current table schema"""
    cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='comparison_results'")
    result = cursor.fetchone()
    return result[0] if result else None

def update_comparison_results_schema(cursor, conn):
    """Update comparison_results table to support NO_CHANGE"""
    
    print("\n🔧 UPDATING COMPARISON_RESULTS SCHEMA:")
    
    if not check_table_exists(cursor):
        print("   📝 Creating comparison_results table with NO_CHANGE support...")
        cursor.execute('''
            CREATE TABLE comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT CHECK(change_type IN ('NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED', 'NO_CHANGE')),
                priority TEXT CHECK(priority IN ('High', 'Medium', 'Low', 'Routine')),
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()
        print("   ✅ Created comparison_results table with NO_CHANGE support")
        return True
    
    # Check current schema
    current_schema = get_current_schema(cursor)
    if 'NO_CHANGE' in current_schema:
        print("   ✅ NO_CHANGE already supported in schema")
        return True
    
    print("   📝 Table exists but needs NO_CHANGE support...")
    
    # Get existing data
    cursor.execute("SELECT * FROM comparison_results")
    existing_data = cursor.fetchall()
    print(f"   📊 Found {len(existing_data)} existing records")
    
    # Get column names
    cursor.execute("PRAGMA table_info(comparison_results)")
    columns = [row[1] for row in cursor.fetchall()]
    
    try:
        # Drop existing table
        cursor.execute("DROP TABLE comparison_results")
        
        # Create new table with NO_CHANGE support
        cursor.execute('''
            CREATE TABLE comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT CHECK(change_type IN ('NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED', 'NO_CHANGE')),
                priority TEXT CHECK(priority IN ('High', 'Medium', 'Low', 'Routine')),
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Restore existing data
        if existing_data:
            print(f"   📥 Restoring {len(existing_data)} existing records...")
            
            # Build insert statement based on available columns
            placeholders = ', '.join(['?' for _ in columns])
            column_names = ', '.join(columns)
            
            cursor.executemany(
                f"INSERT INTO comparison_results ({column_names}) VALUES ({placeholders})",
                existing_data
            )
        
        conn.commit()
        print("   ✅ Successfully updated schema with NO_CHANGE support")
        
        # Verify data restoration
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        final_count = cursor.fetchone()[0]
        print(f"   ✅ Verified: {final_count} records restored")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error updating schema: {e}")
        return False

def test_no_change_insertion(cursor, conn):
    """Test that NO_CHANGE records can be inserted"""
    
    print("\n🧪 TESTING NO_CHANGE INSERTION:")
    
    try:
        # Insert a test NO_CHANGE record
        cursor.execute('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label,
             previous_value, current_value, change_type, priority)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('test_session', 'TEST001', 'Test Employee', 'EARNINGS', 'BASIC SALARY',
              '1000.00', '1000.00', 'NO_CHANGE', 'Medium'))
        
        conn.commit()
        
        # Verify insertion
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE change_type = 'NO_CHANGE'")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print("   ✅ NO_CHANGE records can be inserted successfully")
            
            # Clean up test data
            cursor.execute("DELETE FROM comparison_results WHERE session_id = 'test_session'")
            conn.commit()
            
            return True
        else:
            print("   ❌ NO_CHANGE record insertion failed")
            return False
            
    except Exception as e:
        print(f"   ❌ NO_CHANGE insertion test failed: {e}")
        return False

def main():
    """Main migration function"""
    
    print("🚀 NO_CHANGE SUPPORT MIGRATION")
    print("=" * 50)
    
    # Get database path
    db_path = get_database_path()
    print(f"📁 Database path: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    # Create backup
    backup_path = backup_database(db_path)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Update schema
        if not update_comparison_results_schema(cursor, conn):
            print("❌ Migration failed!")
            return False
        
        # Test NO_CHANGE insertion
        if not test_no_change_insertion(cursor, conn):
            print("⚠️ NO_CHANGE insertion test failed")
        
        conn.close()
        
        print("\n✅ MIGRATION COMPLETED SUCCESSFULLY!")
        print("🎯 comparison_results table now supports NO_CHANGE change type")
        print("📊 Enhanced change detection system ready")
        
        if backup_path:
            print(f"💾 Backup available at: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
