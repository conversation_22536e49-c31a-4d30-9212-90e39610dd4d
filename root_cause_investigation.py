#!/usr/bin/env python3
"""
ROOT CAUSE INVESTIGATION
Find out exactly why comparison_results is empty and pre_reporting_results has data
"""

import sqlite3
import sys
import os

def investigate_root_cause():
    print("🔍 ROOT CAUSE INVESTIGATION")
    print("=" * 50)
    
    db_path = 'data/templar_payroll_auditor.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Get current session details
        print("1. 📊 SESSION ANALYSIS:")
        cursor.execute('SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY created_at DESC LIMIT 1')
        current_session = cursor.fetchone()[0]
        print(f"   Current session: {current_session}")
        
        # Check session metadata
        cursor.execute('SELECT * FROM audit_sessions WHERE session_id = ?', (current_session,))
        session_info = cursor.fetchone()
        if session_info:
            print(f"   Session status: {session_info}")
        else:
            print("   ⚠️ No session metadata found")
        
        # 2. Analyze pre_reporting_results structure
        print("\n2. 🔍 PRE_REPORTING_RESULTS ANALYSIS:")
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (current_session,))
        pr_count = cursor.fetchone()[0]
        print(f"   Total records: {pr_count}")
        
        # Check change_id references
        cursor.execute('SELECT DISTINCT change_id FROM pre_reporting_results WHERE session_id = ? LIMIT 10', (current_session,))
        change_ids = cursor.fetchall()
        print(f"   Sample change_ids: {[c[0] for c in change_ids]}")
        
        # Check if change_ids reference comparison_results
        if change_ids:
            sample_id = change_ids[0][0]
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE id = ?', (sample_id,))
            ref_exists = cursor.fetchone()[0]
            print(f"   change_id {sample_id} exists in comparison_results: {ref_exists > 0}")
        
        # 3. Check comparison_results for this session
        print("\n3. 📊 COMPARISON_RESULTS ANALYSIS:")
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        cr_count = cursor.fetchone()[0]
        print(f"   Records for current session: {cr_count}")
        
        # Check total comparison_results
        cursor.execute('SELECT COUNT(*) FROM comparison_results')
        total_cr = cursor.fetchone()[0]
        print(f"   Total comparison_results: {total_cr}")
        
        if total_cr > 0:
            cursor.execute('SELECT DISTINCT session_id FROM comparison_results')
            cr_sessions = cursor.fetchall()
            print(f"   Sessions with comparison data: {[s[0] for s in cr_sessions]}")
        
        # 4. Check extracted_data (source of comparison_results)
        print("\n4. 📊 EXTRACTED_DATA ANALYSIS:")
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
        ed_count = cursor.fetchone()[0]
        print(f"   Extracted data for current session: {ed_count}")
        
        cursor.execute('SELECT COUNT(*) FROM extracted_data')
        total_ed = cursor.fetchone()[0]
        print(f"   Total extracted data: {total_ed}")
        
        # 5. Check phase completion status
        print("\n5. 📊 PHASE STATUS ANALYSIS:")
        
        # Check session phases
        cursor.execute('SELECT * FROM session_phases WHERE session_id = ?', (current_session,))
        phases = cursor.fetchall()
        if phases:
            print("   Phase completion status:")
            for phase in phases:
                print(f"     {phase}")
        else:
            print("   ⚠️ No phase tracking found")
        
        # 6. Timeline analysis
        print("\n6. ⏰ TIMELINE ANALYSIS:")
        
        # Check creation times
        cursor.execute('SELECT MIN(created_at), MAX(created_at) FROM pre_reporting_results WHERE session_id = ?', (current_session,))
        pr_times = cursor.fetchone()
        print(f"   Pre-reporting created: {pr_times[0]} to {pr_times[1]}")
        
        if total_cr > 0:
            cursor.execute('SELECT MIN(created_at), MAX(created_at) FROM comparison_results')
            cr_times = cursor.fetchone()
            print(f"   Comparison created: {cr_times[0]} to {cr_times[1]}")
        
        # 7. Data integrity check
        print("\n7. 🔍 DATA INTEGRITY CHECK:")
        
        # Check if pre_reporting_results has actual comparison data
        cursor.execute('SELECT * FROM pre_reporting_results WHERE session_id = ? LIMIT 3', (current_session,))
        pr_samples = cursor.fetchall()
        
        print("   Sample pre_reporting_results:")
        for i, sample in enumerate(pr_samples):
            print(f"     {i+1}. {sample}")
        
        # Check if change_ids are sequential (indicating they were generated, not referenced)
        cursor.execute('SELECT change_id FROM pre_reporting_results WHERE session_id = ? ORDER BY change_id LIMIT 10', (current_session,))
        change_id_sequence = [row[0] for row in cursor.fetchall()]
        print(f"   Change ID sequence: {change_id_sequence}")
        
        is_sequential = all(change_id_sequence[i] == change_id_sequence[i-1] + 1 for i in range(1, len(change_id_sequence)))
        print(f"   Change IDs are sequential: {is_sequential}")
        
        # 8. Determine root cause
        print("\n8. 🎯 ROOT CAUSE DETERMINATION:")
        
        if cr_count == 0 and pr_count > 0:
            if is_sequential:
                print("   🚨 ROOT CAUSE: PRE_REPORTING_RESULTS was generated WITHOUT comparison_results")
                print("   📋 EVIDENCE: Sequential change_ids indicate artificial generation")
                print("   💡 SOLUTION: Need to regenerate comparison_results from extracted_data")
                return "MISSING_COMPARISON_PIPELINE"
            else:
                print("   🚨 ROOT CAUSE: COMPARISON_RESULTS was deleted after pre_reporting_results creation")
                print("   📋 EVIDENCE: Non-sequential change_ids indicate real references")
                print("   💡 SOLUTION: Need to restore comparison_results from backup or regenerate")
                return "DELETED_COMPARISON_DATA"
        elif cr_count > 0 and pr_count > 0:
            print("   ✅ NORMAL STATE: Both tables have data")
            return "NORMAL"
        elif cr_count == 0 and pr_count == 0:
            print("   🚨 ROOT CAUSE: NO DATA GENERATED")
            print("   💡 SOLUTION: Need to run complete audit process")
            return "NO_DATA"
        else:
            print("   🚨 ROOT CAUSE: UNKNOWN STATE")
            return "UNKNOWN"
    
    except Exception as e:
        print(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return "ERROR"
    
    finally:
        conn.close()

def propose_permanent_solution(root_cause):
    print("\n" + "=" * 50)
    print("🔧 PERMANENT SOLUTION PROPOSAL")
    print("=" * 50)
    
    if root_cause == "MISSING_COMPARISON_PIPELINE":
        print("📋 SOLUTION: REGENERATE COMPARISON PIPELINE")
        print("   1. Load extracted_data for current session")
        print("   2. Run comparison engine to generate comparison_results")
        print("   3. Clear existing pre_reporting_results")
        print("   4. Regenerate pre_reporting_results from comparison_results")
        print("   5. Verify data integrity and relationships")
        
    elif root_cause == "DELETED_COMPARISON_DATA":
        print("📋 SOLUTION: RESTORE COMPARISON DATA")
        print("   1. Analyze pre_reporting_results.change_id references")
        print("   2. Reverse-engineer comparison_results structure")
        print("   3. Populate comparison_results with reconstructed data")
        print("   4. Verify referential integrity")
        
    elif root_cause == "NO_DATA":
        print("📋 SOLUTION: COMPLETE AUDIT PROCESS")
        print("   1. Run full extraction process")
        print("   2. Run comparison process")
        print("   3. Run pre-reporting process")
        print("   4. Verify complete data pipeline")
        
    else:
        print("📋 SOLUTION: COMPREHENSIVE INVESTIGATION NEEDED")
        print("   1. Manual data analysis required")
        print("   2. Check system logs for clues")
        print("   3. Implement data validation checks")

if __name__ == "__main__":
    root_cause = investigate_root_cause()
    propose_permanent_solution(root_cause)
    
    print(f"\n🎯 ROOT CAUSE: {root_cause}")
    print("🔧 Ready to implement permanent solution based on findings")
