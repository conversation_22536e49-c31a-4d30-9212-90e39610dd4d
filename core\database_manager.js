/**
 * FAST DATABASE CONNECTION MANAGER
 * Singleton pattern to prevent multiple database initializations
 * Optimized for instant startup performance
 */

const UnifiedDatabase = require('./unified_database');

class DatabaseManager {
    constructor() {
        this.instance = null;
        this.isInitializing = false;
        this.initPromise = null;
    }

    /**
     * Get database instance (singleton pattern) with improved error handling
     */
    async getInstance() {
        // If we already have a valid instance, return it immediately
        if (this.instance && this.instance.isConnected) {
            console.log('✅ Using existing database connection');
            return this.instance;
        }

        // If initialization is already in progress, wait for it
        if (this.isInitializing) {
            console.log('⏳ Database initialization already in progress, waiting...');
            try {
                return await this.initPromise;
            } catch (error) {
                console.error('❌ Database initialization failed while waiting:', error.message);
                // Reset initialization state so we can try again
                this.isInitializing = false;
                this.initPromise = null;
                // Return a fallback instance that will work in read-only mode
                return this.createFallbackInstance();
            }
        }

        // Start fresh initialization with timeout protection
        console.log('🔄 Starting database initialization');
        this.isInitializing = true;
        
        // Create a timeout promise to prevent infinite hanging
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Database initialization timed out after 8 seconds')), 8000);
        });
        
        // Create the actual initialization promise
        this.initPromise = this.createInstance();
        
        try {
            // Race between initialization and timeout
            this.instance = await Promise.race([this.initPromise, timeoutPromise]);
            console.log('✅ Database initialized successfully');
            return this.instance;
        } catch (error) {
            console.error('❌ Database initialization error:', error.message);
            // Create a fallback instance that will work in read-only mode
            this.instance = this.createFallbackInstance();
            return this.instance;
        } finally {
            this.isInitializing = false;
            this.initPromise = null;
        }
    }

    /**
     * Create new database instance
     */
    async createInstance() {
        const db = new UnifiedDatabase();
        
        // Wait for initialization to complete
        let retries = 0;
        while (!db.isConnected && retries < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            retries++;
        }

        if (!db.isConnected) {
            throw new Error('Database connection timeout');
        }

        return db;
    }

    /**
     * Close database connection
     */
    async close() {
        if (this.instance) {
            await this.instance.close();
            this.instance = null;
        }
    }

    /**
     * Reset instance (for testing)
     */
    reset() {
        this.instance = null;
        this.isInitializing = false;
        this.initPromise = null;
    }
}

// Export singleton instance
module.exports = new DatabaseManager();
