/**
 * BANK ADVISER UI CONTROLLER
 * Manages the Bank Adviser Module interface with dual functionality:
 * 1. Core Bank Adviser - Excel Bank Advice generation
 * 2. Loan & Allowance Tracker - Mini-database tracking system
 */

class BankAdviserUI {
    constructor() {
        this.currentSession = null;
        this.processingStatus = 'idle';
        this.documentsProcessed = {
            finalAdjusted: false,
            allowances: false,
            awards: false
        };

        // Tax rate settings
        this.taxRates = {
            allowances: 5.0,
            awards: 7.5,
            educational_subsidy: 5.0
        };

        // Available sections
        this.availableSections = [
            'HEADQUARTERS',
            'INTERNATIONAL MISSIONS',
            'PENTMEDIA CENTER'
        ];

        console.log('🏦 Bank Adviser UI initialized');

        // Initialize UI asynchronously to load session
        this.initializeUI().then(() => {
            // Initialize with manual session creation (no auto-creation)
            this.initializeManualSessionCreation();
        }).catch(error => {
            console.error('[BANK-ADVISER] Initialization error:', error);
            // Continue with manual session creation even if session loading fails
            this.initializeManualSessionCreation();
        });
    }

    /**
     * Initialize Bank Adviser UI components
     */
    async initializeUI() {
        this.createBankAdviserInterface();
        this.createLoanTrackerInterface();
        this.setupEventListeners();
        this.loadSettings();

        // Initialize processing status
        this.processingStatus = 'idle';
        this.documentsProcessed = {
            finalAdjusted: false,
            allowances: false,
            awards: false
        };

        // CRITICAL FIX: Load current session from unified session manager
        await this.loadCurrentSession();
    }

    /**
     * Load current session from unified session manager
     */
    async loadCurrentSession() {
        try {
            console.log('[BANK-ADVISER] Loading current session...');

            // Try to get current session from unified session manager
            if (window.api && window.api.getCurrentSessionId) {
                const sessionResult = await window.api.getCurrentSessionId();
                if (sessionResult && sessionResult.success && sessionResult.session_id) {
                    // Create a session object compatible with Bank Adviser
                    this.currentSession = {
                        sessionId: sessionResult.session_id,
                        sessionName: `Session_${sessionResult.session_id.slice(-8)}`,
                        status: 'active'
                    };
                    console.log('[BANK-ADVISER] ✅ Loaded current session:', this.currentSession);
                    return;
                }
            }

            console.log('[BANK-ADVISER] ⚠️ No current session found - user will need to create one');
        } catch (error) {
            console.warn('[BANK-ADVISER] ⚠️ Could not load current session:', error.message);
        }
    }

    /**
     * Create Core Bank Adviser interface
     */
    createBankAdviserInterface() {
        const bankAdviserContent = document.getElementById('bank-adviser-content');
        if (!bankAdviserContent) return;

        bankAdviserContent.innerHTML = `
            <div class="bank-adviser-container">
                <!-- Period Selection (Auto-Session Creation) -->
                <div class="period-section">
                    <h3>📅 Bank Advice Period</h3>
                    <div class="period-controls">
                        <div class="input-group">
                            <label>Month:</label>
                            <select id="current-month">
                                <option value="JANUARY">January</option>
                                <option value="FEBRUARY">February</option>
                                <option value="MARCH">March</option>
                                <option value="APRIL">April</option>
                                <option value="MAY">May</option>
                                <option value="JUNE" selected>June</option>
                                <option value="JULY">July</option>
                                <option value="AUGUST">August</option>
                                <option value="SEPTEMBER">September</option>
                                <option value="OCTOBER">October</option>
                                <option value="NOVEMBER">November</option>
                                <option value="DECEMBER">December</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label>Year:</label>
                            <input type="number" id="current-year" value="2025" min="2020" max="2030">
                        </div>
                        <div class="period-status" id="period-status">
                            <i class="fas fa-info-circle"></i>
                            <span>Select month and year to begin</span>
                        </div>
                        <div class="session-creation" id="session-creation" style="display: none;">
                            <button id="start-session-btn" class="start-session-btn">
                                <i class="fas fa-play"></i> Start Session
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Section Selection -->
                <div class="section-selection" id="section-selection" style="display: none;">
                    <h3>🏢 Section Selection</h3>
                    <div class="sections-grid">
                        ${this.availableSections.map(section => `
                            <label class="section-checkbox">
                                <input type="checkbox" value="${section}" class="section-select bank-adviser-section" data-bank-adviser="true">
                                <span>${section}</span>
                            </label>
                        `).join('')}
                    </div>
                    <div class="generation-options">
                        <div class="generation-info">
                            <i class="fas fa-info-circle"></i>
                            <span>Excel Bank Advice will be generated for selected sections only (Composite mode)</span>
                        </div>
                    </div>
                    <div class="custom-section">
                        <input type="text" id="custom-section" placeholder="Add custom section">
                        <button id="add-section-btn">Add Section</button>
                    </div>
                </div>

                <!-- Document Processing -->
                <div class="document-processing" id="document-processing" style="display: none;">
                    <h3>📄 Document Processing</h3>

                    <!-- Processing Controls -->
                    <div class="processing-controls">
                        <button id="start-processing-btn" class="primary-btn" disabled>
                            <i class="fas fa-play"></i> Start Processing
                        </button>
                        <button id="stop-processing-btn" class="secondary-btn" disabled>
                            <i class="fas fa-stop"></i> Stop Processing
                        </button>
                        <div class="processing-progress" id="processing-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>
                            <div class="progress-text" id="progress-text">Ready to process documents</div>
                        </div>
                    </div>

                    <!-- Final Adjusted Payslip -->
                    <div class="document-section">
                        <h4>1. Final Adjusted Payslip</h4>
                        <div class="upload-area" id="final-adjusted-upload">
                            <div class="upload-zone">
                                <i class="upload-icon">📄</i>
                                <p>Drop Final Adjusted Payslip PDF here or click to browse</p>
                                <input type="file" id="final-adjusted-file" accept=".pdf" style="display: none;">
                            </div>
                            <div class="processing-status" id="final-adjusted-status">
                                <span class="status-badge" id="final-adjusted-count">Processed: 0 items</span>
                            </div>
                        </div>
                    </div>

                    <!-- Allowances Payslip -->
                    <div class="document-section">
                        <h4>2. Allowances Payslip</h4>
                        <div class="tax-settings">
                            <label>Tax Rate (%):</label>
                            <input type="number" id="allowances-tax-rate" value="5.0" step="0.1" min="0" max="100">
                        </div>
                        <div class="upload-area" id="allowances-upload">
                            <div class="upload-zone">
                                <i class="upload-icon">🎁</i>
                                <p>Drop Allowances Payslip PDF here or click to browse</p>
                                <input type="file" id="allowances-file" accept=".pdf" style="display: none;">
                            </div>
                            <div class="processing-status" id="allowances-status">
                                <span class="status-badge" id="allowances-count">Processed: 0 items</span>
                            </div>
                        </div>
                    </div>

                    <!-- Awards Payslip -->
                    <div class="document-section">
                        <h4>3. Awards Payslip</h4>
                        <div class="tax-settings">
                            <label>Tax Rate (%):</label>
                            <input type="number" id="awards-tax-rate" value="7.5" step="0.1" min="0" max="100">
                        </div>
                        <div class="upload-area" id="awards-upload">
                            <div class="upload-zone">
                                <i class="upload-icon">🏆</i>
                                <p>Drop Awards Payslip PDF here or click to browse</p>
                                <input type="file" id="awards-file" accept=".pdf" style="display: none;">
                            </div>
                            <div class="processing-status" id="awards-status">
                                <span class="status-badge" id="awards-count">Processed: 0 items</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Advice Generation -->
                <div class="bank-advice-generation" id="bank-advice-generation" style="display: none;">
                    <h3>📊 Bank Advice Generation</h3>
                    <div class="generation-controls">
                        <button id="generate-correct-bank-advice-btn" class="success-btn" disabled>
                            <i class="fas fa-file-excel"></i> Generate CORRECT Excel Bank Advice
                        </button>
                        <div class="generation-progress" id="generation-progress"></div>
                    </div>
                    <div class="ready-indicator" id="ready-indicator" style="display: none;">
                        <div class="ready-message">
                            <i class="fas fa-check-circle"></i>
                            <strong>Ready to Generate Excel Bank Advice!</strong>
                            <p>All documents have been processed successfully. Click the button above to generate your Excel files.</p>
                        </div>
                    </div>

                    <!-- Generated Reports Section with Eye Icon -->
                    <div class="generated-reports" id="generated-reports" style="display: none;">
                        <h4>📄 Generated Reports</h4>
                        <div class="reports-list" id="reports-list">
                            <!-- Dynamic reports with eye icons will be added here -->
                        </div>
                    </div>

                    <div class="preview-section" id="bank-advice-preview"></div>
                </div>

                <!-- Transform Screen (Similar to Payroll Audit) -->
                <div class="transform-screen" id="transform-screen" style="display: none;">
                    <h3>🔄 Bank Advice Transform</h3>
                    <div class="transform-container">
                        <div class="transform-header">
                            <div class="transform-info">
                                <span class="transform-title" id="transform-title">Processing Bank Advice Data...</span>
                                <span class="transform-subtitle" id="transform-subtitle">Transforming extracted data into Excel format</span>
                            </div>
                            <div class="transform-actions">
                                <button id="close-transform-btn" class="secondary-btn">
                                    <i class="fas fa-times"></i> Close
                                </button>
                            </div>
                        </div>

                        <div class="transform-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="transform-progress-fill"></div>
                            </div>
                            <div class="progress-text" id="transform-progress-text">Initializing...</div>
                        </div>

                        <div class="transform-details" id="transform-details">
                            <div class="details-grid">
                                <div class="detail-item">
                                    <span class="detail-label">Final Adjusted Records:</span>
                                    <span class="detail-value" id="transform-final-count">0</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Allowance Records:</span>
                                    <span class="detail-value" id="transform-allowance-count">0</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Award Records:</span>
                                    <span class="detail-value" id="transform-award-count">0</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Total Bank Records:</span>
                                    <span class="detail-value" id="transform-total-count">0</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Banks Identified:</span>
                                    <span class="detail-value" id="transform-bank-count">0</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Status:</span>
                                    <span class="detail-value" id="transform-status">Ready</span>
                                </div>
                            </div>
                        </div>

                        <div class="transform-log" id="transform-log">
                            <h4>📋 Processing Log</h4>
                            <div class="log-container" id="log-container">
                                <!-- Dynamic log entries will be added here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Session Status -->
                <div class="session-status" id="session-status">
                    <h4>📈 Session Status</h4>
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">Final Adjusted:</span>
                            <span class="status-value" id="final-adjusted-count">0 employees</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Allowances:</span>
                            <span class="status-value" id="allowances-count">0 items</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Awards:</span>
                            <span class="status-value" id="awards-count">0 items</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create Loan & Allowance Tracker interface
     */
    createLoanTrackerInterface() {
        const loanTrackerContent = document.getElementById('loan-tracker-content');
        if (!loanTrackerContent) return;

        loanTrackerContent.innerHTML = `
            <div class="loan-tracker-container">
                <h3>📊 Loan & Allowance Tracker</h3>

                <!-- Category Tabs -->
                <div class="tracker-tabs">
                    <button class="tab-btn active" data-category="in_house_loans">In-House Loans</button>
                    <button class="tab-btn" data-category="external_loans">External Loans</button>
                    <button class="tab-btn" data-category="leave_claims">Leave Claims</button>
                    <button class="tab-btn" data-category="educational_subsidy">Educational Subsidy</button>
                    <button class="tab-btn" data-category="long_service_awards">Long Service Awards</button>
                    <button class="tab-btn" data-category="motor_vehicle_maintenance">Motor Vehicle Maintenance</button>
                    <button class="tab-btn" data-category="duplicate_checker">🔍 Duplicate Checker</button>
                </div>

                <!-- Search and Filter Controls -->
                <div class="tracker-controls">
                    <div class="search-section">
                        <input type="text" id="employee-search" placeholder="Search by Employee No. or Name">
                        <button id="search-btn">🔍 Search</button>
                    </div>
                    <div class="filter-section">
                        <select id="filter-year">
                            <option value="">All Years</option>
                            <option value="2025">2025</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                        </select>
                        <select id="filter-month">
                            <option value="">All Months</option>
                            <option value="01">January</option>
                            <option value="02">February</option>
                            <option value="03">March</option>
                            <option value="04">April</option>
                            <option value="05">May</option>
                            <option value="06">June</option>
                            <option value="07">July</option>
                            <option value="08">August</option>
                            <option value="09">September</option>
                            <option value="10">October</option>
                            <option value="11">November</option>
                            <option value="12">December</option>
                        </select>
                        <button id="apply-filters-btn">📅 Apply Filters</button>
                        <button id="export-btn">📊 Export to Excel</button>
                    </div>
                    <div class="management-section">
                        <button id="refresh-btn">🔄 Refresh</button>
                        <button id="delete-selected-btn" class="btn-warning">🗑️ Delete Selected</button>
                        <button id="clear-table-btn" class="btn-danger">🗑️ Clear Table</button>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="tracker-table-container">
                    <table id="tracker-table" class="data-table sortable">
                        <thead id="tracker-table-head">
                            <!-- Dynamic headers based on category -->
                        </thead>
                        <tbody id="tracker-table-body">
                            <!-- Dynamic data -->
                        </tbody>
                    </table>
                </div>

                <!-- Summary Section -->
                <div class="tracker-summary" id="tracker-summary">
                    <!-- Dynamic summary based on category -->
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Manual session creation
        document.getElementById('start-session-btn')?.addEventListener('click', () => {
            this.createManualSession();
        });

        // Section management
        document.getElementById('add-section-btn')?.addEventListener('click', () => {
            this.addCustomSection();
        });

        // Section checkbox listeners (FIXED: Add missing event listeners)
        this.setupSectionCheckboxListeners();

        // File uploads
        this.setupFileUploadListeners();

        // Processing controls
        document.getElementById('start-processing-btn')?.addEventListener('click', () => {
            this.startProcessing();
        });

        document.getElementById('stop-processing-btn')?.addEventListener('click', () => {
            this.stopProcessing();
        });

        // CORRECT Bank advice generation
        document.getElementById('generate-correct-bank-advice-btn')?.addEventListener('click', () => {
            this.generateCorrectBankAdvice();
        });

        // Transform screen controls
        document.getElementById('close-transform-btn')?.addEventListener('click', () => {
            this.closeTransformScreen();
        });

        // Period change listeners (Show Start Session button)
        document.getElementById('current-month')?.addEventListener('change', () => {
            this.updateSessionCreationVisibility();
        });

        document.getElementById('current-year')?.addEventListener('change', () => {
            this.updateSessionCreationVisibility();
        });

        // Loan tracker tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTrackerCategory(e.target.dataset.category);
            });
        });

        // Filter and management controls
        document.getElementById('apply-filters-btn')?.addEventListener('click', () => {
            this.applyFilters();
        });

        document.getElementById('refresh-btn')?.addEventListener('click', () => {
            this.refreshCurrentCategory();
        });

        document.getElementById('delete-selected-btn')?.addEventListener('click', () => {
            this.deleteSelectedRows();
        });

        document.getElementById('clear-table-btn')?.addEventListener('click', () => {
            this.clearCurrentTable();
        });

        // Search functionality
        document.getElementById('search-btn')?.addEventListener('click', () => {
            this.searchEmployeeData();
        });

        document.getElementById('employee-search')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchEmployeeData();
            }
        });

        // Search functionality
        document.getElementById('search-btn')?.addEventListener('click', () => {
            this.searchEmployeeData();
        });

        // Export functionality
        document.getElementById('export-btn')?.addEventListener('click', () => {
            this.exportTrackerData();
        });
    }

    /**
     * Setup section checkbox listeners (FIXED: Missing functionality)
     */
    setupSectionCheckboxListeners() {
        // Use direct event listeners instead of delegation to avoid conflicts
        this.attachSectionListeners();

        // Also set up a MutationObserver to handle dynamically added checkboxes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const newCheckboxes = node.querySelectorAll('.section-select.bank-adviser-section');
                        newCheckboxes.forEach(checkbox => {
                            this.attachSingleSectionListener(checkbox);
                        });
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    /**
     * Attach listeners to existing section checkboxes
     */
    attachSectionListeners() {
        const checkboxes = document.querySelectorAll('.section-select.bank-adviser-section');
        console.log('[BANK-ADVISER] ✅ Found', checkboxes.length, 'section checkboxes to attach listeners to');
        checkboxes.forEach(checkbox => {
            this.attachSingleSectionListener(checkbox);
        });
    }

    /**
     * Attach listener to a single section checkbox
     */
    attachSingleSectionListener(checkbox) {
        // Remove any existing listeners to prevent duplicates
        checkbox.removeEventListener('change', this.boundSectionHandler);

        // Create bound handler if it doesn't exist
        if (!this.boundSectionHandler) {
            this.boundSectionHandler = (e) => {
                e.stopPropagation();
                e.stopImmediatePropagation();
                console.log('[BANK-ADVISER] ✅ Section checkbox changed:', e.target.value, e.target.checked);

                // Show visual feedback immediately
                this.showMessage(`Section ${e.target.value} ${e.target.checked ? 'selected' : 'deselected'}`, 'info');

                this.handleSectionChange(e.target);
            };
        }

        // Add the listener
        checkbox.addEventListener('change', this.boundSectionHandler);
        console.log('[BANK-ADVISER] ✅ Listener attached to checkbox:', checkbox.value);
    }

    /**
     * Handle section checkbox change (FIXED: New method)
     */
    async handleSectionChange(checkbox) {
        if (!this.currentSession) {
            console.log('[BANK-ADVISER] No active session for section update');
            return;
        }

        try {
            const selectedSections = this.getSelectedSections();

            console.log('[BANK-ADVISER] DEBUG: About to call IPC with:', {
                sessionId: this.currentSession.sessionId,
                selectedSections: selectedSections
            });

            // Update session with selected sections
            const result = await window.electronAPI.invoke('bank-adviser-update-session-sections', {
                sessionId: this.currentSession.sessionId,
                selectedSections: selectedSections
            });

            console.log('[BANK-ADVISER] DEBUG: IPC response received:', result);

            if (result && result.success) {
                console.log('[BANK-ADVISER] Section selection updated successfully:', selectedSections);
                this.currentSession.selectedSections = selectedSections;
                // Show success message briefly
                this.showMessage('Section selection updated', 'success');
            } else {
                console.error('[BANK-ADVISER] Failed to update section selection. Result:', result);
                // Revert checkbox state
                checkbox.checked = !checkbox.checked;
                this.showMessage('Failed to update section selection', 'error');
            }
        } catch (error) {
            console.error('[BANK-ADVISER] Error updating section selection:', error);
            // Revert checkbox state
            checkbox.checked = !checkbox.checked;
            this.showMessage('Error updating section selection', 'error');
        }
    }

    /**
     * Setup file upload listeners
     */
    setupFileUploadListeners() {
        const uploadAreas = [
            { id: 'final-adjusted-upload', fileId: 'final-adjusted-file', type: 'final_adjusted' },
            { id: 'allowances-upload', fileId: 'allowances-file', type: 'allowances' },
            { id: 'awards-upload', fileId: 'awards-file', type: 'awards' }
        ];

        uploadAreas.forEach(area => {
            const uploadZone = document.getElementById(area.id);
            const fileInput = document.getElementById(area.fileId);

            if (uploadZone && fileInput) {
                // Click to browse
                uploadZone.addEventListener('click', () => {
                    fileInput.click();
                });

                // File selection
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.handleFileUpload(e.target.files[0], area.type);
                    }
                });

                // Drag and drop
                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.classList.add('drag-over');
                });

                uploadZone.addEventListener('dragleave', () => {
                    uploadZone.classList.remove('drag-over');
                });

                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadZone.classList.remove('drag-over');

                    if (e.dataTransfer.files.length > 0) {
                        this.handleFileUpload(e.dataTransfer.files[0], area.type);
                    }
                });
            }
        });
    }

    /**
     * Auto-create session based on month/year selection
     */
    async autoCreateSession() {
        const currentMonth = document.getElementById('current-month').value;
        const currentYear = parseInt(document.getElementById('current-year').value);

        // Auto-generate session name
        const sessionName = `${currentMonth} ${currentYear} Bank Advice`;

        try {
            const result = await window.electronAPI.invoke('bank-adviser-create-session', {
                sessionName,
                currentMonth,
                currentYear
            });

            if (result.success) {
                this.currentSession = result.session;

                // Update period status
                const periodStatus = document.getElementById('period-status');
                if (periodStatus) {
                    periodStatus.innerHTML = `
                        <i class="fas fa-check-circle" style="color: #28a745;"></i>
                        <span style="color: #28a745;">Session: ${sessionName}</span>
                    `;
                }

                this.showMessage(`Session created: ${sessionName}`, 'success');
                this.showSectionSelection();
            } else {
                this.showMessage(`Failed to create session: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Error creating session: ${error.message}`, 'error');
        }
    }

    /**
     * Wait for Bank Adviser Manager to be initialized and then create session
     */
    async waitForManagerAndCreateSession() {
        let retries = 0;
        const maxRetries = 30; // 30 seconds max wait

        const checkAndCreate = async () => {
            try {
                // Test if the manager is ready by attempting to create a session
                const result = await window.electronAPI.invoke('bank-adviser-create-session', {
                    sessionName: 'Test Session',
                    currentMonth: 'June',
                    currentYear: 2025
                });

                if (result.success) {
                    // Manager is ready, now create the actual session
                    await this.autoCreateSession();
                    return;
                } else if (result.error && result.error.includes('not initialized')) {
                    // Manager not ready yet, wait and retry
                    if (retries < maxRetries) {
                        retries++;
                        console.log(`[BANK-ADVISER] Waiting for manager initialization... (${retries}/${maxRetries})`);
                        setTimeout(checkAndCreate, 1000);
                    } else {
                        console.error('[BANK-ADVISER] Manager initialization timeout');
                        this.showMessage('Bank Adviser Manager initialization timeout', 'error');
                    }
                } else {
                    // Other error, try to create session anyway
                    await this.autoCreateSession();
                }
            } catch (error) {
                if (retries < maxRetries) {
                    retries++;
                    console.log(`[BANK-ADVISER] Manager check failed, retrying... (${retries}/${maxRetries})`);
                    setTimeout(checkAndCreate, 1000);
                } else {
                    console.error('[BANK-ADVISER] Failed to initialize after maximum retries');
                    this.showMessage('Failed to initialize Bank Adviser Manager', 'error');
                }
            }
        };

        // Start checking
        checkAndCreate();
    }

    /**
     * Initialize manual session creation system
     */
    initializeManualSessionCreation() {
        // Show the Start Session button when month/year are selected
        this.updateSessionCreationVisibility();

        // Update period status to indicate manual session creation
        const periodStatus = document.getElementById('period-status');
        if (periodStatus) {
            periodStatus.innerHTML = `
                <i class="fas fa-info-circle"></i>
                <span>Select month and year, then click "Start Session"</span>
            `;
        }
    }

    /**
     * Update visibility of session creation button based on month/year selection
     */
    updateSessionCreationVisibility() {
        const currentMonth = document.getElementById('current-month')?.value;
        const currentYear = document.getElementById('current-year')?.value;
        const sessionCreation = document.getElementById('session-creation');
        const periodStatus = document.getElementById('period-status');

        if (currentMonth && currentYear && !this.currentSession) {
            // Show Start Session button
            if (sessionCreation) {
                sessionCreation.style.display = 'block';
            }

            if (periodStatus) {
                periodStatus.innerHTML = `
                    <i class="fas fa-info-circle" style="color: #17a2b8;"></i>
                    <span style="color: #17a2b8;">Ready to create session for ${currentMonth} ${currentYear}</span>
                `;
            }
        } else if (this.currentSession) {
            // Hide Start Session button if session exists
            if (sessionCreation) {
                sessionCreation.style.display = 'none';
            }
        } else {
            // Hide Start Session button if month/year not selected
            if (sessionCreation) {
                sessionCreation.style.display = 'none';
            }

            if (periodStatus) {
                periodStatus.innerHTML = `
                    <i class="fas fa-info-circle"></i>
                    <span>Select month and year to begin</span>
                `;
            }
        }
    }

    /**
     * Create new Bank Adviser session manually (triggered by Start Session button)
     */
    async createManualSession() {
        const currentMonth = document.getElementById('current-month').value;
        const currentYear = parseInt(document.getElementById('current-year').value);

        if (!currentMonth || !currentYear) {
            this.showMessage('Please select both month and year', 'warning');
            return;
        }

        // Auto-generate session name
        const sessionName = `${currentMonth} ${currentYear} Bank Advice`;

        // Disable the Start Session button during creation
        const startSessionBtn = document.getElementById('start-session-btn');
        if (startSessionBtn) {
            startSessionBtn.disabled = true;
            startSessionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Session...';
        }

        try {
            const result = await window.electronAPI.invoke('bank-adviser-create-session', {
                sessionName,
                currentMonth,
                currentYear
            });

            if (result.success) {
                this.currentSession = result.session;

                // Update period status
                const periodStatus = document.getElementById('period-status');
                if (periodStatus) {
                    periodStatus.innerHTML = `
                        <i class="fas fa-check-circle" style="color: #28a745;"></i>
                        <span style="color: #28a745;">Session: ${sessionName}</span>
                    `;
                }

                // Hide the Start Session button
                const sessionCreation = document.getElementById('session-creation');
                if (sessionCreation) {
                    sessionCreation.style.display = 'none';
                }

                this.showMessage(`Session created: ${sessionName}`, 'success');
                this.showSectionSelection();
            } else {
                this.showMessage(`Failed to create session: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Error creating session: ${error.message}`, 'error');
        } finally {
            // Re-enable the Start Session button
            if (startSessionBtn) {
                startSessionBtn.disabled = false;
                startSessionBtn.innerHTML = '<i class="fas fa-play"></i> Start Session';
            }
        }
    }

    /**
     * Create new Bank Adviser session (Legacy method - kept for compatibility)
     */
    async createSession() {
        // This method is now replaced by createManualSession
        await this.createManualSession();
    }

    /**
     * Show section selection interface
     */
    showSectionSelection() {
        document.getElementById('section-selection').style.display = 'block';
        document.getElementById('document-processing').style.display = 'block';

        // Initialize processing controls
        this.processingStatus = 'idle';
        this.updateProcessingControls();
        this.updateProgress(0, 'Ready to process documents');
    }

    /**
     * Add custom section
     */
    addCustomSection() {
        const customSectionInput = document.getElementById('custom-section');
        const customSection = customSectionInput?.value.trim();

        if (!customSection) {
            this.showMessage('Please enter a section name', 'warning');
            return;
        }

        if (this.availableSections.includes(customSection.toUpperCase())) {
            this.showMessage('Section already exists', 'warning');
            return;
        }

        // Add to available sections
        this.availableSections.push(customSection.toUpperCase());

        // Add to UI
        const sectionsGrid = document.querySelector('.sections-grid');
        if (sectionsGrid) {
            const newCheckbox = document.createElement('label');
            newCheckbox.className = 'section-checkbox';
            newCheckbox.innerHTML = `
                <input type="checkbox" value="${customSection.toUpperCase()}" class="section-select bank-adviser-section" data-bank-adviser="true">
                <span>${customSection.toUpperCase()}</span>
            `;
            sectionsGrid.appendChild(newCheckbox);

            // Attach listener to the new checkbox
            const checkbox = newCheckbox.querySelector('.section-select');
            if (checkbox) {
                this.attachSingleSectionListener(checkbox);
            }

            customSectionInput.value = '';
            this.showMessage(`Section "${customSection.toUpperCase()}" added successfully`, 'success');
        }
    }

    /**
     * Start processing all uploaded documents
     */
    async startProcessing() {
        if (!this.currentSession) {
            this.showMessage('Please create a session first', 'error');
            return;
        }

        this.processingStatus = 'processing';
        this.updateProcessingControls();
        this.updateProgress(0, 'Starting document processing...');

        try {
            // Process documents in order: Final Adjusted → Allowances → Awards
            const processingQueue = [];

            // Check for uploaded files
            const finalAdjustedFile = document.getElementById('final-adjusted-file').files[0];
            const allowancesFile = document.getElementById('allowances-file').files[0];
            const awardsFile = document.getElementById('awards-file').files[0];

            if (finalAdjustedFile) {
                processingQueue.push({ file: finalAdjustedFile, type: 'final_adjusted' });
            }
            if (allowancesFile) {
                processingQueue.push({ file: allowancesFile, type: 'allowances' });
            }
            if (awardsFile) {
                processingQueue.push({ file: awardsFile, type: 'awards' });
            }

            if (processingQueue.length === 0) {
                this.showMessage('No documents uploaded for processing', 'error');
                this.processingStatus = 'idle';
                this.updateProcessingControls();
                return;
            }

            // Process each document
            for (let i = 0; i < processingQueue.length; i++) {
                if (this.processingStatus === 'stopped') {
                    break;
                }

                const { file, type } = processingQueue[i];
                const progress = ((i + 1) / processingQueue.length) * 100;

                this.updateProgress(progress, `Processing ${type.replace('_', ' ')} document...`);
                await this.processDocument(file, type);
            }

            if (this.processingStatus !== 'stopped') {
                this.updateProgress(100, 'All documents processed successfully!');
                this.showMessage('Document processing completed', 'success');

                // Enable Excel generation if Final Adjusted is processed
                this.checkGenerationReadiness();

                // Auto-generate Excel if all documents are processed
                if (this.documentsProcessed.finalAdjusted) {
                    this.updateProgress(100, 'Ready to generate Excel Bank Advice');
                    this.showMessage('Ready to generate Excel Bank Advice', 'info');
                }
            }

        } catch (error) {
            this.showMessage(`Processing error: ${error.message}`, 'error');
        } finally {
            this.processingStatus = 'idle';
            this.updateProcessingControls();
        }
    }

    /**
     * Stop processing
     */
    stopProcessing() {
        this.processingStatus = 'stopped';
        this.updateProcessingControls();
        this.updateProgress(0, 'Processing stopped by user');
        this.showMessage('Processing stopped', 'warning');
    }

    /**
     * Update processing controls
     */
    updateProcessingControls() {
        const startBtn = document.getElementById('start-processing-btn');
        const stopBtn = document.getElementById('stop-processing-btn');

        if (startBtn && stopBtn) {
            startBtn.disabled = this.processingStatus === 'processing';
            stopBtn.disabled = this.processingStatus !== 'processing';
        }
    }

    /**
     * Update progress display
     */
    updateProgress(percentage, message) {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        const generationProgress = document.getElementById('generation-progress');

        // Ensure percentage is within bounds
        const safePercentage = Math.min(100, Math.max(0, percentage));

        if (progressFill) {
            progressFill.style.width = `${safePercentage}%`;
            progressFill.style.backgroundColor = safePercentage >= 100 ? '#28a745' : '#007bff';
            progressFill.style.transition = 'width 0.3s ease';
        }

        if (progressText) {
            progressText.textContent = message || `Processing... ${safePercentage}%`;
        }

        // Also update generation progress if it exists
        if (generationProgress && !generationProgress.innerHTML.includes('success')) {
            generationProgress.innerHTML = `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${safePercentage}%; background-color: ${safePercentage >= 100 ? '#28a745' : '#007bff'}; transition: width 0.3s ease;"></div>
                </div>
                <div class="progress-text">${message || `Processing... ${safePercentage}%`}</div>
            `;
        }

        console.log(`[PROGRESS] ${safePercentage}% - ${message}`);
    }

    /**
     * Handle file upload (prepare for processing)
     */
    handleFileUpload(file, documentType) {
        if (!this.currentSession) {
            this.showMessage('Please create a session first', 'error');
            return;
        }

        // Store the file for processing
        const statusElement = document.getElementById(`${documentType.replace('_', '-')}-status`);
        statusElement.innerHTML = `<div class="info">📄 File uploaded: ${file.name}</div>`;

        // Enable start processing button
        const startBtn = document.getElementById('start-processing-btn');
        if (startBtn) {
            startBtn.disabled = false;
        }

        this.showMessage(`${documentType.replace('_', ' ')} file uploaded successfully`, 'success');
    }

    /**
     * Process uploaded document
     */
    async processDocument(file, documentType) {
        if (!this.currentSession) {
            this.showMessage('Please create a session first', 'error');
            return;
        }

        const statusElement = document.getElementById(`${documentType.replace('_', '-')}-status`);
        statusElement.innerHTML = '<div class="processing">🔄 Processing...</div>';

        try {
            let taxRate = null;
            if (documentType === 'allowances') {
                taxRate = parseFloat(document.getElementById('allowances-tax-rate').value);
            } else if (documentType === 'awards') {
                taxRate = parseFloat(document.getElementById('awards-tax-rate').value);
            }

            const selectedSections = this.getSelectedSections();

            const result = await window.electronAPI.invoke('bank-adviser-process-document', {
                filePath: file.path,
                fileName: file.name,
                documentType,
                taxRate,
                selectedSections
            });

            if (result.success) {
                // Convert document type to camelCase for documentsProcessed flags
                const flagName = documentType === 'final_adjusted' ? 'finalAdjusted' :
                                documentType === 'allowances' ? 'allowances' : 'awards';
                this.documentsProcessed[flagName] = true;

                console.log(`[BANK-ADVISER] Document processed successfully: ${documentType}`);
                console.log(`[BANK-ADVISER] Updated documentsProcessed:`, this.documentsProcessed);

                statusElement.innerHTML = `<div class="success">✅ Processed: ${result.count || 0} items</div>`;
                this.updateSessionStatus(documentType, result.count || 0);
                this.checkGenerationReadiness();
            } else {
                statusElement.innerHTML = `<div class="error">❌ Error: ${result.error}</div>`;
            }
        } catch (error) {
            statusElement.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
        }
    }

    /**
     * Get selected sections
     */
    getSelectedSections() {
        const checkboxes = document.querySelectorAll('.section-select:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    /**
     * Update session status display
     */
    updateSessionStatus(documentType, count) {
        const statusMap = {
            'final_adjusted': 'final-adjusted-count',
            'allowances': 'allowances-count',
            'awards': 'awards-count'
        };

        const elementId = statusMap[documentType];
        if (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                const unit = documentType === 'final_adjusted' ? 'employees' : 'items';
                element.textContent = `${count} ${unit}`;
            }
        }
    }

    /**
     * Check if ready for bank advice generation
     */
    checkGenerationReadiness() {
        console.log('[BANK-ADVISER] Checking generation readiness...');
        console.log('[BANK-ADVISER] Documents processed:', this.documentsProcessed);

        const correctGenerateBtn = document.getElementById('generate-correct-bank-advice-btn');
        const generateBtn = document.getElementById('generate-bank-advice-btn');
        const autoGenerateBtn = document.getElementById('auto-generate-excel-btn');
        const readyIndicator = document.getElementById('ready-indicator');
        const bankAdviceGeneration = document.getElementById('bank-advice-generation');

        if (this.documentsProcessed.finalAdjusted) {
            console.log('[BANK-ADVISER] Final Adjusted processed - enabling generation buttons');

            // Safely enable buttons only if they exist
            if (correctGenerateBtn) correctGenerateBtn.disabled = false;
            if (generateBtn) generateBtn.disabled = false;
            if (autoGenerateBtn) autoGenerateBtn.disabled = false;
            if (bankAdviceGeneration) bankAdviceGeneration.style.display = 'block';
            if (readyIndicator) readyIndicator.style.display = 'block';

            // Show success message
            this.showMessage('🎉 Ready to generate CORRECT Excel Bank Advice! Click the CORRECT button.', 'success');
        } else {
            console.log('[BANK-ADVISER] Final Adjusted not processed yet - buttons remain disabled');
        }
    }

    /**
     * Show transform screen with processing details
     */
    showTransformScreen() {
        const transformScreen = document.getElementById('transform-screen');
        if (transformScreen) {
            transformScreen.style.display = 'block';

            // Initialize transform screen
            this.updateTransformProgress(0, 'Initializing transform...');
            this.addTransformLog('Transform screen opened', 'info');
        }
    }

    /**
     * Close transform screen (now just hides it since it's embedded)
     */
    closeTransformScreen() {
        const transformScreen = document.getElementById('transform-screen');
        if (transformScreen) {
            transformScreen.style.display = 'none';
        }
    }

    /**
     * Update transform progress
     */
    updateTransformProgress(percentage, message) {
        const progressFill = document.getElementById('transform-progress-fill');
        const progressText = document.getElementById('transform-progress-text');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
            progressFill.style.backgroundColor = percentage >= 100 ? '#28a745' : '#007bff';
        }

        if (progressText) {
            progressText.textContent = message;
        }
    }

    /**
     * Add log entry to transform screen
     */
    addTransformLog(message, type = 'info') {
        const logContainer = document.getElementById('log-container');
        if (logContainer) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;

            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';

            logEntry.innerHTML = `
                <span class="log-time">${timestamp}</span>
                <span class="log-icon">${icon}</span>
                <span class="log-message">${message}</span>
            `;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    }

    /**
     * Update transform details
     */
    updateTransformDetails(data) {
        // Safely update transform details only if elements exist
        const finalCountEl = document.getElementById('transform-final-count');
        const allowanceCountEl = document.getElementById('transform-allowance-count');
        const awardCountEl = document.getElementById('transform-award-count');
        const totalCountEl = document.getElementById('transform-total-count');
        const bankCountEl = document.getElementById('transform-bank-count');
        const statusEl = document.getElementById('transform-status');

        if (finalCountEl) finalCountEl.textContent = data.finalCount || 0;
        if (allowanceCountEl) allowanceCountEl.textContent = data.allowanceCount || 0;
        if (awardCountEl) awardCountEl.textContent = data.awardCount || 0;
        if (totalCountEl) totalCountEl.textContent = data.totalCount || 0;
        if (bankCountEl) bankCountEl.textContent = data.bankCount || 0;
        if (statusEl) statusEl.textContent = data.status || 'Processing';
    }

    /**
     * Add generated report with eye icon
     */
    addGeneratedReport(reportData) {
        const reportsSection = document.getElementById('generated-reports');
        const reportsList = document.getElementById('reports-list');

        if (reportsSection && reportsList) {
            reportsSection.style.display = 'block';

            const reportItem = document.createElement('div');
            reportItem.className = 'report-item';
            reportItem.innerHTML = `
                <div class="report-info">
                    <div class="report-name">
                        <i class="fas fa-file-excel"></i>
                        <span>${reportData.fileName}</span>
                    </div>
                    <div class="report-details">
                        <span class="report-type">${reportData.type}</span>
                        <span class="report-time">${reportData.timestamp}</span>
                    </div>
                </div>
                <div class="report-actions">
                    <button class="eye-btn" onclick="bankAdviser.openReportFile('${reportData.filePath.replace(/\\/g, '\\\\')}')" title="View Report">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="folder-btn" onclick="bankAdviser.openReportLocation('${reportData.filePath.replace(/\\/g, '\\\\')}')" title="Open Folder">
                        <i class="fas fa-folder-open"></i>
                    </button>
                </div>
            `;

            reportsList.appendChild(reportItem);
        }
    }

    /**
     * Open report file with proper error handling
     */
    async openReportFile(filePath) {
        try {
            console.log(`[BANK-ADVISER] Opening file: ${filePath}`);
            const result = await window.electronAPI.invoke('open-file', filePath);

            if (!result.success) {
                console.error(`[BANK-ADVISER] Failed to open file: ${result.error}`);
                this.showMessage(`Failed to open file: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error(`[BANK-ADVISER] Error opening file: ${error}`);
            this.showMessage(`Error opening file: ${error.message}`, 'error');
        }
    }

    /**
     * Open report file location with proper error handling
     */
    async openReportLocation(filePath) {
        try {
            console.log(`[BANK-ADVISER] Opening file location: ${filePath}`);
            const result = await window.electronAPI.invoke('open-file-location', filePath);

            if (!result.success) {
                console.error(`[BANK-ADVISER] Failed to open file location: ${result.error}`);
                this.showMessage(`Failed to open file location: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error(`[BANK-ADVISER] Error opening file location: ${error}`);
            this.showMessage(`Error opening file location: ${error.message}`, 'error');
        }
    }

    /**
     * Generate CORRECT Excel Bank Advice - FIXED TO MATCH EXACT REQUIREMENTS (with Transform Screen)
     */
    async generateCorrectBankAdvice() {
        console.log('[BANK-ADVISER] Generate CORRECT Excel Bank Advice button clicked!');

        if (!this.currentSession) {
            console.log('[BANK-ADVISER] No active session found');
            this.showMessage('No active session found', 'error');
            return;
        }

        console.log('[BANK-ADVISER] Current session:', this.currentSession);
        console.log('[BANK-ADVISER] Documents processed:', this.documentsProcessed);

        try {
            // Show transform screen
            this.showTransformScreen();
            this.updateTransformProgress(10, 'Retrieving session data...');
            this.addTransformLog('Starting CORRECT Excel Bank Advice generation', 'info');

            // Check if we have processed documents
            const hasData = this.documentsProcessed?.finalAdjusted ||
                           this.documentsProcessed?.allowances ||
                           this.documentsProcessed?.awards;

            if (!hasData) {
                this.addTransformLog('No processed documents found', 'error');
                this.showMessage('No processed documents found. Please process documents first.', 'error');
                return;
            }

            // Auto-select default file path
            const timestamp = new Date().toISOString().slice(0, 10);
            const defaultPath = `CORRECT_Bank_Advice_${this.currentSession.sessionName.replace(/\s+/g, '_')}_${timestamp}.xlsx`;

            this.updateTransformProgress(30, 'Generating CORRECT Excel Bank Advice...');
            this.addTransformLog(`Output file: ${defaultPath}`, 'info');

            // Generate the CORRECT Excel Bank Advice using the new method
            const result = await window.electronAPI.invoke('bank-adviser-generate-excel', {
                outputPath: defaultPath,
                generationType: 'composite'
            });

            this.updateTransformProgress(70, 'Processing CORRECT Excel generation...');

            if (result.success) {
                this.updateTransformProgress(100, 'CORRECT Excel Bank Advice generated successfully!');
                this.addTransformLog(`Excel file created: ${result.filePath}`, 'success');

                // Update transform details
                this.updateTransformDetails({
                    finalCount: result.stats?.finalCount || 0,
                    allowanceCount: result.stats?.allowanceCount || 0,
                    awardCount: result.stats?.awardCount || 0,
                    totalCount: result.recordsCount || 0,
                    bankCount: result.stats?.bankCount || 0,
                    status: 'Completed'
                });

                const progressElement = document.getElementById('generation-progress');
                if (progressElement) {
                    progressElement.innerHTML = `
                        <div class="success">
                            ✅ Generated CORRECT Excel Bank Advice
                            <br>📁 File saved: ${result.filePath}
                            <br>📊 Records: ${result.recordsCount || 0}
                            <br>🎯 Format: EXACT B-K Column Structure with Bank Grouping
                        </div>
                    `;
                }

                // Add report to generated reports section with eye icon
                this.addGeneratedReport({
                    fileName: result.filePath.split('\\').pop() || result.filePath.split('/').pop(),
                    filePath: result.filePath,
                    type: 'Bank Advice Excel',
                    timestamp: new Date().toLocaleString()
                });

                this.showMessage(`🎉 Successfully generated CORRECT Excel Bank Advice!`, 'success');
            } else {
                this.updateTransformProgress(0, 'Failed to generate CORRECT Excel Bank Advice');
                this.addTransformLog(`Generation failed: ${result.error}`, 'error');
                this.showMessage(`❌ CORRECT Excel generation failed: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('CORRECT Excel generation error:', error);
            this.updateTransformProgress(0, 'Error generating CORRECT Excel Bank Advice');
            this.addTransformLog(`Error: ${error.message}`, 'error');
            this.showMessage(`❌ CORRECT Excel generation failed: ${error.message}`, 'error');
        }
    }

    /**
     * Auto-generate Excel Bank Advice (simplified one-click) - OLD METHOD
     */
    async autoGenerateExcel() {
        console.log('[BANK-ADVISER] Auto-Generate Excel button clicked!');

        if (!this.currentSession) {
            console.log('[BANK-ADVISER] No active session found');
            this.showMessage('No active session found', 'error');
            return;
        }

        console.log('[BANK-ADVISER] Current session:', this.currentSession);
        console.log('[BANK-ADVISER] Documents processed:', this.documentsProcessed);

        try {
            this.updateProgress(10, 'Starting Excel Bank Advice generation...');

            // Check if we have processed documents
            const hasData = this.documentsProcessed?.finalAdjusted ||
                           this.documentsProcessed?.allowances ||
                           this.documentsProcessed?.awards;

            if (!hasData) {
                this.showMessage('No processed documents found. Please process documents first.', 'error');
                return;
            }

            // Auto-select default file path
            const timestamp = new Date().toISOString().slice(0, 10);
            const defaultPath = `Bank_Advice_${this.currentSession.sessionName.replace(/\s+/g, '_')}_${timestamp}.xlsx`;

            this.updateProgress(30, 'Generating Excel files...');

            // Get selected sections
            const selectedSections = this.getSelectedSections();
            const generationType = document.querySelector('input[name="generation-type"]:checked')?.value || 'composite';

            // Generate all available document types
            const generationPromises = [];

            if (this.documentsProcessed.finalAdjusted) {
                generationPromises.push(
                    window.electronAPI.invoke('bank-adviser-generate-excel', {
                        adviceType: 'final_adjusted',
                        outputPath: defaultPath.replace('.xlsx', '_Final_Adjusted.xlsx'),
                        selectedSections,
                        generationType
                    })
                );
            }

            if (this.documentsProcessed.allowances) {
                generationPromises.push(
                    window.electronAPI.invoke('bank-adviser-generate-excel', {
                        adviceType: 'allowances',
                        outputPath: defaultPath.replace('.xlsx', '_Allowances.xlsx'),
                        generationType
                    })
                );
            }

            if (this.documentsProcessed.awards) {
                generationPromises.push(
                    window.electronAPI.invoke('bank-adviser-generate-excel', {
                        adviceType: 'awards',
                        outputPath: defaultPath.replace('.xlsx', '_Awards.xlsx'),
                        generationType
                    })
                );
            }

            this.updateProgress(70, 'Processing Excel generation...');

            // Execute all generations
            const results = await Promise.all(generationPromises);

            // Check results
            const successfulGenerations = results.filter(r => r.success);
            const failedGenerations = results.filter(r => !r.success);

            if (successfulGenerations.length > 0) {
                this.updateProgress(100, 'Excel Bank Advice generated successfully!');

                const progressElement = document.getElementById('generation-progress');
                if (progressElement) {
                    progressElement.innerHTML = `
                        <div class="success">
                            ✅ Generated ${successfulGenerations.length} Excel Bank Advice file(s)
                            <br>📁 Files saved to Downloads folder
                            <br>📊 Total Banks: ${successfulGenerations[0].data?.banks_count || 0}
                            <br>👥 Total Employees: ${successfulGenerations[0].data?.total_employees || 0}
                        </div>
                    `;
                }

                this.showBankAdvicePreview(successfulGenerations);
                this.showMessage(`🎉 Successfully generated ${successfulGenerations.length} Excel Bank Advice file(s)!`, 'success');
            }

            if (failedGenerations.length > 0) {
                const errorMessages = failedGenerations.map(r => r.error).join(', ');
                this.showMessage(`⚠️ Some generations failed: ${errorMessages}`, 'error');
            }

        } catch (error) {
            console.error('Auto Excel generation error:', error);
            this.showMessage(`❌ Excel generation failed: ${error.message}`, 'error');
            this.updateProgress(0, 'Excel generation failed');
        }
    }

    /**
     * Generate Excel Bank Advice (manual with file dialog)
     */
    async generateBankAdvice() {
        console.log('[BANK-ADVISER] Generate Bank Advice button clicked!');

        if (!this.currentSession) {
            console.log('[BANK-ADVISER] No active session found');
            this.showMessage('No active session', 'error');
            return;
        }

        console.log('[BANK-ADVISER] Current session:', this.currentSession);
        console.log('[BANK-ADVISER] Documents processed:', this.documentsProcessed);

        const progressElement = document.getElementById('generation-progress');
        if (progressElement) {
            progressElement.innerHTML = '<div class="processing">🔄 Generating Excel Bank Advice...</div>';
        }

        try {
            const selectedSections = this.getSelectedSections();
            const generationType = document.querySelector('input[name="generation-type"]:checked').value;

            // Show file save dialog
            const result = await window.electronAPI.showSaveDialog({
                filters: [
                    { name: 'Excel Files', extensions: ['xlsx'] }
                ],
                defaultPath: `Bank_Advice_${this.currentSession.sessionName.replace(/\s+/g, '_')}.xlsx`
            });

            if (result.canceled) {
                if (progressElement) {
                    progressElement.innerHTML = '';
                }
                return;
            }

            // Generate different types of bank advice based on processed documents
            const generationPromises = [];

            if (this.documentsProcessed.finalAdjusted) {
                const finalAdjustedPath = result.filePath.replace('.xlsx', '_Final_Adjusted.xlsx');
                generationPromises.push(
                    window.electronAPI.invoke('bank-adviser-generate-excel', {
                        adviceType: 'final_adjusted',
                        outputPath: finalAdjustedPath,
                        selectedSections,
                        generationType
                    })
                );
            }

            if (this.documentsProcessed.allowances) {
                const allowancesPath = result.filePath.replace('.xlsx', '_Allowances.xlsx');
                generationPromises.push(
                    window.electronAPI.invoke('bank-adviser-generate-excel', {
                        adviceType: 'allowances',
                        outputPath: allowancesPath,
                        generationType
                    })
                );
            }

            if (this.documentsProcessed.awards) {
                const awardsPath = result.filePath.replace('.xlsx', '_Awards.xlsx');
                generationPromises.push(
                    window.electronAPI.invoke('bank-adviser-generate-excel', {
                        adviceType: 'awards',
                        outputPath: awardsPath,
                        generationType
                    })
                );
            }

            // Execute all generations
            const results = await Promise.all(generationPromises);

            // Check results
            const successfulGenerations = results.filter(r => r.success);
            const failedGenerations = results.filter(r => !r.success);

            if (successfulGenerations.length > 0) {
                if (progressElement) {
                    progressElement.innerHTML = `
                        <div class="success">
                            ✅ Generated ${successfulGenerations.length} Excel Bank Advice file(s)
                            <br>Total Banks: ${successfulGenerations[0].data?.banks_count || 0}
                            <br>Total Employees: ${successfulGenerations[0].data?.total_employees || 0}
                        </div>
                    `;
                }
                this.showBankAdvicePreview(successfulGenerations);
            }

            if (failedGenerations.length > 0) {
                const errorMessages = failedGenerations.map(r => r.error).join(', ');
                this.showMessage(`Some generations failed: ${errorMessages}`, 'error');
            }

        } catch (error) {
            if (progressElement) {
                progressElement.innerHTML = `<div class="error">❌ Generation failed: ${error.message}</div>`;
            }
        }
    }

    /**
     * Show bank advice preview
     */
    showBankAdvicePreview(generationResults) {
        const previewElement = document.getElementById('bank-advice-preview');

        if (!previewElement) return; // Exit if element doesn't exist

        if (generationResults && generationResults.length > 0) {
            let previewHTML = '<h4>📋 Bank Advice Generation Summary</h4>';

            generationResults.forEach((result, index) => {
                const data = result.data;
                previewHTML += `
                    <div class="generation-summary">
                        <h5>📊 Generation ${index + 1}</h5>
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-label">Banks:</span>
                                <span class="stat-value">${data.banks_count || 0}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Employees:</span>
                                <span class="stat-value">${data.total_employees || 0}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total Amount:</span>
                                <span class="stat-value">GHS ${(data.total_amount || 0).toLocaleString()}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Generation Type:</span>
                                <span class="stat-value">${data.generation_type || 'composite'}</span>
                            </div>
                        </div>
                        ${data.file_path ? `<div class="file-path">📁 ${data.file_path}</div>` : ''}
                        ${data.files ? `
                            <div class="files-list">
                                <h6>Generated Files:</h6>
                                ${data.files.map(file => `
                                    <div class="file-item">
                                        📄 ${file.bank}: ${file.employees} employees, GHS ${file.amount.toLocaleString()}
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            previewElement.innerHTML = previewHTML;
        } else {
            previewElement.innerHTML = '<div class="no-preview">No preview data available</div>';
        }
    }

    /**
     * Switch tracker category
     */
    switchTrackerCategory(category) {
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Load category data
        this.loadTrackerData(category);
    }

    /**
     * Load tracker data for category
     */
    async loadTrackerData(category) {
        try {
            const result = await window.electronAPI.invoke('bank-adviser-get-tracker-data', {
                category,
                searchTerm: document.getElementById('employee-search').value,
                periodFilter: document.getElementById('period-filter').value
            });

            if (result.success) {
                this.displayTrackerData(category, result.data);
                this.updateTrackerSummary(category, result.summary);
            } else {
                this.showMessage(`Failed to load ${category} data: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Error loading tracker data: ${error.message}`, 'error');
        }
    }

    /**
     * Display tracker data in table
     */
    displayTrackerData(category, data) {
        const tableHead = document.getElementById('tracker-table-head');
        const tableBody = document.getElementById('tracker-table-body');

        // Define column headers for each category
        const columnHeaders = {
            'in_house_loans': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Loan Type', 'Loan Amount', 'Period Acquired', 'Remarks'],
            'external_loans': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Loan Type', 'Loan Amount', 'Period Acquired'],
            'leave_claims': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period', 'Remarks'],
            'educational_subsidy': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period', 'Remarks'],
            'long_service_awards': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Award Type', 'Payable Amount', 'Period', 'Remarks'],
            'motor_vehicle_maintenance': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period', 'Remarks'],
            'duplicate_checker': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Claim Type', 'Payable Amount', '2nd Period', 'Remarks']
        };

        // Set table headers with sorting and selection
        const headers = columnHeaders[category] || [];
        tableHead.innerHTML = `
            <tr>
                ${headers.map(header => {
                    if (header === 'Select') {
                        return `<th><input type="checkbox" id="select-all-checkbox" title="Select All"></th>`;
                    } else {
                        return `<th class="sortable-header" data-column="${header}" title="Click to sort">
                            ${header} <span class="sort-indicator">⇅</span>
                        </th>`;
                    }
                }).join('')}
            </tr>
        `;

        // Set table data
        if (data && data.length > 0) {
            tableBody.innerHTML = data.map((row, index) => {
                const rowId = row.id || index;
                let rowHtml = `<tr data-id="${rowId}">`;

                // Generate cells based on headers
                headers.forEach(header => {
                    if (header === 'Select') {
                        rowHtml += `<td><input type="checkbox" class="row-checkbox" data-id="${rowId}"></td>`;
                    } else {
                        let value = '';
                        switch (header) {
                            case 'Employee No.':
                                value = row.employee_no || '';
                                break;
                            case 'Employee Name':
                                value = row.employee_name || '';
                                break;
                            case 'Department':
                                value = row.department || '';
                                break;
                            case 'Loan Type':
                                value = row.loan_type || '';
                                break;
                            case 'Allowance Type':
                                value = row.allowance_type || '';
                                break;
                            case 'Award Type':
                                value = row.award_type || '';
                                break;
                            case 'Claim Type':
                                value = row.claim_type || '';
                                break;
                            case 'Loan Amount':
                                value = row.loan_amount ? `GHS ${parseFloat(row.loan_amount).toLocaleString()}` : '';
                                break;
                            case 'Payable Amount':
                                value = row.payable_amount ? `GHS ${parseFloat(row.payable_amount).toLocaleString()}` : '';
                                break;
                            case 'Period Acquired':
                            case 'Period':
                                value = row.period_acquired || `${row.period_year}-${row.period_month}` || '';
                                break;
                            case '2nd Period':
                                value = row.second_period || '';
                                break;
                            case 'Remarks':
                                value = row.remarks || '';
                                break;
                            default:
                                value = '';
                        }
                        rowHtml += `<td>${value}</td>`;
                    }
                });

                rowHtml += '</tr>';
                return rowHtml;
            }).join('');
        } else {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="${headers.length}" class="no-data">No data found for this category</td>
                </tr>
            `;
        }
    }

    /**
     * Update tracker summary
     */
    updateTrackerSummary(category, summary) {
        const summaryElement = document.getElementById('tracker-summary');

        if (summary) {
            summaryElement.innerHTML = `
                <h4>📊 ${category.replace('_', ' ').toUpperCase()} Summary</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">Total Records:</span>
                        <span class="summary-value">${summary.totalRecords || 0}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Total Amount:</span>
                        <span class="summary-value">GHS ${(summary.totalAmount || 0).toLocaleString()}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Unique Employees:</span>
                        <span class="summary-value">${summary.uniqueEmployees || 0}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Current Period:</span>
                        <span class="summary-value">${summary.currentPeriod || 'N/A'}</span>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Search employee data
     */
    async searchEmployeeData() {
        const activeCategory = document.querySelector('.tab-btn.active').dataset.category;
        await this.loadTrackerData(activeCategory);
    }

    /**
     * Export tracker data to Excel
     */
    async exportTrackerData() {
        const activeCategory = document.querySelector('.tab-btn.active').dataset.category;
        const searchTerm = document.getElementById('employee-search').value;
        const periodFilter = document.getElementById('period-filter').value;

        try {
            const result = await window.electronAPI.invoke('bank-adviser-export-tracker', {
                category: activeCategory,
                searchTerm,
                periodFilter
            });

            if (result.success) {
                this.showMessage(`Data exported to: ${result.filePath}`, 'success');
            } else {
                this.showMessage(`Export failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Export error: ${error.message}`, 'error');
        }
    }

    /**
     * Load settings from backend
     */
    async loadSettings() {
        try {
            const result = await window.electronAPI.invoke('bank-adviser-get-settings');
            if (result.success) {
                this.taxRates = { ...this.taxRates, ...result.settings };

                // Update UI with loaded settings
                if (document.getElementById('allowances-tax-rate')) {
                    document.getElementById('allowances-tax-rate').value = this.taxRates.allowances;
                }
                if (document.getElementById('awards-tax-rate')) {
                    document.getElementById('awards-tax-rate').value = this.taxRates.awards;
                }
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }

    /**
     * Show message to user
     */
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageElement = document.getElementById('bank-adviser-message');
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'bank-adviser-message';
            messageElement.className = 'message-popup';
            document.body.appendChild(messageElement);
        }

        messageElement.className = `message-popup ${type}`;
        messageElement.textContent = message;
        messageElement.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, 5000);
    }

    /**
     * Switch tracker category
     */
    switchTrackerCategory(category) {
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`)?.classList.add('active');

        // Store current category
        this.currentTrackerCategory = category;

        // Load data for the category
        this.loadTrackerData(category);
    }

    /**
     * Load tracker data for category
     */
    async loadTrackerData(category) {
        try {
            const result = await window.electronAPI.invoke('bank-adviser-get-tracker-data', {
                category: category,
                sessionId: this.currentSession?.sessionId
            });

            if (result.success) {
                this.displayTrackerData(category, result.data);
            } else {
                this.showMessage(`Failed to load ${category} data: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Error loading tracker data: ${error.message}`, 'error');
        }
    }

    /**
     * Display tracker data in table (FIXED with proper column mapping)
     */
    displayTrackerData(category, data) {
        const tableHead = document.getElementById('tracker-table-head');
        const tableBody = document.getElementById('tracker-table-body');
        const summary = document.getElementById('tracker-summary');

        if (!tableHead || !tableBody || !summary) return;

        // Set headers based on category
        const headers = this.getTrackerHeaders(category);
        tableHead.innerHTML = `<tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>`;

        // Set data rows with proper column mapping
        if (data && data.length > 0) {
            tableBody.innerHTML = data.map((row, index) => {
                const cells = headers.map(header => {
                    // Map display headers to database columns
                    let value = '';
                    switch (header) {
                        case 'Select':
                            // Create checkbox for row selection
                            value = `<input type="checkbox" class="row-checkbox" data-id="${row.id || index}" data-employee="${row.employee_no || ''}" />`;
                            break;
                        case 'Employee No.':
                            value = row.employee_no || '';
                            break;
                        case 'Employee Name':
                            value = row.employee_name || '';
                            break;
                        case 'Department':
                            value = row.department || '';
                            break;
                        case 'Loan Type':
                            value = row.loan_type || '';
                            break;
                        case 'Loan Amount':
                            value = row.loan_amount ? `GHS ${parseFloat(row.loan_amount).toLocaleString()}` : '';
                            break;
                        case 'Allowance Type':
                            value = row.allowance_type || '';
                            break;
                        case 'Award Type':
                            value = row.award_type || '';
                            break;
                        case 'Claim Type':
                            value = row.claim_type || '';
                            break;
                        case 'Payable Amount':
                            value = row.payable_amount ? `GHS ${parseFloat(row.payable_amount).toLocaleString()}` : '';
                            break;
                        case 'Period Acquired':
                            value = row.period_acquired || `${row.period_month || ''} ${row.period_year || ''}`.trim();
                            break;
                        case '2nd Period':
                            value = row.second_period || '';
                            break;
                        case 'Remarks':
                            value = row.remarks || '';
                            break;
                        default:
                            value = row[header.toLowerCase().replace(/[^a-z0-9]/g, '_')] || '';
                    }
                    return `<td>${value}</td>`;
                });
                return `<tr data-id="${row.id || index}">${cells.join('')}</tr>`;
            }).join('');

            // Update summary with totals
            const totalAmount = data.reduce((sum, row) => {
                const amount = parseFloat(row.loan_amount || row.payable_amount || 0);
                return sum + amount;
            }, 0);

            summary.innerHTML = `
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Records:</span>
                        <span class="stat-value">${data.length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Amount:</span>
                        <span class="stat-value">GHS ${totalAmount.toLocaleString()}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Category:</span>
                        <span class="stat-value">${category.replace('_', ' ').toUpperCase()}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Unique Employees:</span>
                        <span class="stat-value">${new Set(data.map(row => row.employee_no)).size}</span>
                    </div>
                </div>
            `;
        } else {
            tableBody.innerHTML = '<tr><td colspan="100%" class="no-data">No data available for this category</td></tr>';
            summary.innerHTML = '<div class="no-data">No summary available</div>';
        }
    }

    /**
     * Get headers for tracker category (FIXED according to BANKMOD.txt requirements)
     */
    getTrackerHeaders(category) {
        const headerMap = {
            // IN HOUSE LOAN: Employee No., Employee Name, Department, Loan Type, Loan Amount, Period Acquired (Month and Year), Remarks (Monitoring)
            'in_house_loans': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Loan Type', 'Loan Amount', 'Period Acquired', 'Remarks'],

            // EXTERNAL LOANS: Employee No., Employee Name, Department, Loan Type, Loan Amount, Period Acquired (Month and Year)
            'external_loans': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Loan Type', 'Loan Amount', 'Period Acquired'],

            // LEAVE CLAIMS: Employee No., Employee Name, Department, Allowance Type, Payable Amount, Period Acquired (Month and Year), Remarks (Monitoring)
            'leave_claims': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period Acquired', 'Remarks'],

            // EDUCATIONAL SUBSIDY: Employee No., Employee Name, Department, Allowance Type, Payable Amount, Period Acquired (Month and Year), Remarks (Monitoring)
            'educational_subsidy': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period Acquired', 'Remarks'],

            // LONG SERVICE AWARDS: Employee No., Employee Name, Department, Award Type, Payable Amount, Period Acquired (Month and Year), Remarks (Monitoring)
            'long_service_awards': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Award Type', 'Payable Amount', 'Period Acquired', 'Remarks'],

            // MOTOR VEH. MAINTENAN: Employee No., Employee Name, Department, Allowance Type, Payable Amount, Period Acquired (Month and Year), Remarks (Monitoring)
            'motor_vehicle_maintenance': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period Acquired', 'Remarks'],

            // DUPLICATE CHECKER: Employee No., Employee Name, Department, Claim Type, Payable Amount, 2nd Period, Remarks
            'duplicate_checker': ['Select', 'Employee No.', 'Employee Name', 'Department', 'Claim Type', 'Payable Amount', '2nd Period', 'Remarks']
        };
        return headerMap[category] || ['Select', 'Employee No.', 'Employee Name', 'Department', 'Type', 'Amount', 'Period Acquired'];
    }

    /**
     * Search employee data
     */
    searchEmployeeData() {
        const searchTerm = document.getElementById('employee-search')?.value.trim();
        if (!searchTerm) {
            this.showMessage('Please enter a search term', 'error');
            return;
        }

        // Filter current table data
        const tableRows = document.querySelectorAll('#tracker-table-body tr');
        let visibleCount = 0;

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm.toLowerCase())) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        this.showMessage(`Found ${visibleCount} matching records`, 'info');
    }

    /**
     * Export tracker data
     */
    async exportTrackerData() {
        const activeCategory = document.querySelector('.tab-btn.active')?.dataset.category;
        if (!activeCategory) {
            this.showMessage('No category selected for export', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.invoke('bank-adviser-export-tracker', {
                category: activeCategory,
                sessionId: this.currentSession?.sessionId
            });

            if (result.success) {
                this.showMessage(`Tracker data exported successfully: ${result.filePath}`, 'success');
            } else {
                this.showMessage(`Export failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Export error: ${error.message}`, 'error');
        }
    }

    /**
     * Apply filters to current table
     */
    applyFilters() {
        const year = document.getElementById('filter-year')?.value;
        const month = document.getElementById('filter-month')?.value;
        const category = this.currentTrackerCategory || document.querySelector('.tab-btn.active')?.dataset.category;

        if (!category) {
            this.showMessage('No category selected', 'error');
            return;
        }

        // Reload data with filters
        this.loadTrackerDataWithFilters(category, { year, month });
    }

    /**
     * Load tracker data with filters
     */
    async loadTrackerDataWithFilters(category, filters = {}) {
        try {
            const result = await window.electronAPI.invoke('bank-adviser-get-tracker-data', {
                category: category,
                filters: filters
            });

            if (result.success) {
                this.displayTrackerData(category, result.data);
                this.showMessage(`Filtered to ${result.data.length} records`, 'info');
            } else {
                this.showMessage(`Failed to load filtered data: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Filter error: ${error.message}`, 'error');
        }
    }

    /**
     * Refresh current category
     */
    refreshCurrentCategory() {
        const category = this.currentTrackerCategory || document.querySelector('.tab-btn.active')?.dataset.category;
        if (category) {
            this.loadTrackerData(category);
            this.showMessage('Data refreshed', 'info');
        }
    }

    /**
     * Delete selected rows
     */
    async deleteSelectedRows() {
        const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
        const category = this.currentTrackerCategory || document.querySelector('.tab-btn.active')?.dataset.category;

        if (selectedCheckboxes.length === 0) {
            this.showMessage('No rows selected for deletion', 'warning');
            return;
        }

        if (!confirm(`Are you sure you want to delete ${selectedCheckboxes.length} selected record(s)?`)) {
            return;
        }

        const recordIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.id);

        try {
            const result = await window.electronAPI.invoke('bank-adviser-delete-records', {
                category: category,
                recordIds: recordIds
            });

            if (result.success) {
                this.showMessage(`Deleted ${recordIds.length} record(s)`, 'success');
                this.refreshCurrentCategory();
            } else {
                this.showMessage(`Delete failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Delete error: ${error.message}`, 'error');
        }
    }

    /**
     * Clear entire table
     */
    async clearCurrentTable() {
        const category = this.currentTrackerCategory || document.querySelector('.tab-btn.active')?.dataset.category;

        if (!category) {
            this.showMessage('No category selected', 'error');
            return;
        }

        const categoryName = category.replace('_', ' ').toUpperCase();
        if (!confirm(`Are you sure you want to clear ALL data from ${categoryName} table? This action cannot be undone.`)) {
            return;
        }

        try {
            const result = await window.electronAPI.invoke('bank-adviser-clear-table', {
                category: category
            });

            if (result.success) {
                this.showMessage(`${categoryName} table cleared successfully`, 'success');
                this.refreshCurrentCategory();
            } else {
                this.showMessage(`Clear table failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showMessage(`Clear table error: ${error.message}`, 'error');
        }
    }

    /**
     * Initialize Bank Adviser on page load
     */
    static initialize() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                new BankAdviserUI();
            });
        } else {
            new BankAdviserUI();
        }
    }
}

// Auto-initialize when script loads
BankAdviserUI.initialize();