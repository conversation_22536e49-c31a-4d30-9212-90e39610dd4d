#!/usr/bin/env python3
"""
Startup Synchronization Service
Ensures dictionary synchronization on system startup and provides continuous monitoring.
"""

import sys
import time
import signal
import atexit
from pathlib import Path
from production_dictionary_sync_system import ProductionDictionarySyncSystem
from production_database_manager import get_database_manager

class StartupSyncService:
    """Service that ensures dictionary synchronization on startup"""
    
    def __init__(self):
        self.sync_system = ProductionDictionarySyncSystem()
        self.db_manager = get_database_manager()
        self.running = False
        
        # Register cleanup handlers
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\nReceived signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    def startup_check(self) -> bool:
        """Perform startup synchronization check"""
        print("🚀 PAYROLL AUDITOR STARTUP SYNCHRONIZATION")
        print("=" * 50)
        
        print("\n1. Checking database integrity...")
        
        # Check both databases
        primary_valid = self.sync_system.validate_database_integrity(self.sync_system.primary_db)
        secondary_valid = self.sync_system.validate_database_integrity(self.sync_system.secondary_db)
        
        print(f"   Primary DB: {'✅ Valid' if primary_valid else '❌ Invalid'}")
        print(f"   Secondary DB: {'✅ Valid' if secondary_valid else '❌ Invalid'}")
        
        # Determine action needed
        if primary_valid and secondary_valid:
            # Both valid, check if they're synchronized
            primary_data = self.sync_system.get_dictionary_data(self.sync_system.primary_db)
            secondary_data = self.sync_system.get_dictionary_data(self.sync_system.secondary_db)
            
            primary_count = sum(len(section['items']) for section in primary_data.values()) if primary_data else 0
            secondary_count = sum(len(section['items']) for section in secondary_data.values()) if secondary_data else 0
            
            if primary_count == secondary_count:
                print("   ✅ Databases are synchronized")
                return True
            else:
                print(f"   ⚠️ Databases out of sync (Primary: {primary_count}, Secondary: {secondary_count})")
                print("\n2. Synchronizing databases...")
                return self.sync_system.perform_full_sync()
        
        elif primary_valid or secondary_valid:
            # One valid, sync to the other
            print("\n2. Synchronizing databases...")
            return self.sync_system.perform_full_sync()
        
        else:
            # Neither valid
            print("   ❌ Both databases are invalid!")
            print("   Please check your database files and restart the system.")
            return False
    
    def start(self):
        """Start the synchronization service"""
        # Perform startup check
        if not self.startup_check():
            print("❌ Startup synchronization failed!")
            return False
        
        print("\n3. Starting continuous monitoring...")
        self.sync_system.start_monitoring()
        self.running = True
        
        print("✅ Payroll Auditor synchronization service started successfully!")
        print("   - Dictionary data is synchronized")
        print("   - Toggle states will be properly respected")
        print("   - Continuous monitoring is active")
        print("\nPress Ctrl+C to stop the service.")
        
        return True
    
    def run_as_service(self):
        """Run as a background service"""
        if not self.start():
            return False
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        return True
    
    def stop(self):
        """Stop the service"""
        if self.running:
            print("\n🛑 Stopping synchronization service...")
            self.sync_system.stop_monitoring()
            self.db_manager.stop_health_monitoring()
            self.running = False
            print("   Service stopped.")
    
    def cleanup(self):
        """Cleanup on exit"""
        self.stop()

def main():
    """Main entry point"""
    service = StartupSyncService()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--check-only':
        # Just perform startup check and exit
        success = service.startup_check()
        sys.exit(0 if success else 1)
    else:
        # Run as service
        success = service.run_as_service()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
