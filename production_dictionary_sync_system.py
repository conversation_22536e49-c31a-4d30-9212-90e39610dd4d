#!/usr/bin/env python3
"""
Production Dictionary Synchronization System
Ensures high persistence and availability of dictionary data across all system components.
"""

import sqlite3
import json
import os
import shutil
import time
import threading
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class ProductionDictionarySyncSystem:
    """Production-level dictionary synchronization with high availability and persistence"""
    
    def __init__(self):
        self.setup_logging()
        self.primary_db = Path("data/templar_payroll_auditor.db")
        self.secondary_db = Path("payroll_audit.db")
        self.backup_dir = Path("dictionary_backups")
        self.sync_lock = threading.Lock()
        self.health_check_interval = 30  # seconds
        self.running = False
        
        # Ensure directories exist
        self.backup_dir.mkdir(exist_ok=True)
        Path("data").mkdir(exist_ok=True)
        
    def setup_logging(self):
        """Setup comprehensive logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('dictionary_sync.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('DictionarySync')
        
    def validate_database_integrity(self, db_path: Path) -> bool:
        """Validate database integrity and structure"""
        try:
            if not db_path.exists():
                self.logger.warning(f"Database does not exist: {db_path}")
                return False
                
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Check for required tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('dictionary_sections', 'dictionary_items')")
            tables = cursor.fetchall()
            
            if len(tables) != 2:
                self.logger.error(f"Missing dictionary tables in {db_path}")
                conn.close()
                return False
            
            # Check data integrity
            cursor.execute("SELECT COUNT(*) FROM dictionary_sections")
            sections_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dictionary_items")
            items_count = cursor.fetchone()[0]
            
            conn.close()
            
            self.logger.info(f"Database {db_path}: {sections_count} sections, {items_count} items")
            return True
            
        except Exception as e:
            self.logger.error(f"Database validation failed for {db_path}: {e}")
            return False
    
    def get_dictionary_data(self, db_path: Path) -> Optional[Dict]:
        """Extract dictionary data from database"""
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Get sections
            cursor.execute("SELECT id, section_name FROM dictionary_sections")
            sections = cursor.fetchall()
            
            dictionary_data = {}
            
            for section_id, section_name in sections:
                # Get items for this section
                cursor.execute("""
                    SELECT item_name, include_in_report, include_new, include_increase, 
                           include_decrease, include_removed, include_no_change
                    FROM dictionary_items 
                    WHERE section_id = ?
                """, (section_id,))
                
                items = cursor.fetchall()
                
                section_items = {}
                for item_name, include_report, new, inc, dec, rem, no_change in items:
                    section_items[item_name] = {
                        'include_in_report': bool(include_report),
                        'include_new': bool(new),
                        'include_increase': bool(inc),
                        'include_decrease': bool(dec),
                        'include_removed': bool(rem),
                        'include_no_change': bool(no_change)
                    }
                
                dictionary_data[section_name] = {
                    'id': section_id,
                    'items': section_items
                }
            
            conn.close()
            return dictionary_data
            
        except Exception as e:
            self.logger.error(f"Failed to extract dictionary data from {db_path}: {e}")
            return None
    
    def sync_dictionary_data(self, source_db: Path, target_db: Path) -> bool:
        """Synchronize dictionary data from source to target database"""
        try:
            with self.sync_lock:
                self.logger.info(f"Starting sync from {source_db} to {target_db}")
                
                # Validate source
                if not self.validate_database_integrity(source_db):
                    self.logger.error(f"Source database invalid: {source_db}")
                    return False
                
                # Get dictionary data from source
                dictionary_data = self.get_dictionary_data(source_db)
                if not dictionary_data:
                    self.logger.error("Failed to extract dictionary data from source")
                    return False
                
                # Create backup of target if it exists
                if target_db.exists():
                    backup_path = self.backup_dir / f"{target_db.name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                    shutil.copy2(target_db, backup_path)
                    self.logger.info(f"Created backup: {backup_path}")
                
                # Ensure target database exists and has proper structure
                self.ensure_database_structure(target_db)
                
                # Sync the data
                conn = sqlite3.connect(str(target_db))
                cursor = conn.cursor()
                
                # Clear existing dictionary data
                cursor.execute("DELETE FROM dictionary_items")
                cursor.execute("DELETE FROM dictionary_sections")
                
                # Insert sections and items
                for section_name, section_data in dictionary_data.items():
                    # Insert section
                    cursor.execute("INSERT INTO dictionary_sections (section_name) VALUES (?)", (section_name,))
                    section_id = cursor.lastrowid
                    
                    # Insert items
                    for item_name, item_data in section_data['items'].items():
                        cursor.execute("""
                            INSERT INTO dictionary_items 
                            (section_id, item_name, include_in_report, include_new, include_increase, 
                             include_decrease, include_removed, include_no_change)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            section_id, item_name,
                            item_data['include_in_report'],
                            item_data['include_new'],
                            item_data['include_increase'],
                            item_data['include_decrease'],
                            item_data['include_removed'],
                            item_data['include_no_change']
                        ))
                
                conn.commit()
                conn.close()
                
                # Validate sync
                if self.validate_database_integrity(target_db):
                    self.logger.info(f"Successfully synced dictionary data to {target_db}")
                    return True
                else:
                    self.logger.error(f"Sync validation failed for {target_db}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Dictionary sync failed: {e}")
            return False
    
    def ensure_database_structure(self, db_path: Path):
        """Ensure database has proper dictionary table structure"""
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Create dictionary_sections table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS dictionary_sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT NOT NULL UNIQUE
            )
        """)
        
        # Create dictionary_items table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS dictionary_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_id INTEGER NOT NULL,
                item_name TEXT NOT NULL,
                include_in_report BOOLEAN DEFAULT 1,
                include_new BOOLEAN DEFAULT 1,
                include_increase BOOLEAN DEFAULT 1,
                include_decrease BOOLEAN DEFAULT 1,
                include_removed BOOLEAN DEFAULT 1,
                include_no_change BOOLEAN DEFAULT 0,
                FOREIGN KEY (section_id) REFERENCES dictionary_sections (id),
                UNIQUE(section_id, item_name)
            )
        """)
        
        conn.commit()
        conn.close()
    
    def perform_full_sync(self) -> bool:
        """Perform complete synchronization between databases"""
        self.logger.info("Starting full dictionary synchronization")
        
        # Determine which database has the most complete data
        primary_valid = self.validate_database_integrity(self.primary_db)
        secondary_valid = self.validate_database_integrity(self.secondary_db)
        
        if primary_valid and not secondary_valid:
            # Sync from primary to secondary
            return self.sync_dictionary_data(self.primary_db, self.secondary_db)
        elif secondary_valid and not primary_valid:
            # Sync from secondary to primary
            return self.sync_dictionary_data(self.secondary_db, self.primary_db)
        elif primary_valid and secondary_valid:
            # Both valid, compare data counts and sync from the one with more data
            primary_data = self.get_dictionary_data(self.primary_db)
            secondary_data = self.get_dictionary_data(self.secondary_db)
            
            primary_count = sum(len(section['items']) for section in primary_data.values()) if primary_data else 0
            secondary_count = sum(len(section['items']) for section in secondary_data.values()) if secondary_data else 0
            
            if primary_count >= secondary_count:
                return self.sync_dictionary_data(self.primary_db, self.secondary_db)
            else:
                return self.sync_dictionary_data(self.secondary_db, self.primary_db)
        else:
            self.logger.error("Both databases are invalid or missing")
            return False
    
    def start_monitoring(self):
        """Start continuous monitoring and synchronization"""
        self.running = True
        self.logger.info("Starting dictionary synchronization monitoring")
        
        def monitor_loop():
            while self.running:
                try:
                    # Perform health check and sync if needed
                    primary_valid = self.validate_database_integrity(self.primary_db)
                    secondary_valid = self.validate_database_integrity(self.secondary_db)
                    
                    if primary_valid and not secondary_valid:
                        self.logger.warning("Secondary database invalid, syncing from primary")
                        self.sync_dictionary_data(self.primary_db, self.secondary_db)
                    elif secondary_valid and not primary_valid:
                        self.logger.warning("Primary database invalid, syncing from secondary")
                        self.sync_dictionary_data(self.secondary_db, self.primary_db)
                    
                    time.sleep(self.health_check_interval)
                    
                except Exception as e:
                    self.logger.error(f"Monitor loop error: {e}")
                    time.sleep(5)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.running = False
        self.logger.info("Stopped dictionary synchronization monitoring")

def main():
    """Main execution function"""
    print("🚀 PRODUCTION DICTIONARY SYNCHRONIZATION SYSTEM")
    print("=" * 60)
    
    sync_system = ProductionDictionarySyncSystem()
    
    # Perform initial full sync
    print("\n1. Performing initial synchronization...")
    success = sync_system.perform_full_sync()
    
    if success:
        print("✅ Initial synchronization completed successfully")
        
        # Validate both databases
        print("\n2. Validating synchronized databases...")
        primary_valid = sync_system.validate_database_integrity(sync_system.primary_db)
        secondary_valid = sync_system.validate_database_integrity(sync_system.secondary_db)
        
        print(f"   Primary DB ({sync_system.primary_db}): {'✅ Valid' if primary_valid else '❌ Invalid'}")
        print(f"   Secondary DB ({sync_system.secondary_db}): {'✅ Valid' if secondary_valid else '❌ Invalid'}")
        
        if primary_valid and secondary_valid:
            print("\n✅ SYNCHRONIZATION SUCCESSFUL!")
            print("   Both databases now contain identical dictionary data.")
            print("   Toggle states will now be properly respected in pre-reporting.")
            
            # Start monitoring
            print("\n3. Starting continuous monitoring...")
            sync_system.start_monitoring()
            print("   Monitoring started. Press Ctrl+C to stop.")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                sync_system.stop_monitoring()
                print("\n   Monitoring stopped.")
        else:
            print("\n❌ Synchronization validation failed")
    else:
        print("❌ Initial synchronization failed")

if __name__ == "__main__":
    main()
