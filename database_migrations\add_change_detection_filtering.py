#!/usr/bin/env python3
"""
DATABASE MIGRATION: Add Change Detection Filtering Columns
Adds change type filtering columns to dictionary_items table for enhanced noise reduction.
"""

import sqlite3
import os
import sys
from datetime import datetime

def get_database_path():
    """Get the database path"""
    # Check for common database names in current directory
    db_names = ['payroll_audit.db', 'payroll_auditor.db']

    for db_name in db_names:
        if os.path.exists(db_name):
            return db_name

    # Check in data directory
    data_dir = os.path.join(os.getcwd(), 'data')
    if os.path.exists(data_dir):
        for db_name in db_names:
            db_path = os.path.join(data_dir, db_name)
            if os.path.exists(db_path):
                return db_path

    # Default path
    return 'payroll_audit.db'

def backup_database(db_path):
    """Create a backup of the database before migration"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {e}")
        return None

def check_existing_columns(cursor):
    """Check which change detection columns already exist"""
    cursor.execute("PRAGMA table_info(dictionary_items)")
    columns = [row[1] for row in cursor.fetchall()]
    
    change_detection_columns = [
        'include_new',
        'include_increase', 
        'include_decrease',
        'include_removed',
        'include_no_change'
    ]
    
    existing = [col for col in change_detection_columns if col in columns]
    missing = [col for col in change_detection_columns if col not in columns]
    
    return existing, missing

def create_dictionary_tables_if_needed(cursor, conn):
    """Create dictionary tables if they don't exist"""

    print("\n🏗️ ENSURING DICTIONARY TABLES EXIST:")

    # Check if dictionary_sections exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dictionary_sections'")
    if not cursor.fetchone():
        print("   📝 Creating dictionary_sections table...")
        cursor.execute('''
            CREATE TABLE dictionary_sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT UNIQUE NOT NULL,
                display_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()
        print("   ✅ Created dictionary_sections table")

    # Check if dictionary_items exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='dictionary_items'")
    if not cursor.fetchone():
        print("   📝 Creating dictionary_items table with change detection columns...")
        cursor.execute('''
            CREATE TABLE dictionary_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_id INTEGER,
                item_name TEXT NOT NULL,
                standard_key TEXT,
                format_type TEXT,
                value_format TEXT,
                include_in_report BOOLEAN DEFAULT 1,
                include_new BOOLEAN DEFAULT 1,
                include_increase BOOLEAN DEFAULT 1,
                include_decrease BOOLEAN DEFAULT 1,
                include_removed BOOLEAN DEFAULT 1,
                include_no_change BOOLEAN DEFAULT 0,
                is_fixed BOOLEAN DEFAULT 0,
                validation_rules TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (section_id) REFERENCES dictionary_sections(id),
                UNIQUE(section_id, item_name)
            )
        ''')
        conn.commit()
        print("   ✅ Created dictionary_items table with change detection columns")
        return True

    return False

def add_change_detection_columns(cursor, conn):
    """Add change detection filtering columns to dictionary_items table"""

    print("\n🔧 ADDING CHANGE DETECTION FILTERING COLUMNS:")

    # First ensure tables exist
    if create_dictionary_tables_if_needed(cursor, conn):
        print("   ✅ Dictionary tables created with change detection columns!")
        return True

    # Check existing columns
    try:
        existing, missing = check_existing_columns(cursor)

        if existing:
            print(f"   ✅ Already exists: {', '.join(existing)}")

        if not missing:
            print("   ✅ All change detection columns already exist!")
            return True

        print(f"   📝 Adding columns: {', '.join(missing)}")

        # Column definitions with defaults
        column_definitions = {
            'include_new': 'BOOLEAN DEFAULT 1',
            'include_increase': 'BOOLEAN DEFAULT 1',
            'include_decrease': 'BOOLEAN DEFAULT 1',
            'include_removed': 'BOOLEAN DEFAULT 1',
            'include_no_change': 'BOOLEAN DEFAULT 0'  # Default to FALSE for NO_CHANGE
        }

        for column in missing:
            sql = f"ALTER TABLE dictionary_items ADD COLUMN {column} {column_definitions[column]}"
            print(f"   Executing: {sql}")
            cursor.execute(sql)
            conn.commit()
            print(f"   ✅ Added column: {column}")

        return True

    except Exception as e:
        print(f"   ❌ Error adding columns: {e}")
        return False

def create_indexes_for_performance(cursor, conn):
    """Create indexes for optimal query performance"""
    
    print("\n📊 CREATING PERFORMANCE INDEXES:")
    
    indexes = [
        ("idx_dict_items_change_filtering", 
         "CREATE INDEX IF NOT EXISTS idx_dict_items_change_filtering ON dictionary_items(include_new, include_increase, include_decrease, include_removed, include_no_change)"),
        ("idx_dict_items_section_filtering",
         "CREATE INDEX IF NOT EXISTS idx_dict_items_section_filtering ON dictionary_items(section_id, include_in_report)"),
        ("idx_dict_items_name_lookup",
         "CREATE INDEX IF NOT EXISTS idx_dict_items_name_lookup ON dictionary_items(item_name, include_in_report)")
    ]
    
    for index_name, sql in indexes:
        try:
            cursor.execute(sql)
            conn.commit()
            print(f"   ✅ Created index: {index_name}")
        except Exception as e:
            print(f"   ⚠️ Index creation warning for {index_name}: {e}")

def verify_migration(cursor):
    """Verify the migration was successful"""
    
    print("\n🔍 VERIFYING MIGRATION:")
    
    # Check table structure
    cursor.execute("PRAGMA table_info(dictionary_items)")
    columns = [row[1] for row in cursor.fetchall()]
    
    required_columns = [
        'include_new',
        'include_increase', 
        'include_decrease',
        'include_removed',
        'include_no_change'
    ]
    
    missing = [col for col in required_columns if col not in columns]
    
    if missing:
        print(f"   ❌ Missing columns: {', '.join(missing)}")
        return False
    
    print("   ✅ All change detection columns present")
    
    # Check default values by inserting a test record
    try:
        cursor.execute("SELECT COUNT(*) FROM dictionary_sections WHERE section_name = 'TEST_MIGRATION'")
        if cursor.fetchone()[0] == 0:
            cursor.execute("INSERT INTO dictionary_sections (section_name) VALUES ('TEST_MIGRATION')")
        
        cursor.execute("SELECT id FROM dictionary_sections WHERE section_name = 'TEST_MIGRATION'")
        section_id = cursor.fetchone()[0]
        
        cursor.execute("""
            INSERT OR REPLACE INTO dictionary_items 
            (section_id, item_name) 
            VALUES (?, 'TEST_ITEM')
        """, (section_id,))
        
        cursor.execute("""
            SELECT include_new, include_increase, include_decrease, include_removed, include_no_change
            FROM dictionary_items 
            WHERE item_name = 'TEST_ITEM'
        """)
        
        defaults = cursor.fetchone()
        expected = (1, 1, 1, 1, 0)  # include_no_change should default to 0
        
        if defaults == expected:
            print("   ✅ Default values correct")
        else:
            print(f"   ⚠️ Default values: {defaults}, expected: {expected}")
        
        # Clean up test data
        cursor.execute("DELETE FROM dictionary_items WHERE item_name = 'TEST_ITEM'")
        cursor.execute("DELETE FROM dictionary_sections WHERE section_name = 'TEST_MIGRATION'")
        
        return True
        
    except Exception as e:
        print(f"   ⚠️ Verification warning: {e}")
        return True  # Don't fail migration for verification issues

def main():
    """Main migration function"""
    
    print("🚀 CHANGE DETECTION FILTERING MIGRATION")
    print("=" * 50)
    
    # Get database path
    db_path = get_database_path()
    print(f"📁 Database path: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    # Create backup
    backup_path = backup_database(db_path)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Add change detection columns
        if not add_change_detection_columns(cursor, conn):
            print("❌ Migration failed!")
            return False
        
        # Create performance indexes
        create_indexes_for_performance(cursor, conn)
        
        # Verify migration
        if not verify_migration(cursor):
            print("⚠️ Migration verification had issues")
        
        conn.close()
        
        print("\n✅ MIGRATION COMPLETED SUCCESSFULLY!")
        print("🎯 Change detection filtering columns added to dictionary_items table")
        print("📊 Performance indexes created")
        
        if backup_path:
            print(f"💾 Backup available at: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
