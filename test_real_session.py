#!/usr/bin/env python3
"""Test with real session from real database"""

import sys
import sqlite3

def test_real_session():
    print("🔍 TESTING WITH REAL SESSION")
    print("=" * 40)
    
    # Get real session from real database
    conn = sqlite3.connect('data/templar_payroll_auditor.db')
    cursor = conn.cursor()
    
    # Get latest session
    cursor.execute('SELECT DISTINCT session_id FROM pre_reporting_results ORDER BY created_at DESC LIMIT 1')
    session_result = cursor.fetchone()
    
    if not session_result:
        print("❌ No session found in real database")
        return False
    
    real_session = session_result[0]
    print(f"Real session: {real_session}")
    
    # Check data for this session
    cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ? AND selected_for_report = 1', (real_session,))
    selected_count = cursor.fetchone()[0]
    print(f"Selected records: {selected_count}")
    
    if selected_count == 0:
        print("❌ No selected records for this session")
        return False
    
    conn.close()
    
    # Test with PhasedProcessManager
    print("\nTesting PhasedProcessManager with real session...")
    
    sys.path.append('core')
    from phased_process_manager import PhasedProcessManager
    
    manager = PhasedProcessManager(debug_mode=False)
    manager.session_id = real_session
    
    # Test the method
    selected_changes = manager._load_selected_changes_for_reporting()
    print(f"Method returned: {len(selected_changes)} changes")
    
    if selected_changes:
        print("✅ SUCCESS! Method working with real session")
        
        # Test full report generation
        result = manager.generate_final_reports(real_session)
        print(f"Report generation: {result.get('success', False)}")
        
        if result.get('success'):
            print("🎉 COMPLETE SUCCESS! Reports generated!")
            return True
        else:
            print(f"❌ Report generation failed: {result.get('error')}")
            return False
    else:
        print("❌ Method still returning 0 changes")
        return False

if __name__ == "__main__":
    success = test_real_session()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}")
    sys.exit(0 if success else 1)
