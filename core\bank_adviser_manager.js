/**
 * BANK ADVISER MANAGER
 * Core management system for Bank Adviser Module
 * Handles both Core Bank Adviser and Loan & Allowance Tracker functionality
 */

const BankAdviserDatabase = require('./bank_adviser_database');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const ExcelJS = require('exceljs');

class BankAdviserManager {
    constructor() {
        this.database = null;
        this.isInitialized = false;
        this.currentSession = null;
        this.processingStatus = 'idle';

        // Document processing queue
        this.documentQueue = [];
        this.isProcessingQueue = false;

        // Tax rate settings
        this.taxRates = {
            allowances: 5.0,    // 5% for allowances
            awards: 7.5,        // 7.5% for awards
            educational_subsidy: 5.0  // 5% specifically for educational subsidy
        };

        console.log('🏦 BANK ADVISER MANAGER INITIALIZING...');
        this.initialize();
    }

    /**
     * Initialize Bank Adviser Manager
     */
    async initialize() {
        try {
            // Initialize database
            this.database = new BankAdviserDatabase();

            // Wait for database to be ready
            let retries = 0;
            while (!this.database.isInitialized && retries < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retries++;
            }

            if (!this.database.isInitialized) {
                throw new Error('Database initialization timeout');
            }

            // Load settings
            await this.loadSettings();

            this.isInitialized = true;
            console.log('✅ BANK ADVISER MANAGER READY');

        } catch (error) {
            console.error('❌ Bank Adviser Manager initialization failed:', error);
            throw error;
        }
    }

    /**
     * Create new Bank Adviser session
     */
    async createSession(sessionName, currentMonth, currentYear, selectedSections = [], generationType = 'composite') {
        if (!this.isInitialized) {
            throw new Error('Bank Adviser Manager not initialized');
        }

        const sessionId = uuidv4();

        try {
            await this.database.runQuery(
                `INSERT INTO bank_adviser_sessions
                 (session_id, session_name, current_month, current_year, selected_sections, generation_type)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [sessionId, sessionName, currentMonth, currentYear, JSON.stringify(selectedSections), generationType]
            );

            this.currentSession = {
                sessionId,
                sessionName,
                currentMonth,
                currentYear,
                selectedSections,
                generationType,
                documentsProcessed: 0,
                totalEmployees: 0
            };

            console.log(`🏦 Created Bank Adviser session: ${sessionName} (${sessionId})`);
            return { success: true, sessionId, session: this.currentSession };

        } catch (error) {
            console.error('❌ Failed to create Bank Adviser session:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Update session sections (FIXED: Missing method)
     */
    async updateSessionSections(sessionId, selectedSections) {
        if (!this.isInitialized) {
            throw new Error('Bank Adviser Manager not initialized');
        }

        try {
            console.log(`🔄 Updating session sections for ${sessionId}:`, selectedSections);

            // Update database (FIXED: Remove updated_at column reference)
            await this.database.runQuery(
                `UPDATE bank_adviser_sessions
                 SET selected_sections = ?
                 WHERE session_id = ?`,
                [JSON.stringify(selectedSections), sessionId]
            );

            // Update current session if it matches
            if (this.currentSession && this.currentSession.sessionId === sessionId) {
                this.currentSession.selectedSections = selectedSections;
                console.log(`✅ Current session sections updated: ${selectedSections.join(', ')}`);
            }

            return {
                success: true,
                sessionId,
                selectedSections,
                message: 'Session sections updated successfully'
            };

        } catch (error) {
            console.error('❌ Failed to update session sections:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Process Final Adjusted Payslip with enhanced validation
     */
    async processFinalAdjustedPayslip(filePath, fileName, selectedSections = []) {
        if (!this.currentSession) {
            throw new Error('No active Bank Adviser session');
        }

        console.log(`📄 Processing Final Adjusted Payslip: ${fileName}`);
        console.log(`   📍 Selected sections: ${selectedSections.length > 0 ? selectedSections.join(', ') : 'All sections'}`);

        const startTime = Date.now(); // Fix: Add missing startTime variable

        try {
            // Validate file exists
            if (!fs.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Record document processing
            const documentResult = await this.database.runQuery(
                `INSERT INTO document_processing
                 (session_id, document_type, file_path, file_name, processing_status)
                 VALUES (?, ?, ?, ?, ?)`,
                [this.currentSession.sessionId, 'final_adjusted', filePath, fileName, 'processing']
            );

            const documentId = documentResult.lastID;

            // Extract data using Bank Adviser Perfect Extractor
            const extractionResult = await this.runBankAdviserExtractor(filePath, 'final_adjusted');

            if (!extractionResult.success) {
                // Update status to failed
                await this.database.runQuery(
                    `UPDATE document_processing SET processing_status = ?, total_employees = 0 WHERE id = ?`,
                    ['failed', documentId]
                );
                throw new Error(`Extraction failed: ${extractionResult.error}`);
            }

            // Validate extraction results
            const validationResult = this.validateFinalAdjustedData(extractionResult.data);
            if (!validationResult.isValid) {
                console.warn(`⚠️ Validation warnings: ${validationResult.warnings.join(', ')}`);
            }

            // Filter by selected sections if specified
            let filteredData = extractionResult.data;
            if (selectedSections.length > 0) {
                const beforeFilter = filteredData.length;
                filteredData = extractionResult.data.filter(employee =>
                    selectedSections.includes(employee.section_detected) ||
                    selectedSections.includes(employee.section)
                );
                console.log(`   🔍 Section filtering: ${beforeFilter} → ${filteredData.length} employees`);
            }

            // Store extracted data with enhanced fields
            for (const employee of filteredData) {
                await this.database.runQuery(
                    `INSERT INTO final_adjusted_data
                     (session_id, document_id, employee_no, employee_name, department, section,
                      job_title, net_pay, bank, account_no, branch, extraction_confidence)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        this.currentSession.sessionId, documentId, employee.employee_no,
                        employee.employee_name, employee.department,
                        employee.section_detected || employee.section,
                        employee.job_title, employee.net_pay, employee.bank,
                        employee.account_no, employee.branch, employee.extraction_confidence
                    ]
                );

                // Feed loan data to tracker if available
                if (employee.loans && employee.loans.length > 0) {
                    for (const loan of employee.loans) {
                        await this.feedToTracker('loans', {
                            employee_no: employee.employee_no,
                            employee_name: employee.employee_name,
                            department: employee.department,
                            loan_type: loan.loan_type,
                            balance_bf: loan.balance_bf,
                            current_deduction: loan.current_deduction,
                            outstanding_balance: loan.outstanding_balance,
                            loan_classification: loan.loan_classification
                        });
                    }
                }
            }

            // Update document processing status
            await this.database.runQuery(
                `UPDATE document_processing
                 SET processing_status = ?, total_employees = ?
                 WHERE id = ?`,
                ['completed', filteredData.length, documentId]
            );

            console.log(`✅ Final Adjusted Payslip processed: ${filteredData.length} employees`);
            console.log(`   📊 Average confidence: ${(filteredData.reduce((sum, emp) => sum + emp.extraction_confidence, 0) / filteredData.length).toFixed(2)}`);

            // Generate and save processing report
            await this.generateProcessingReport('final_adjusted', {
                documentId,
                employeesProcessed: filteredData.length,
                validationResult,
                processingTime: Date.now() - startTime
            });

            return {
                success: true,
                employeesProcessed: filteredData.length,
                documentId,
                validationResult
            };

        } catch (error) {
            console.error('❌ Final Adjusted Payslip processing failed:', error);

            // Try to update document status to failed
            try {
                await this.database.runQuery(
                    `UPDATE document_processing SET processing_status = ? WHERE session_id = ? AND file_name = ?`,
                    ['failed', this.currentSession.sessionId, fileName]
                );
            } catch (dbError) {
                console.error('❌ Failed to update document status:', dbError);
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * Validate Final Adjusted Payslip data
     */
    validateFinalAdjustedData(data) {
        const warnings = [];
        let validCount = 0;

        for (const employee of data) {
            let isValid = true;

            // Check required fields
            if (!employee.employee_no) {
                warnings.push(`Missing employee number for ${employee.employee_name || 'unknown employee'}`);
                isValid = false;
            }

            if (!employee.employee_name) {
                warnings.push(`Missing employee name for ${employee.employee_no || 'unknown employee'}`);
                isValid = false;
            }

            if (!employee.net_pay || employee.net_pay <= 0) {
                warnings.push(`Invalid net pay for ${employee.employee_no}: ${employee.net_pay}`);
                isValid = false;
            }

            if (!employee.bank) {
                warnings.push(`Missing bank information for ${employee.employee_no}`);
            }

            if (isValid) {
                validCount++;
            }
        }

        return {
            isValid: warnings.length === 0,
            validCount,
            totalCount: data.length,
            warnings
        };
    }

    /**
     * Process Allowances Payslip with enhanced validation and tax calculation
     */
    async processAllowancesPayslip(filePath, fileName, taxRate = null) {
        if (!this.currentSession) {
            throw new Error('No active Bank Adviser session');
        }

        const effectiveTaxRate = taxRate || this.taxRates.allowances;
        console.log(`🎁 Processing Allowances Payslip: ${fileName}`);
        console.log(`   📊 Tax rate: ${effectiveTaxRate}%`);

        try {
            // Validate file exists
            if (!fs.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Record document processing
            const documentResult = await this.database.runQuery(
                `INSERT INTO document_processing
                 (session_id, document_type, file_path, file_name, processing_status, tax_percentage)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [this.currentSession.sessionId, 'allowances', filePath, fileName, 'processing', effectiveTaxRate]
            );

            const documentId = documentResult.lastID;

            // Extract data using Bank Adviser Perfect Extractor
            const extractionResult = await this.runBankAdviserExtractor(filePath, 'allowances', effectiveTaxRate);

            if (!extractionResult.success) {
                await this.database.runQuery(
                    `UPDATE document_processing SET processing_status = ? WHERE id = ?`,
                    ['failed', documentId]
                );
                throw new Error(`Extraction failed: ${extractionResult.error}`);
            }

            // Validate allowances data
            const validationResult = this.validateAllowancesData(extractionResult.data);
            if (!validationResult.isValid) {
                console.warn(`⚠️ Validation warnings: ${validationResult.warnings.join(', ')}`);
            }

            // Store extracted allowances data with enhanced tax calculation
            for (const allowance of extractionResult.data) {
                // Use the tax amount from extractor or calculate if missing
                const taxAmount = allowance.allowance_tax_amount ||
                                (allowance.allowance_gross_amount * (effectiveTaxRate / 100));

                await this.database.runQuery(
                    `INSERT INTO allowances_data
                     (session_id, document_id, employee_no, employee_name, department,
                      allowance_type, gross_amount, tax_percentage, tax_amount, payable_amount, extraction_confidence)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        this.currentSession.sessionId, documentId, allowance.employee_no,
                        allowance.employee_name, allowance.department, allowance.allowance_type,
                        allowance.allowance_gross_amount, effectiveTaxRate, taxAmount,
                        allowance.allowance_payable_amount, allowance.extraction_confidence
                    ]
                );

                // Feed to Loan & Allowance Tracker
                await this.feedToTracker('allowances', allowance);
            }

            // Update document processing status
            await this.database.runQuery(
                `UPDATE document_processing
                 SET processing_status = ?, total_employees = ?
                 WHERE id = ?`,
                ['completed', extractionResult.data.length, documentId]
            );

            // Calculate summary statistics
            const totalGross = extractionResult.data.reduce((sum, item) => sum + item.allowance_gross_amount, 0);
            const totalTax = extractionResult.data.reduce((sum, item) => sum + (item.allowance_tax_amount || 0), 0);
            const totalPayable = extractionResult.data.reduce((sum, item) => sum + item.allowance_payable_amount, 0);

            console.log(`✅ Allowances Payslip processed: ${extractionResult.data.length} allowances`);
            console.log(`   💰 Total Gross: ${totalGross.toLocaleString()}, Tax: ${totalTax.toLocaleString()}, Payable: ${totalPayable.toLocaleString()}`);

            return {
                success: true,
                allowancesProcessed: extractionResult.data.length,
                documentId,
                summary: { totalGross, totalTax, totalPayable },
                validationResult
            };

        } catch (error) {
            console.error('❌ Allowances Payslip processing failed:', error);

            try {
                await this.database.runQuery(
                    `UPDATE document_processing SET processing_status = ? WHERE session_id = ? AND file_name = ?`,
                    ['failed', this.currentSession.sessionId, fileName]
                );
            } catch (dbError) {
                console.error('❌ Failed to update document status:', dbError);
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * Validate Allowances data
     */
    validateAllowancesData(data) {
        const warnings = [];
        let validCount = 0;

        for (const allowance of data) {
            let isValid = true;

            if (!allowance.employee_no) {
                warnings.push(`Missing employee number for allowance ${allowance.allowance_type}`);
                isValid = false;
            }

            if (!allowance.allowance_type) {
                warnings.push(`Missing allowance type for ${allowance.employee_no}`);
                isValid = false;
            }

            if (!allowance.allowance_gross_amount || allowance.allowance_gross_amount <= 0) {
                warnings.push(`Invalid gross amount for ${allowance.employee_no}: ${allowance.allowance_gross_amount}`);
                isValid = false;
            }

            if (!allowance.allowance_payable_amount || allowance.allowance_payable_amount <= 0) {
                warnings.push(`Invalid payable amount for ${allowance.employee_no}: ${allowance.allowance_payable_amount}`);
                isValid = false;
            }

            if (isValid) {
                validCount++;
            }
        }

        return {
            isValid: warnings.length === 0,
            validCount,
            totalCount: data.length,
            warnings
        };
    }

    /**
     * Process Awards Payslip with enhanced validation and tax calculation
     */
    async processAwardsPayslip(filePath, fileName, taxRate = null) {
        if (!this.currentSession) {
            throw new Error('No active Bank Adviser session');
        }

        const effectiveTaxRate = taxRate || this.taxRates.awards;
        console.log(`🏆 Processing Awards Payslip: ${fileName}`);
        console.log(`   📊 Tax rate: ${effectiveTaxRate}%`);

        try {
            // Validate file exists
            if (!fs.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Record document processing
            const documentResult = await this.database.runQuery(
                `INSERT INTO document_processing
                 (session_id, document_type, file_path, file_name, processing_status, tax_percentage)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [this.currentSession.sessionId, 'awards', filePath, fileName, 'processing', effectiveTaxRate]
            );

            const documentId = documentResult.lastID;

            // Extract data using Bank Adviser Perfect Extractor
            const extractionResult = await this.runBankAdviserExtractor(filePath, 'awards', effectiveTaxRate);

            if (!extractionResult.success) {
                await this.database.runQuery(
                    `UPDATE document_processing SET processing_status = ? WHERE id = ?`,
                    ['failed', documentId]
                );
                throw new Error(`Extraction failed: ${extractionResult.error}`);
            }

            // Validate awards data
            const validationResult = this.validateAwardsData(extractionResult.data);
            if (!validationResult.isValid) {
                console.warn(`⚠️ Validation warnings: ${validationResult.warnings.join(', ')}`);
            }

            // Store extracted awards data with enhanced tax calculation
            for (const award of extractionResult.data) {
                // Use the tax amount from extractor or calculate if missing
                const taxAmount = award.award_tax_amount ||
                                (award.award_gross_amount * (effectiveTaxRate / 100));

                await this.database.runQuery(
                    `INSERT INTO awards_data
                     (session_id, document_id, employee_no, employee_name, department,
                      award_type, gross_amount, tax_percentage, tax_amount, payable_amount, net_pay, extraction_confidence)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        this.currentSession.sessionId, documentId, award.employee_no,
                        award.employee_name, award.department, award.award_type,
                        award.award_gross_amount, effectiveTaxRate, taxAmount,
                        award.award_payable_amount, award.net_pay, award.extraction_confidence
                    ]
                );

                // Feed to Loan & Allowance Tracker
                await this.feedToTracker('awards', award);
            }

            // Update document processing status
            await this.database.runQuery(
                `UPDATE document_processing
                 SET processing_status = ?, total_employees = ?
                 WHERE id = ?`,
                ['completed', extractionResult.data.length, documentId]
            );

            // Calculate summary statistics
            const totalGross = extractionResult.data.reduce((sum, item) => sum + item.award_gross_amount, 0);
            const totalTax = extractionResult.data.reduce((sum, item) => sum + (item.award_tax_amount || 0), 0);
            const totalPayable = extractionResult.data.reduce((sum, item) => sum + item.award_payable_amount, 0);

            console.log(`✅ Awards Payslip processed: ${extractionResult.data.length} awards`);
            console.log(`   💰 Total Gross: ${totalGross.toLocaleString()}, Tax: ${totalTax.toLocaleString()}, Payable: ${totalPayable.toLocaleString()}`);

            return {
                success: true,
                awardsProcessed: extractionResult.data.length,
                documentId,
                summary: { totalGross, totalTax, totalPayable },
                validationResult
            };

        } catch (error) {
            console.error('❌ Awards Payslip processing failed:', error);

            try {
                await this.database.runQuery(
                    `UPDATE document_processing SET processing_status = ? WHERE session_id = ? AND file_name = ?`,
                    ['failed', this.currentSession.sessionId, fileName]
                );
            } catch (dbError) {
                console.error('❌ Failed to update document status:', dbError);
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * Validate Awards data
     */
    validateAwardsData(data) {
        const warnings = [];
        let validCount = 0;

        for (const award of data) {
            let isValid = true;

            if (!award.employee_no) {
                warnings.push(`Missing employee number for award ${award.award_type}`);
                isValid = false;
            }

            if (!award.award_type) {
                warnings.push(`Missing award type for ${award.employee_no}`);
                isValid = false;
            }

            if (!award.award_gross_amount || award.award_gross_amount <= 0) {
                warnings.push(`Invalid gross amount for ${award.employee_no}: ${award.award_gross_amount}`);
                isValid = false;
            }

            if (!award.award_payable_amount || award.award_payable_amount <= 0) {
                warnings.push(`Invalid payable amount for ${award.employee_no}: ${award.award_payable_amount}`);
                isValid = false;
            }

            if (isValid) {
                validCount++;
            }
        }

        return {
            isValid: warnings.length === 0,
            validCount,
            totalCount: data.length,
            warnings
        };
    }

    /**
     * Run Bank Adviser Perfect Extractor
     */
    async runBankAdviserExtractor(filePath, documentType, taxRate = null) {
        return new Promise((resolve) => {
            const pythonPath = path.join(__dirname, 'bank_adviser_perfect_extractor.py');
            const args = [documentType, filePath];

            if (taxRate) {
                args.push(taxRate.toString());
            }

            console.log(`🏦 Running Bank Adviser Perfect Extractor: ${pythonPath} ${args.join(' ')}`);

            const pythonProcess = spawn('python', [pythonPath, ...args]);
            let output = '';
            let errorOutput = '';

            pythonProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            pythonProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
                console.log(`🔍 Extractor stderr: ${data.toString()}`);
            });

            pythonProcess.on('close', (code) => {
                console.log(`🏦 Extractor process closed with code: ${code}`);

                if (code === 0) {
                    try {
                        // Extract JSON from output (filter out log messages)
                        const lines = output.split('\n');
                        let jsonStart = -1;
                        let jsonEnd = -1;

                        console.log(`🔍 Debug: Total lines: ${lines.length}`);
                        console.log(`🔍 Debug: First 3 lines:`, lines.slice(0, 3));
                        console.log(`🔍 Debug: Last 3 lines:`, lines.slice(-3));

                        // Find the start of JSON array (look for standalone '[' not part of debug messages)
                        for (let i = 0; i < lines.length; i++) {
                            const line = lines[i].trim();
                            // Look for a line that is exactly '[' or starts with '[' followed by whitespace or '{'
                            if (line === '[' || (line.startsWith('[') && (line.charAt(1) === ' ' || line.charAt(1) === '\n' || line.charAt(1) === '\r' || line.charAt(1) === '{'))) {
                                // Make sure this is not a debug message by checking if it contains '[BANK-ADVISER]'
                                if (!line.includes('[BANK-ADVISER]') && !line.includes('Debug:')) {
                                    jsonStart = i;
                                    console.log(`🔍 Debug: JSON start found at line ${i}: "${lines[i].substring(0, 50)}..."`);
                                    break;
                                }
                            }
                        }

                        // Find the end of JSON array (look for standalone ']')
                        for (let i = lines.length - 1; i >= 0; i--) {
                            const line = lines[i].trim();
                            if (line === ']' || line.endsWith(']')) {
                                // Make sure this is not part of a debug message
                                if (!line.includes('[BANK-ADVISER]') && !line.includes('Debug:')) {
                                    jsonEnd = i;
                                    console.log(`🔍 Debug: JSON end found at line ${i}: "...${lines[i].substring(-50)}"`);
                                    break;
                                }
                            }
                        }

                        if (jsonStart === -1 || jsonEnd === -1) {
                            console.log(`🔍 Debug: JSON boundaries not found. Start: ${jsonStart}, End: ${jsonEnd}`);
                            // Try alternative approach: look for complete JSON in the output
                            const cleanOutput = output.replace(/\[BANK-ADVISER\].*?\n/g, '').trim();
                            console.log(`🔍 Debug: Trying to parse cleaned output: ${cleanOutput.substring(0, 200)}...`);
                            const result = JSON.parse(cleanOutput);
                            console.log(`✅ Extraction successful (alternative method): ${result.length} items extracted`);
                            resolve({ success: true, data: result });
                            return;
                        }

                        // Extract and parse JSON
                        const jsonLines = lines.slice(jsonStart, jsonEnd + 1);
                        const jsonString = jsonLines.join('\n');
                        console.log(`🔍 Debug: Extracted JSON string length: ${jsonString.length}`);
                        console.log(`🔍 Debug: JSON string preview: ${jsonString.substring(0, 200)}...`);
                        const result = JSON.parse(jsonString);

                        console.log(`✅ Extraction successful: ${result.length} items extracted`);
                        resolve({ success: true, data: result });
                    } catch (error) {
                        console.error('❌ JSON parsing failed:', error);
                        console.error('Raw output:', output);
                        resolve({ success: false, error: 'Invalid JSON response from extractor' });
                    }
                } else {
                    console.error('❌ Extractor process failed:', errorOutput);
                    resolve({ success: false, error: errorOutput || 'Extraction process failed' });
                }
            });

            pythonProcess.on('error', (error) => {
                console.error('❌ Failed to start extractor process:', error);
                resolve({ success: false, error: `Failed to start extraction process: ${error.message}` });
            });
        });
    }

    /**
     * Feed data to Loan & Allowance Tracker
     */
    async feedToTracker(dataType, data) {
        const currentPeriod = `${this.currentSession.currentMonth} ${this.currentSession.currentYear}`;

        try {
            if (dataType === 'loans') {
                // Determine loan classification and save to appropriate table
                const loanClassification = data.loan_classification || 'IN-HOUSE';
                const tableName = loanClassification === 'EXTERNAL' ? 'external_loans' : 'in_house_loans';

                // Check for duplicates (same employee, loan type, period)
                const duplicateCheck = await this.database.getOne(
                    `SELECT id FROM ${tableName}
                     WHERE employee_no = ? AND loan_type = ? AND period_month = ? AND period_year = ?`,
                    [data.employee_no, data.loan_type, this.currentSession.currentMonth, this.currentSession.currentYear]
                );

                if (duplicateCheck) {
                    console.log(`⚠️ Duplicate loan detected: ${data.employee_no} - ${data.loan_type} for ${currentPeriod}`);
                    return; // Skip duplicate
                }

                // Generate remarks for IN HOUSE LOANS (Monitoring)
                let remarks = '';
                if (tableName === 'in_house_loans') {
                    remarks = 'Monitoring';
                }

                // Build query based on table type
                if (tableName === 'in_house_loans') {
                    await this.database.runQuery(
                        `INSERT INTO ${tableName}
                         (employee_no, employee_name, department, loan_type, loan_amount,
                          period_month, period_year, period_acquired, source_session, remarks)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            data.employee_no, data.employee_name, data.department,
                            data.loan_type, data.current_deduction || 0,
                            this.currentSession.currentMonth, this.currentSession.currentYear,
                            currentPeriod, this.currentSession.sessionId, remarks
                        ]
                    );
                } else {
                    await this.database.runQuery(
                        `INSERT INTO ${tableName}
                         (employee_no, employee_name, department, loan_type, loan_amount,
                          period_month, period_year, period_acquired, source_session)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            data.employee_no, data.employee_name, data.department,
                            data.loan_type, data.current_deduction || 0,
                            this.currentSession.currentMonth, this.currentSession.currentYear,
                            currentPeriod, this.currentSession.sessionId
                        ]
                    );
                }

                console.log(`✅ Fed loan data to ${tableName}: ${data.employee_no} - ${data.loan_type}`);

            } else if (dataType === 'allowances') {
                // Determine which tracker table to use
                const allowanceType = data.allowance_type.toUpperCase();

                if (allowanceType.includes('LEAVE')) {
                    await this.database.runQuery(
                        `INSERT INTO leave_claims
                         (employee_no, employee_name, department, allowance_type, payable_amount,
                          period_month, period_year, period_acquired, source_session)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            data.employee_no, data.employee_name, data.department,
                            data.allowance_type, data.allowance_payable_amount,
                            this.currentSession.currentMonth, this.currentSession.currentYear,
                            currentPeriod, this.currentSession.sessionId
                        ]
                    );
                } else if (allowanceType.includes('EDUCATIONAL')) {
                    await this.database.runQuery(
                        `INSERT INTO educational_subsidy
                         (employee_no, employee_name, department, allowance_type, payable_amount,
                          period_month, period_year, period_acquired, source_session)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            data.employee_no, data.employee_name, data.department,
                            data.allowance_type, data.allowance_payable_amount,
                            this.currentSession.currentMonth, this.currentSession.currentYear,
                            currentPeriod, this.currentSession.sessionId
                        ]
                    );
                }
            } else if (dataType === 'awards') {
                await this.database.runQuery(
                    `INSERT INTO long_service_awards
                     (employee_no, employee_name, department, award_type, payable_amount,
                      period_month, period_year, period_acquired, source_session)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        data.employee_no, data.employee_name, data.department,
                        data.award_type, data.award_payable_amount,
                        this.currentSession.currentMonth, this.currentSession.currentYear,
                        currentPeriod, this.currentSession.sessionId
                    ]
                );
            }
        } catch (error) {
            console.error('❌ Failed to feed data to tracker:', error);
        }
    }

    /**
     * Generate duplicate detection remarks (Flagging duplicate rules apply)
     */
    async generateDuplicateRemarks(tableName, data, currentPeriod) {
        try {
            // Check for historical duplicates (same employee, same type, different periods)
            const historicalDuplicates = await this.database.getQuery(
                `SELECT period_acquired FROM ${tableName}
                 WHERE employee_no = ? AND (allowance_type = ? OR award_type = ?) AND period_acquired != ?`,
                [
                    data.employee_no,
                    data.allowance_type || data.award_type,
                    data.allowance_type || data.award_type,
                    currentPeriod
                ]
            );

            if (historicalDuplicates.length > 0) {
                const previousPeriods = historicalDuplicates.map(d => d.period_acquired).join(', ');
                return `Previously received: ${previousPeriods}`;
            }

            // Check for same period duplicates (different types)
            const samePeriodCount = await this.database.getOne(
                `SELECT COUNT(*) as count FROM ${tableName}
                 WHERE employee_no = ? AND period_acquired = ?`,
                [data.employee_no, currentPeriod]
            );

            if (samePeriodCount && samePeriodCount.count > 0) {
                return `Multiple items this period`;
            }

            return ''; // No remarks needed
        } catch (error) {
            console.error('❌ Error generating duplicate remarks:', error);
            return '';
        }
    }

    /**
     * Load settings from database
     */
    async loadSettings() {
        try {
            const settings = await this.database.getQuery(
                'SELECT setting_key, setting_value FROM bank_adviser_settings'
            );

            for (const setting of settings) {
                if (setting.setting_key === 'default_allowance_tax_rate') {
                    this.taxRates.allowances = parseFloat(setting.setting_value);
                } else if (setting.setting_key === 'default_awards_tax_rate') {
                    this.taxRates.awards = parseFloat(setting.setting_value);
                } else if (setting.setting_key === 'educational_subsidy_tax_rate') {
                    this.taxRates.educational_subsidy = parseFloat(setting.setting_value);
                }
            }

            console.log('📊 Tax rates loaded:', this.taxRates);
        } catch (error) {
            console.error('❌ Failed to load settings:', error);
        }
    }

    /**
     * Generate Excel Bank Advice
     */
    async generateExcelBankAdvice(sessionId, generationType, selectedSections) {
        try {
            console.log(`📊 Generating Excel Bank Advice for session: ${sessionId}`);

            // Get session data
            const session = await this.database.getOne(
                'SELECT * FROM bank_adviser_sessions WHERE session_id = ?',
                [sessionId]
            );

            if (!session) {
                throw new Error('Session not found');
            }

            // Get final adjusted data
            const finalAdjustedData = await this.database.getQuery(
                'SELECT * FROM final_adjusted_data WHERE session_id = ? ORDER BY bank, employee_no',
                [sessionId]
            );

            // Get allowances data
            const allowancesData = await this.database.getQuery(
                'SELECT * FROM allowances_data WHERE session_id = ?',
                [sessionId]
            );

            // Get awards data
            const awardsData = await this.database.getQuery(
                'SELECT * FROM awards_data WHERE session_id = ?',
                [sessionId]
            );

            // Generate bank advice records
            const bankAdviceRecords = this.generateBankAdviceRecords(
                finalAdjustedData, allowancesData, awardsData
            );

            // Save bank advice records to database
            for (const record of bankAdviceRecords) {
                await this.database.runQuery(
                    `INSERT INTO bank_advice_records
                     (session_id, employee_no, employee_name, net_salary, leave_allowance,
                      awards, total_amount, bank, account_no, branch, section, remarks)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        sessionId, record.employee_no, record.employee_name,
                        record.net_salary, record.leave_allowance, record.awards,
                        record.total_amount, record.bank, record.account_no,
                        record.branch, record.section, record.remarks
                    ]
                );
            }

            // Generate Excel file
            const excelPath = await this.createExcelBankAdvice(session, bankAdviceRecords, generationType);

            console.log(`✅ Excel Bank Advice generated: ${excelPath}`);
            return {
                success: true,
                filePath: excelPath,
                recordsCount: bankAdviceRecords.length,
                preview: bankAdviceRecords.slice(0, 10)
            };

        } catch (error) {
            console.error('❌ Excel Bank Advice generation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate bank advice records by matching data
     */
    generateBankAdviceRecords(finalAdjustedData, allowancesData, awardsData) {
        const records = [];

        // Safety checks for data arrays
        const safeAllowancesData = allowancesData || [];
        const safeAwardsData = awardsData || [];
        const safeFinalAdjustedData = finalAdjustedData || [];

        console.log(`📊 generateBankAdviceRecords called with:`);
        console.log(`   📍 Final Adjusted: ${safeFinalAdjustedData.length} records`);
        console.log(`   📍 Allowances: ${safeAllowancesData.length} records`);
        console.log(`   📍 Awards: ${safeAwardsData.length} records`);

        // Create lookup maps for allowances and awards
        const allowancesMap = new Map();
        safeAllowancesData.forEach(allowance => {
            const key = allowance.employee_no;
            if (!allowancesMap.has(key)) {
                allowancesMap.set(key, 0);
            }
            allowancesMap.set(key, allowancesMap.get(key) + allowance.payable_amount);
        });

        const awardsMap = new Map();
        safeAwardsData.forEach(award => {
            const key = award.employee_no;
            if (!awardsMap.has(key)) {
                awardsMap.set(key, 0);
            }
            awardsMap.set(key, awardsMap.get(key) + award.payable_amount);
        });

        // Generate records for each employee
        safeFinalAdjustedData.forEach(employee => {
            const leaveAllowance = allowancesMap.get(employee.employee_no) || 0;
            const awards = awardsMap.get(employee.employee_no) || 0;
            const totalAmount = employee.net_pay + leaveAllowance + awards;

            records.push({
                employee_no: employee.employee_no,
                employee_name: employee.employee_name,
                net_salary: employee.net_pay,
                leave_allowance: leaveAllowance,
                awards: awards,
                total_amount: totalAmount,
                bank: employee.bank,
                account_no: employee.account_no,
                branch: employee.branch,
                section: employee.section,
                remarks: 'PRE-AUDITED'
            });
        });

        return records;
    }

    /**
     * Create Excel Bank Advice file - FIXED TO MATCH EXACT REQUIREMENTS
     */
    async createExcelBankAdvice(session, records, generationType) {
        const ExcelJS = require('exceljs');
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Bank Advice');

        // EXACT REQUIREMENTS: row 1-3, column B TO K merged and centered Bold, Calibri font size 21
        worksheet.mergeCells('B1:K1');
        worksheet.mergeCells('B2:K2');
        worksheet.mergeCells('B3:K3');

        // Row 1: THE CHURCH OF PENTECOST-HEADQUARTERS
        const headerCell1 = worksheet.getCell('B1');
        headerCell1.value = 'THE CHURCH OF PENTECOST-HEADQUARTERS';
        headerCell1.font = { name: 'Calibri', size: 21, bold: true };
        headerCell1.alignment = { horizontal: 'center', vertical: 'middle' };

        // Row 2: BANK ADVICE
        const headerCell2 = worksheet.getCell('B2');
        headerCell2.value = 'BANK ADVICE';
        headerCell2.font = { name: 'Calibri', size: 21, bold: true };
        headerCell2.alignment = { horizontal: 'center', vertical: 'middle' };

        // Row 3: Current Month and Year (e.g.: JUNE 2025)
        const headerCell3 = worksheet.getCell('B3');
        headerCell3.value = `${session.current_month.toUpperCase()} ${session.current_year}`;
        headerCell3.font = { name: 'Calibri', size: 21, bold: true };
        headerCell3.alignment = { horizontal: 'center', vertical: 'middle' };

        // EXACT REQUIREMENTS: Row 4 column headings
        // B=EMPLOYEE NO., C=EMPLOYEE NAME, D=NET SALARY E=LEAVE ALLOWANCE, F= AWARDS, G=TOTAL, H= BANK, I=ACCOUNT NO., J= BRANCH, K=REMARKS
        const columnHeaders = {
            'B': 'EMPLOYEE NO.',
            'C': 'EMPLOYEE NAME',
            'D': 'NET SALARY',
            'E': 'LEAVE ALLOWANCE',
            'F': 'AWARDS',
            'G': 'TOTAL',
            'H': 'BANK',
            'I': 'ACCOUNT NO.',
            'J': 'BRANCH',
            'K': 'REMARKS'
        };

        Object.entries(columnHeaders).forEach(([column, header]) => {
            const cell = worksheet.getCell(`${column}4`);
            cell.value = header;
            cell.font = { name: 'Calibri', bold: true };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
            };
        });

        // EXACT REQUIREMENTS: Row 5 starting is where extractible data must start to populate
        // Group records by bank for proper bank grouping with totals
        const bankGroups = {};
        records.forEach(record => {
            if (!bankGroups[record.bank]) {
                bankGroups[record.bank] = [];
            }
            bankGroups[record.bank].push(record);
        });

        let currentRow = 5;

        // Process each bank group
        Object.entries(bankGroups).forEach(([bankName, bankRecords], bankIndex) => {
            let bankNetSalaryTotal = 0;
            let bankLeaveAllowanceTotal = 0;
            let bankAwardsTotal = 0;
            let bankGrandTotal = 0;

            // Add employees for this bank
            bankRecords.forEach(record => {
                // EXACT REQUIREMENTS: B=EMPLOYEE NO., C=EMPLOYEE NAME, D=NET SALARY E=LEAVE ALLOWANCE, F= AWARDS, G=TOTAL, H= BANK, I=ACCOUNT NO., J= BRANCH, K=REMARKS
                worksheet.getCell(`B${currentRow}`).value = record.employee_no;
                worksheet.getCell(`C${currentRow}`).value = record.employee_name;
                worksheet.getCell(`D${currentRow}`).value = record.net_salary;
                worksheet.getCell(`E${currentRow}`).value = record.leave_allowance;
                worksheet.getCell(`F${currentRow}`).value = record.awards;

                // EXACT REQUIREMENTS: TOTAL column= will be equal to NET SALARY+LEAVE ALLOWANCE+AWARDS
                const totalAmount = record.net_salary + record.leave_allowance + record.awards;
                worksheet.getCell(`G${currentRow}`).value = totalAmount;

                worksheet.getCell(`H${currentRow}`).value = record.bank;
                worksheet.getCell(`I${currentRow}`).value = record.account_no;
                worksheet.getCell(`J${currentRow}`).value = record.branch;

                // EXACT REQUIREMENTS: REMARKS column: auto generate "PRE-AUDITED" (in Bold) and give the row from B to K a light green color
                worksheet.getCell(`K${currentRow}`).value = 'PRE-AUDITED';
                worksheet.getCell(`K${currentRow}`).font = { name: 'Calibri', bold: true };

                // Apply light green highlighting for PRE-AUDITED rows (B to K)
                for (let col = 'B'; col <= 'K'; col = String.fromCharCode(col.charCodeAt(0) + 1)) {
                    const cell = worksheet.getCell(`${col}${currentRow}`);
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFE8F5E8' } // Light green
                    };
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                }

                // Add to bank totals
                bankNetSalaryTotal += record.net_salary;
                bankLeaveAllowanceTotal += record.leave_allowance;
                bankAwardsTotal += record.awards;
                bankGrandTotal += totalAmount;

                currentRow++;
            });

            // EXACT REQUIREMENTS: After every Bank group lets place "TOTAL" in next available cell under EMPLOYEE NAME column, then sum the figures under NET SALARY,LEAVE ALLOWANCE, AWARDS in Bold
            worksheet.getCell(`C${currentRow}`).value = 'TOTAL';
            worksheet.getCell(`C${currentRow}`).font = { name: 'Calibri', bold: true };

            worksheet.getCell(`D${currentRow}`).value = bankNetSalaryTotal;
            worksheet.getCell(`D${currentRow}`).font = { name: 'Calibri', bold: true };

            worksheet.getCell(`E${currentRow}`).value = bankLeaveAllowanceTotal;
            worksheet.getCell(`E${currentRow}`).font = { name: 'Calibri', bold: true };

            worksheet.getCell(`F${currentRow}`).value = bankAwardsTotal;
            worksheet.getCell(`F${currentRow}`).font = { name: 'Calibri', bold: true };

            worksheet.getCell(`G${currentRow}`).value = bankGrandTotal;
            worksheet.getCell(`G${currentRow}`).font = { name: 'Calibri', bold: true };

            // Add borders to total row
            for (let col = 'B'; col <= 'K'; col = String.fromCharCode(col.charCodeAt(0) + 1)) {
                const cell = worksheet.getCell(`${col}${currentRow}`);
                cell.border = {
                    top: { style: 'thick' },
                    left: { style: 'thin' },
                    bottom: { style: 'thick' },
                    right: { style: 'thin' }
                };
            }

            currentRow += 2; // Add space between bank groups
        });

        // Set proper column widths for B-K columns
        worksheet.getColumn('B').width = 15; // EMPLOYEE NO.
        worksheet.getColumn('C').width = 25; // EMPLOYEE NAME
        worksheet.getColumn('D').width = 15; // NET SALARY
        worksheet.getColumn('E').width = 18; // LEAVE ALLOWANCE
        worksheet.getColumn('F').width = 12; // AWARDS
        worksheet.getColumn('G').width = 15; // TOTAL
        worksheet.getColumn('H').width = 20; // BANK
        worksheet.getColumn('I').width = 18; // ACCOUNT NO.
        worksheet.getColumn('J').width = 15; // BRANCH
        worksheet.getColumn('K').width = 15; // REMARKS

        // Save file
        const reportsDir = path.join(__dirname, '..', 'reports', 'Bank Adviser Reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        const fileName = `Bank_Advice_${session.current_month}_${session.current_year}_${Date.now()}.xlsx`;
        const filePath = path.join(reportsDir, fileName);

        await workbook.xlsx.writeFile(filePath);

        // Register Excel file in Report Manager
        await this.registerExcelInReportManager(filePath, session, records);

        console.log(`✅ Excel Bank Advice created: ${filePath}`);
        return filePath;
    }

    /**
     * Get tracker data
     */
    async getTrackerData(category, searchTerm = '', periodFilter = '') {
        try {
            console.log(`📊 Getting tracker data for category: ${category}`);

            let query = `SELECT * FROM ${category}`;
            let params = [];
            let whereConditions = [];

            // Add search filter
            if (searchTerm) {
                whereConditions.push('(employee_no LIKE ? OR employee_name LIKE ?)');
                params.push(`%${searchTerm}%`, `%${searchTerm}%`);
            }

            // Add period filter
            if (periodFilter) {
                whereConditions.push('period_year = ?');
                params.push(parseInt(periodFilter));
            }

            if (whereConditions.length > 0) {
                query += ' WHERE ' + whereConditions.join(' AND ');
            }

            // Different ordering for duplicate_checker table
            if (category === 'duplicate_checker') {
                query += ' ORDER BY detection_date DESC, employee_no';
            } else {
                query += ' ORDER BY period_year DESC, period_month DESC, employee_no';
            }

            const data = await this.database.getQuery(query, params);

            // Generate summary
            const summary = this.generateTrackerSummary(data);

            return {
                success: true,
                data,
                summary
            };

        } catch (error) {
            console.error('❌ Get tracker data failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate tracker summary
     */
    generateTrackerSummary(data) {
        const totalRecords = data.length;
        const uniqueEmployees = new Set(data.map(item => item.employee_no)).size;
        const totalAmount = data.reduce((sum, item) => {
            return sum + (item.loan_amount || item.payable_amount || 0);
        }, 0);

        const currentPeriod = data.length > 0 ?
            `${data[0].period_month} ${data[0].period_year}` : 'N/A';

        return {
            totalRecords,
            uniqueEmployees,
            totalAmount,
            currentPeriod
        };
    }

    /**
     * Export tracker data to Excel
     */
    async exportTrackerData(category, searchTerm = '', periodFilter = '') {
        try {
            console.log(`📊 Exporting tracker data for category: ${category}`);

            const result = await this.getTrackerData(category, searchTerm, periodFilter);
            if (!result.success) {
                throw new Error(result.error);
            }

            const ExcelJS = require('exceljs');
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet(category.replace('_', ' ').toUpperCase());

            // Set up headers based on category
            const headerMappings = {
                'in_house_loans': ['Employee No.', 'Employee Name', 'Department', 'Loan Type', 'Loan Amount', 'Period Acquired', 'Remarks'],
                'external_loans': ['Employee No.', 'Employee Name', 'Department', 'Loan Type', 'Loan Amount', 'Period Acquired'],
                'leave_claims': ['Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period', 'Remarks'],
                'educational_subsidy': ['Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period', 'Remarks'],
                'long_service_awards': ['Employee No.', 'Employee Name', 'Department', 'Award Type', 'Payable Amount', 'Period', 'Remarks'],
                'motor_vehicle_maintenance': ['Employee No.', 'Employee Name', 'Department', 'Allowance Type', 'Payable Amount', 'Period', 'Remarks']
            };

            const headers = headerMappings[category] || [];

            // Add headers
            headers.forEach((header, index) => {
                const cell = worksheet.getCell(1, index + 1);
                cell.value = header;
                cell.font = { bold: true };
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFE8F5E8' }
                };
            });

            // Add data
            result.data.forEach((row, rowIndex) => {
                worksheet.getCell(rowIndex + 2, 1).value = row.employee_no;
                worksheet.getCell(rowIndex + 2, 2).value = row.employee_name;
                worksheet.getCell(rowIndex + 2, 3).value = row.department;
                worksheet.getCell(rowIndex + 2, 4).value = row.loan_type || row.allowance_type || row.award_type;
                worksheet.getCell(rowIndex + 2, 5).value = row.loan_amount || row.payable_amount;
                worksheet.getCell(rowIndex + 2, 6).value = row.period_acquired;
                if (headers.length > 6) {
                    worksheet.getCell(rowIndex + 2, 7).value = row.remarks || '';
                }
            });

            // Auto-fit columns
            worksheet.columns.forEach(column => {
                column.width = 15;
            });

            // Save file
            const reportsDir = path.join(__dirname, '..', 'reports', 'Bank Adviser Reports');
            if (!fs.existsSync(reportsDir)) {
                fs.mkdirSync(reportsDir, { recursive: true });
            }

            const fileName = `${category}_export_${Date.now()}.xlsx`;
            const filePath = path.join(reportsDir, fileName);

            await workbook.xlsx.writeFile(filePath);

            return {
                success: true,
                filePath,
                recordsExported: result.data.length
            };

        } catch (error) {
            console.error('❌ Export tracker data failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate CORRECT Excel Bank Advice - FIXED TO MATCH EXACT REQUIREMENTS
     */
    async generateCorrectBankAdviceExcel(sessionId, outputPath, generationType = 'composite') {
        try {
            console.log(`📊 Generating CORRECT Bank Advice Excel for session: ${sessionId}`);

            // Get session data
            const session = await this.database.getOne(
                'SELECT * FROM bank_adviser_sessions WHERE session_id = ?',
                [sessionId]
            );

            if (!session) {
                throw new Error('Session not found');
            }

            // Get final adjusted data (base data)
            const finalAdjustedData = await this.database.getQuery(
                'SELECT * FROM final_adjusted_data WHERE session_id = ? ORDER BY bank, employee_no',
                [sessionId]
            );

            // Get allowances data
            const allowancesData = await this.database.getQuery(
                'SELECT * FROM allowances_data WHERE session_id = ?',
                [sessionId]
            );

            // Get awards data
            const awardsData = await this.database.getQuery(
                'SELECT * FROM awards_data WHERE session_id = ?',
                [sessionId]
            );

            // Debug: Check data arrays
            console.log(`📊 Data arrays check:`);
            console.log(`   📍 Final Adjusted: ${finalAdjustedData ? finalAdjustedData.length : 'undefined'} records`);
            console.log(`   📍 Allowances: ${allowancesData ? allowancesData.length : 'undefined'} records`);
            console.log(`   📍 Awards: ${awardsData ? awardsData.length : 'undefined'} records`);

            // Generate bank advice records using FIXED algorithm
            const bankAdviceRecords = this.generateBankAdviceRecords(finalAdjustedData, allowancesData, awardsData);

            // Generate Excel file using FIXED Excel generator
            const excelPath = await this.createExcelBankAdvice(session, bankAdviceRecords, generationType);

            console.log(`✅ CORRECT Excel Bank Advice generated: ${excelPath}`);
            return {
                success: true,
                filePath: excelPath,
                recordsCount: bankAdviceRecords.length,
                preview: bankAdviceRecords.slice(0, 5)
            };

        } catch (error) {
            console.error('❌ CORRECT Excel Bank Advice generation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate Bank Advice Excel with real database data - OLD METHOD (DEPRECATED)
     */
    async generateBankAdviceExcel(adviceType, outputPath, generationType = 'composite', selectedSections = []) {
        if (!this.currentSession) {
            throw new Error('No active Bank Adviser session');
        }

        console.log(`📊 Generating ${adviceType} Bank Advice Excel: ${generationType}`);
        console.log(`   📍 Session: ${this.currentSession.sessionId}`);
        console.log(`   🔍 Selected sections: ${selectedSections.length > 0 ? selectedSections.join(', ') : 'All'}`);

        try {
            // Create session data file for Python script
            const sessionDataPath = path.join(__dirname, '..', 'temp', `session_${this.currentSession.sessionId}.json`);

            // Ensure temp directory exists
            const tempDir = path.dirname(sessionDataPath);
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // Write session data to temporary file
            const sessionData = {
                session_id: this.currentSession.sessionId,
                session_name: this.currentSession.sessionName,
                current_month: this.currentSession.currentMonth,
                current_year: this.currentSession.currentYear,
                selected_sections: selectedSections,
                generation_type: generationType
            };

            fs.writeFileSync(sessionDataPath, JSON.stringify(sessionData, null, 2));

            const pythonPath = path.join(__dirname, 'bank_advice_excel_generator.py');
            const args = [adviceType, outputPath, generationType, sessionDataPath];

            console.log(`🏦 Running Excel Generator with session data: ${pythonPath} ${args.join(' ')}`);

            const pythonProcess = spawn('python', [pythonPath, ...args]);
            let output = '';
            let errorOutput = '';

            pythonProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            pythonProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
                console.log(`🔍 Excel Generator stderr: ${data.toString()}`);
            });

            return new Promise((resolve) => {
                pythonProcess.on('close', (code) => {
                    console.log(`🏦 Excel Generator process closed with code: ${code}`);

                    // Clean up temporary session file
                    try {
                        if (fs.existsSync(sessionDataPath)) {
                            fs.unlinkSync(sessionDataPath);
                        }
                    } catch (cleanupError) {
                        console.warn('⚠️ Failed to clean up session file:', cleanupError);
                    }

                    if (code === 0) {
                        try {
                            // Extract JSON from output (remove debug messages)
                            const lines = output.split('\n');
                            let jsonStart = -1;
                            let jsonEnd = -1;

                            // Find JSON boundaries
                            for (let i = 0; i < lines.length; i++) {
                                const line = lines[i].trim();
                                if (line.startsWith('{') && jsonStart === -1) {
                                    jsonStart = i;
                                }
                                if (line.endsWith('}') && jsonStart !== -1) {
                                    jsonEnd = i;
                                    break;
                                }
                            }

                            if (jsonStart !== -1 && jsonEnd !== -1) {
                                const jsonLines = lines.slice(jsonStart, jsonEnd + 1);
                                const jsonString = jsonLines.join('\n');
                                const result = JSON.parse(jsonString);
                                console.log(`✅ Excel generation successful: ${result.total_employees || 0} employees, ${result.banks_count || 0} banks`);
                                resolve({ success: true, data: result });
                            } else {
                                console.error('❌ No valid JSON found in output');
                                console.error('Raw output:', output);
                                resolve({ success: false, error: 'No valid JSON response from Excel generator' });
                            }
                        } catch (error) {
                            console.error('❌ JSON parsing failed:', error);
                            console.error('Raw output:', output);
                            resolve({ success: false, error: 'Invalid JSON response from Excel generator' });
                        }
                    } else {
                        console.error('❌ Excel Generator process failed:', errorOutput);
                        resolve({ success: false, error: errorOutput || 'Excel generation process failed' });
                    }
                });

                pythonProcess.on('error', (error) => {
                    console.error('❌ Failed to start Excel generator process:', error);
                    resolve({ success: false, error: `Failed to start Excel generation process: ${error.message}` });
                });
            });

        } catch (error) {
            console.error('❌ Bank Advice Excel generation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate Final Adjusted Bank Advice Excel
     */
    async generateFinalAdjustedBankAdvice(outputPath, selectedSections = [], generationType = 'composite') {
        return await this.generateBankAdviceExcel('final_adjusted', outputPath, generationType, selectedSections);
    }

    /**
     * Generate Allowances Bank Advice Excel
     */
    async generateAllowancesBankAdvice(outputPath, generationType = 'composite') {
        return await this.generateBankAdviceExcel('allowances', outputPath, generationType);
    }

    /**
     * Generate Awards Bank Advice Excel
     */
    async generateAwardsBankAdvice(outputPath, generationType = 'composite') {
        return await this.generateBankAdviceExcel('awards', outputPath, generationType);
    }

    /**
     * Generate processing report
     */
    async generateProcessingReport(documentType, reportData) {
        try {
            const reportName = `Bank_Adviser_${documentType}_${this.currentSession.sessionName}_${new Date().toISOString().split('T')[0]}`;
            const reportContent = {
                sessionId: this.currentSession.sessionId,
                sessionName: this.currentSession.sessionName,
                documentType,
                processingDate: new Date().toISOString(),
                ...reportData
            };

            // Save to unified database with correct schema
            const UnifiedDatabase = require('./unified_database');
            const unifiedDb = new UnifiedDatabase();

            // Wait for database initialization
            let retries = 0;
            while (!unifiedDb.isInitialized && retries < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retries++;
            }

            if (unifiedDb.isInitialized) {
                const reportId = `bank_advice_${Date.now()}`;
                await unifiedDb.runQuery(
                    `INSERT INTO reports (report_id, report_type, report_category, title, description, file_paths, metadata, created_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        reportId,
                        'Bank Advice Processing',
                        'Bank Adviser',
                        reportName,
                        `Bank Adviser processing report for ${reportData.sessionName}`,
                        JSON.stringify({}),
                        JSON.stringify(reportContent),
                        new Date().toISOString()
                    ]
                );

                await unifiedDb.close();
                console.log(`✅ Processing report saved to unified database: ${reportName}`);
            } else {
                console.warn('⚠️ Unified database not ready, skipping report save');
            }

            console.log(`✅ Processing report generated: ${reportName}`);
            return { success: true, reportName };

        } catch (error) {
            console.error('❌ Failed to generate processing report:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Register Excel file in Report Manager - FIXED VERSION
     */
    async registerExcelInReportManager(filePath, session, records) {
        try {
            console.log(`📊 Registering Excel in Report Manager: ${filePath}`);

            const fs = require('fs');
            const path = require('path');
            const sqlite3 = require('sqlite3').verbose();

            // Use direct SQLite connection to the main database
            const dbPath = path.join(__dirname, '..', 'data', 'templar_payroll_auditor.db');

            if (!fs.existsSync(dbPath)) {
                console.error(`❌ Database not found: ${dbPath}`);
                return;
            }

            const db = new sqlite3.Database(dbPath);

            const reportId = `bank_advice_excel_${Date.now()}`;
            const fileName = path.basename(filePath);
            const fileStats = fs.statSync(filePath);

            // Insert report into database
            await new Promise((resolve, reject) => {
                db.run(
                    `INSERT INTO reports (report_id, report_type, report_category, title, description, file_paths, metadata, created_at, file_size)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                    [
                        reportId,
                        'Excel Bank Advice',
                        'Bank Adviser',
                        `Bank Advice Excel - ${session.session_name}`,
                        `Excel Bank Advice for ${session.session_name} with ${records.length} employees`,
                        JSON.stringify({ excel: filePath }),
                        JSON.stringify({
                            sessionId: session.session_id,
                            sessionName: session.session_name,
                            recordsCount: records.length,
                            fileName: fileName,
                            generationType: 'composite'
                        }),
                        new Date().toISOString(),
                        fileStats.size
                    ],
                    function(err) {
                        if (err) {
                            console.error('❌ Database insert error:', err);
                            reject(err);
                        } else {
                            console.log(`✅ Excel file registered in Report Manager: ${fileName} (ID: ${reportId})`);
                            resolve();
                        }
                    }
                );
            });

            db.close();

        } catch (error) {
            console.error('❌ Failed to register Excel in Report Manager:', error);
        }
    }

    /**
     * Close Bank Adviser Manager
     */
    async close() {
        if (this.database) {
            await this.database.close();
        }
        console.log('🏦 Bank Adviser Manager closed');
    }
}

module.exports = BankAdviserManager;
